/**
 * 阴影系统模块
 * 导出所有阴影相关的类和接口
 */

// 导出基础阴影系统
export { ShadowSystem } from './ShadowSystem';

// 导出高级阴影系统
export { AdvancedShadowSystem } from './AdvancedShadowSystem';
export type { 
  AdvancedShadowConfig,
  CSMConfig,
  SoftShadowConfig,
  VolumetricShadowConfig
} from './AdvancedShadowSystem';
export { ShadowQuality, SoftShadowTechnique } from './AdvancedShadowSystem';

// 导出CSM系统
export { CSM, CSMModes } from './CSM';
export type { CSMParams } from './CSM';

// 导出视锥体
export { Frustum } from './Frustum';

// 导出着色器
export { Shader } from './Shader';
