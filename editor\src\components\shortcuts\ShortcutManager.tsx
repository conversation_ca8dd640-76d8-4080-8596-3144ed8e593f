/**
 * 快捷键管理组件
 * 提供快捷键的查看、编辑、冲突检测和配置管理功能
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Modal, Table, Input, Button, Space, Tag, Alert, Tabs, 
  Tooltip, Popconfirm, message, Switch, Select, Divider
} from 'antd';
import {
  SearchOutlined, EditOutlined, DeleteOutlined, ReloadOutlined,
  SettingOutlined, ExportOutlined, ImportOutlined, WarningOutlined,
  KeyboardOutlined, SaveOutlined, CloseOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { 
  ShortcutService, 
  ShortcutDefinition, 
  ShortcutConflict, 
  ShortcutCategory,
  ShortcutContext 
} from '../../services/ShortcutService';
import ShortcutRecorder from './ShortcutRecorder';
import './ShortcutManager.less';

const { Search } = Input;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 组件属性接口
 */
interface ShortcutManagerProps {
  /** 是否可见 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
}

/**
 * 快捷键管理组件
 */
export const ShortcutManager: React.FC<ShortcutManagerProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const shortcutService = useMemo(() => ShortcutService.getInstance(), []);

  // 状态
  const [shortcuts, setShortcuts] = useState<ShortcutDefinition[]>([]);
  const [conflicts, setConflicts] = useState<ShortcutConflict[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [editingShortcut, setEditingShortcut] = useState<ShortcutDefinition | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  /**
   * 加载快捷键数据
   */
  const loadShortcuts = useCallback(() => {
    const allShortcuts = shortcutService.getAllShortcuts();
    const allConflicts = shortcutService.getAllConflicts();
    
    setShortcuts(allShortcuts);
    setConflicts(allConflicts);
  }, [shortcutService]);

  /**
   * 过滤快捷键
   */
  const filteredShortcuts = useMemo(() => {
    let filtered = shortcuts;

    // 按搜索词过滤
    if (searchTerm) {
      filtered = shortcutService.searchShortcuts(searchTerm);
    }

    // 按分类过滤
    if (selectedCategory) {
      filtered = filtered.filter(shortcut => shortcut.category === selectedCategory);
    }

    return filtered;
  }, [shortcuts, searchTerm, selectedCategory, shortcutService]);

  /**
   * 获取分类列表
   */
  const categories = useMemo(() => {
    return Object.values(ShortcutCategory);
  }, []);

  /**
   * 处理快捷键编辑
   */
  const handleEditShortcut = useCallback((shortcut: ShortcutDefinition) => {
    setEditingShortcut({ ...shortcut });
  }, []);

  /**
   * 处理快捷键保存
   */
  const handleSaveShortcut = useCallback((updatedShortcut: ShortcutDefinition) => {
    shortcutService.updateShortcut(updatedShortcut.id, updatedShortcut);
    setEditingShortcut(null);
    setHasChanges(true);
    loadShortcuts();
    message.success(t('shortcuts.saveSuccess'));
  }, [shortcutService, loadShortcuts, t]);

  /**
   * 处理快捷键删除
   */
  const handleDeleteShortcut = useCallback((id: string) => {
    shortcutService.unregisterShortcut(id);
    setHasChanges(true);
    loadShortcuts();
    message.success(t('shortcuts.deleteSuccess'));
  }, [shortcutService, loadShortcuts, t]);

  /**
   * 处理快捷键启用/禁用
   */
  const handleToggleShortcut = useCallback((id: string, enabled: boolean) => {
    shortcutService.updateShortcut(id, { enabled });
    setHasChanges(true);
    loadShortcuts();
  }, [shortcutService, loadShortcuts]);

  /**
   * 重置为默认配置
   */
  const handleResetToDefaults = useCallback(() => {
    shortcutService.resetToDefaults();
    setHasChanges(false);
    loadShortcuts();
    message.success(t('shortcuts.resetSuccess'));
  }, [shortcutService, loadShortcuts, t]);

  /**
   * 导出配置
   */
  const handleExportConfig = useCallback(() => {
    const config = shortcutService.exportConfig();
    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'shortcuts-config.json';
    a.click();
    URL.revokeObjectURL(url);
    message.success(t('shortcuts.exportSuccess'));
  }, [shortcutService, t]);

  /**
   * 导入配置
   */
  const handleImportConfig = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        shortcutService.importConfig(config);
        setHasChanges(false);
        loadShortcuts();
        message.success(t('shortcuts.importSuccess'));
      } catch (error) {
        message.error(t('shortcuts.importError'));
      }
    };
    reader.readAsText(file);
  }, [shortcutService, loadShortcuts, t]);

  /**
   * 表格列定义
   */
  const columns = [
    {
      title: t('shortcuts.description'),
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string, record: ShortcutDefinition) => (
        <div>
          <div>{text}</div>
          <div style={{ fontSize: 12, color: '#999' }}>{record.id}</div>
        </div>
      )
    },
    {
      title: t('shortcuts.combination'),
      dataIndex: 'combination',
      key: 'combination',
      width: 150,
      render: (combination: string) => (
        <Tag color="blue">{shortcutService.getDisplayText(combination)}</Tag>
      )
    },
    {
      title: t('shortcuts.category'),
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => (
        <Tag>{category}</Tag>
      )
    },
    {
      title: t('shortcuts.context'),
      dataIndex: 'context',
      key: 'context',
      width: 100,
      render: (context: string, record: ShortcutDefinition) => (
        <Tag color={record.global ? 'green' : 'default'}>
          {record.global ? t('shortcuts.global') : context || t('shortcuts.default')}
        </Tag>
      )
    },
    {
      title: t('shortcuts.enabled'),
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled: boolean, record: ShortcutDefinition) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleToggleShortcut(record.id, checked)}
          size="small"
        />
      )
    },
    {
      title: t('shortcuts.actions'),
      key: 'actions',
      width: 120,
      render: (_, record: ShortcutDefinition) => (
        <Space size="small">
          <Tooltip title={t('shortcuts.edit')}>
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              disabled={!record.customizable}
              onClick={() => handleEditShortcut(record)}
            />
          </Tooltip>
          <Tooltip title={t('shortcuts.delete')}>
            <Popconfirm
              title={t('shortcuts.deleteConfirm')}
              onConfirm={() => handleDeleteShortcut(record.id)}
              disabled={!record.customizable}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
                disabled={!record.customizable}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  /**
   * 渲染冲突警告
   */
  const renderConflictWarnings = () => {
    if (conflicts.length === 0) return null;

    return (
      <Alert
        message={t('shortcuts.conflictsDetected', { count: conflicts.length })}
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
        description={
          <div>
            {conflicts.map((conflict, index) => (
              <div key={index} style={{ marginBottom: 8 }}>
                <strong>{shortcutService.getDisplayText(conflict.combination)}</strong>:
                {conflict.conflicts.map(shortcut => (
                  <Tag key={shortcut.id} style={{ marginLeft: 8 }}>
                    {shortcut.description}
                  </Tag>
                ))}
              </div>
            ))}
          </div>
        }
      />
    );
  };

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div className="shortcut-manager-toolbar">
      <Space>
        <Search
          placeholder={t('shortcuts.searchPlaceholder')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{ width: 200 }}
          allowClear
        />
        
        <Select
          placeholder={t('shortcuts.selectCategory')}
          value={selectedCategory}
          onChange={setSelectedCategory}
          style={{ width: 150 }}
          allowClear
        >
          {categories.map(category => (
            <Option key={category} value={category}>
              {category}
            </Option>
          ))}
        </Select>
        
        <Divider type="vertical" />
        
        <Tooltip title={t('shortcuts.export')}>
          <Button icon={<ExportOutlined />} onClick={handleExportConfig} />
        </Tooltip>
        
        <Tooltip title={t('shortcuts.import')}>
          <Button
            icon={<ImportOutlined />}
            onClick={() => {
              const input = document.createElement('input');
              input.type = 'file';
              input.accept = '.json';
              input.onchange = (e) => {
                const file = (e.target as HTMLInputElement).files?.[0];
                if (file) {
                  handleImportConfig(file);
                }
              };
              input.click();
            }}
          />
        </Tooltip>
        
        <Tooltip title={t('shortcuts.reset')}>
          <Popconfirm
            title={t('shortcuts.resetConfirm')}
            onConfirm={handleResetToDefaults}
          >
            <Button icon={<ReloadOutlined />} />
          </Popconfirm>
        </Tooltip>
      </Space>
    </div>
  );

  // 初始化数据
  useEffect(() => {
    if (visible) {
      loadShortcuts();
    }
  }, [visible, loadShortcuts]);

  return (
    <Modal
      title={
        <Space>
          <KeyboardOutlined />
          {t('shortcuts.title')}
          {hasChanges && <Tag color="orange">{t('shortcuts.hasChanges')}</Tag>}
        </Space>
      }
      visible={visible}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="shortcut-manager"
    >
      <Tabs defaultActiveKey="shortcuts">
        <TabPane tab={t('shortcuts.shortcuts')} key="shortcuts">
          {renderToolbar()}
          {renderConflictWarnings()}
          
          <Table
            columns={columns}
            dataSource={filteredShortcuts}
            rowKey="id"
            size="small"
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => t('shortcuts.total', { total })
            }}
          />
        </TabPane>
        
        <TabPane tab={t('shortcuts.conflicts')} key="conflicts">
          {conflicts.length === 0 ? (
            <div style={{ textAlign: 'center', padding: 50 }}>
              <div style={{ fontSize: 16, color: '#52c41a', marginBottom: 8 }}>
                ✓ {t('shortcuts.noConflicts')}
              </div>
              <div style={{ color: '#999' }}>
                {t('shortcuts.noConflictsDesc')}
              </div>
            </div>
          ) : (
            <div>
              {conflicts.map((conflict, index) => (
                <Alert
                  key={index}
                  message={shortcutService.getDisplayText(conflict.combination)}
                  type="warning"
                  style={{ marginBottom: 16 }}
                  description={
                    <div>
                      {conflict.conflicts.map(shortcut => (
                        <div key={shortcut.id} style={{ marginBottom: 4 }}>
                          <strong>{shortcut.description}</strong> ({shortcut.category})
                        </div>
                      ))}
                    </div>
                  }
                />
              ))}
            </div>
          )}
        </TabPane>
      </Tabs>

      {/* 快捷键编辑器 */}
      {editingShortcut && (
        <ShortcutRecorder
          visible={!!editingShortcut}
          shortcut={editingShortcut}
          onSave={handleSaveShortcut}
          onCancel={() => setEditingShortcut(null)}
        />
      )}
    </Modal>
  );
};

export default ShortcutManager;
