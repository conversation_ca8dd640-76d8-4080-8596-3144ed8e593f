/**
 * 优化的遮挡剔除系统
 * 实现高性能的视锥体剔除、遮挡查询和智能剔除算法
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { OcclusionCullingSystem, OcclusionCullingSystemOptions, OcclusionCullingAlgorithm, OcclusionCullingState } from './OcclusionCullingSystem';
import { CullableComponent } from './CullableComponent';
import { Octree } from './spatial/Octree';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor, PerformanceMetricType } from '../../utils/PerformanceMonitor';

/**
 * 优化的遮挡剔除系统事件类型
 */
export enum OptimizedOcclusionCullingEventType {
  /** 剔除开始 */
  CULLING_START = 'culling_start',
  /** 剔除完成 */
  CULLING_COMPLETE = 'culling_complete',
  /** 视锥体剔除完成 */
  FRUSTUM_CULLING_COMPLETE = 'frustum_culling_complete',
  /** 遮挡剔除完成 */
  OCCLUSION_CULLING_COMPLETE = 'occlusion_culling_complete',
  /** 性能阈值触发 */
  PERFORMANCE_THRESHOLD_TRIGGERED = 'performance_threshold_triggered'
}

/**
 * 剔除策略类型
 */
export enum CullingStrategy {
  /** 激进剔除 */
  AGGRESSIVE = 'aggressive',
  /** 平衡剔除 */
  BALANCED = 'balanced',
  /** 保守剔除 */
  CONSERVATIVE = 'conservative',
  /** 自适应剔除 */
  ADAPTIVE = 'adaptive'
}

/**
 * 优化的遮挡剔除系统配置接口
 */
export interface OptimizedOcclusionCullingSystemOptions extends OcclusionCullingSystemOptions {
  /** 剔除策略 */
  cullingStrategy?: CullingStrategy;
  /** 是否启用空间分割 */
  useSpatialPartitioning?: boolean;
  /** 是否启用多线程 */
  useMultiThreading?: boolean;
  /** 是否启用预测性剔除 */
  usePredictiveCulling?: boolean;
  /** 是否启用层次剔除 */
  useHierarchicalCulling?: boolean;
  /** 视锥体扩展系数 */
  frustumExpansionFactor?: number;
  /** 遮挡查询阈值 */
  occlusionQueryThreshold?: number;
  /** 性能目标FPS */
  targetFPS?: number;
  /** 批处理大小 */
  batchSize?: number;
  /** 是否启用时间切片 */
  useTimeSlicing?: boolean;
  /** 时间切片预算（毫秒） */
  timeSliceBudget?: number;
}

/**
 * 剔除统计信息
 */
export interface CullingStats {
  /** 总实体数 */
  totalEntities: number;
  /** 视锥体剔除数 */
  frustumCulled: number;
  /** 遮挡剔除数 */
  occlusionCulled: number;
  /** 可见实体数 */
  visibleEntities: number;
  /** 剔除率 */
  cullingRate: number;
  /** 剔除时间（毫秒） */
  cullingTime: number;
  /** 视锥体剔除时间（毫秒） */
  frustumCullingTime: number;
  /** 遮挡剔除时间（毫秒） */
  occlusionCullingTime: number;
}

/**
 * 优化的遮挡剔除系统
 */
export class OptimizedOcclusionCullingSystem extends OcclusionCullingSystem {
  /** 剔除策略 */
  private cullingStrategy: CullingStrategy;
  /** 是否启用空间分割 */
  private useSpatialPartitioning: boolean;
  /** 是否启用多线程 */
  private useMultiThreading: boolean;
  /** 是否启用预测性剔除 */
  private usePredictiveCulling: boolean;
  /** 是否启用层次剔除 */
  private useHierarchicalCulling: boolean;
  /** 视锥体扩展系数 */
  private frustumExpansionFactor: number;
  /** 遮挡查询阈值 */
  private occlusionQueryThreshold: number;
  /** 性能目标FPS */
  private targetFPS: number;
  /** 批处理大小 */
  private batchSize: number;
  /** 是否启用时间切片 */
  private useTimeSlicing: boolean;
  /** 时间切片预算（毫秒） */
  private timeSliceBudget: number;

  /** 空间分割结构（八叉树） */
  private octree: Octree | null = null;
  /** 视锥体 */
  private frustum: THREE.Frustum = new THREE.Frustum();
  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();
  /** 临时向量 */
  private tempVector: THREE.Vector3 = new THREE.Vector3();
  /** 临时包围盒 */
  private tempBox: THREE.Box3 = new THREE.Box3();
  /** 临时包围球 */
  private tempSphere: THREE.Sphere = new THREE.Sphere();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();

  /** 剔除统计信息 */
  private cullingStats: CullingStats = {
    totalEntities: 0,
    frustumCulled: 0,
    occlusionCulled: 0,
    visibleEntities: 0,
    cullingRate: 0,
    cullingTime: 0,
    frustumCullingTime: 0,
    occlusionCullingTime: 0
  };

  /** 视锥体剔除的实体集合 */
  private frustumCulledEntities: Set<Entity> = new Set();
  /** 遮挡剔除的实体集合 */
  private occlusionCulledEntities: Set<Entity> = new Set();
  /** 可见实体集合 */
  private visibleEntities: Set<Entity> = new Set();

  /** 批处理队列 */
  private batchQueue: Entity[] = [];
  /** 当前批处理索引 */
  private currentBatchIndex: number = 0;

  /** 性能历史记录 */
  private performanceHistory: number[] = [];
  /** 上次剔除时间 */
  private lastCullingTime: number = 0;

  /**
   * 创建优化的遮挡剔除系统
   * @param options 系统配置
   */
  constructor(options: OptimizedOcclusionCullingSystemOptions = {}) {
    super(options);

    // 设置优化选项
    this.cullingStrategy = options.cullingStrategy || CullingStrategy.BALANCED;
    this.useSpatialPartitioning = options.useSpatialPartitioning !== undefined ? options.useSpatialPartitioning : true;
    this.useMultiThreading = options.useMultiThreading !== undefined ? options.useMultiThreading : false;
    this.usePredictiveCulling = options.usePredictiveCulling !== undefined ? options.usePredictiveCulling : true;
    this.useHierarchicalCulling = options.useHierarchicalCulling !== undefined ? options.useHierarchicalCulling : true;
    this.frustumExpansionFactor = options.frustumExpansionFactor || 1.1;
    this.occlusionQueryThreshold = options.occlusionQueryThreshold || 0.01;
    this.targetFPS = options.targetFPS || 60;
    this.batchSize = options.batchSize || 100;
    this.useTimeSlicing = options.useTimeSlicing !== undefined ? options.useTimeSlicing : true;
    this.timeSliceBudget = options.timeSliceBudget || 5; // 5ms预算
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return 'OptimizedOcclusionCullingSystem';
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 初始化空间分割结构
    if (this.useSpatialPartitioning) {
      this.initializeOctree();
    }
  }

  /**
   * 初始化八叉树
   */
  private initializeOctree(): void {
    // 创建八叉树，覆盖整个场景
    const center = new THREE.Vector3(0, 0, 0);
    const size = new THREE.Vector3(1000, 1000, 1000);
    this.octree = new Octree(center, size, 8); // 最大深度8
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled()) {
      return;
    }

    const startTime = performance.now();

    // 获取相机和场景
    const camera = this.getCamera();
    const scene = this.getScene();

    if (!camera || !scene) {
      return;
    }

    // 发出剔除开始事件
    this.eventEmitter.emit(OptimizedOcclusionCullingEventType.CULLING_START);

    // 重置统计信息
    this.resetCullingStats();

    // 根据策略执行剔除
    this.executeCullingStrategy(camera, scene, deltaTime);

    // 更新统计信息
    this.cullingStats.cullingTime = performance.now() - startTime;
    this.updatePerformanceHistory();

    // 发出剔除完成事件
    this.eventEmitter.emit(OptimizedOcclusionCullingEventType.CULLING_COMPLETE, this.cullingStats);

    // 调用父类更新
    super.update(deltaTime);
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    if (!this.world) {
      return null;
    }

    for (const entity of this.world.getEntities().values()) {
      const camera = entity.getComponent('Camera') as Camera;
      if (camera) {
        return camera;
      }
    }

    return null;
  }

  /**
   * 获取场景
   * @returns 场景
   */
  private getScene(): Scene | null {
    if (!this.world) {
      return null;
    }

    for (const entity of this.world.getEntities().values()) {
      const scene = entity.getComponent('Scene');
      if (scene && scene instanceof Scene) {
        return scene;
      }
    }

    return null;
  }

  /**
   * 重置剔除统计信息
   */
  private resetCullingStats(): void {
    this.cullingStats.totalEntities = 0;
    this.cullingStats.frustumCulled = 0;
    this.cullingStats.occlusionCulled = 0;
    this.cullingStats.visibleEntities = 0;
    this.cullingStats.cullingRate = 0;
    this.cullingStats.frustumCullingTime = 0;
    this.cullingStats.occlusionCullingTime = 0;

    this.frustumCulledEntities.clear();
    this.occlusionCulledEntities.clear();
    this.visibleEntities.clear();
  }

  /**
   * 执行剔除策略
   * @param camera 相机
   * @param scene 场景
   * @param deltaTime 帧间隔时间
   */
  private executeCullingStrategy(camera: Camera, scene: Scene, deltaTime: number): void {
    switch (this.cullingStrategy) {
      case CullingStrategy.AGGRESSIVE:
        this.executeAggressiveCulling(camera, scene);
        break;
      case CullingStrategy.BALANCED:
        this.executeBalancedCulling(camera, scene);
        break;
      case CullingStrategy.CONSERVATIVE:
        this.executeConservativeCulling(camera, scene);
        break;
      case CullingStrategy.ADAPTIVE:
        this.executeAdaptiveCulling(camera, scene, deltaTime);
        break;
    }
  }

  /**
   * 执行激进剔除策略
   * @param camera 相机
   * @param scene 场景
   */
  private executeAggressiveCulling(camera: Camera, scene: Scene): void {
    // 激进策略：优先性能，使用较小的视锥体和较高的遮挡阈值
    const expandedFrustum = this.createExpandedFrustum(camera, 0.9); // 收缩视锥体

    // 执行视锥体剔除
    const frustumStartTime = performance.now();
    this.performOptimizedFrustumCulling(expandedFrustum);
    this.cullingStats.frustumCullingTime = performance.now() - frustumStartTime;

    // 执行遮挡剔除（使用较高阈值）
    const occlusionStartTime = performance.now();
    this.performOptimizedOcclusionCulling(camera, scene, this.occlusionQueryThreshold * 2);
    this.cullingStats.occlusionCullingTime = performance.now() - occlusionStartTime;

    this.updateCullingStats();
  }

  /**
   * 执行平衡剔除策略
   * @param camera 相机
   * @param scene 场景
   */
  private executeBalancedCulling(camera: Camera, scene: Scene): void {
    // 平衡策略：性能和质量兼顾
    const expandedFrustum = this.createExpandedFrustum(camera, this.frustumExpansionFactor);

    // 执行视锥体剔除
    const frustumStartTime = performance.now();
    this.performOptimizedFrustumCulling(expandedFrustum);
    this.cullingStats.frustumCullingTime = performance.now() - frustumStartTime;

    // 执行遮挡剔除
    const occlusionStartTime = performance.now();
    this.performOptimizedOcclusionCulling(camera, scene, this.occlusionQueryThreshold);
    this.cullingStats.occlusionCullingTime = performance.now() - occlusionStartTime;

    this.updateCullingStats();
  }

  /**
   * 执行保守剔除策略
   * @param camera 相机
   * @param scene 场景
   */
  private executeConservativeCulling(camera: Camera, scene: Scene): void {
    // 保守策略：优先质量，使用较大的视锥体和较低的遮挡阈值
    const expandedFrustum = this.createExpandedFrustum(camera, this.frustumExpansionFactor * 1.2);

    // 执行视锥体剔除
    const frustumStartTime = performance.now();
    this.performOptimizedFrustumCulling(expandedFrustum);
    this.cullingStats.frustumCullingTime = performance.now() - frustumStartTime;

    // 执行遮挡剔除（使用较低阈值）
    const occlusionStartTime = performance.now();
    this.performOptimizedOcclusionCulling(camera, scene, this.occlusionQueryThreshold * 0.5);
    this.cullingStats.occlusionCullingTime = performance.now() - occlusionStartTime;

    this.updateCullingStats();
  }

  /**
   * 执行自适应剔除策略
   * @param camera 相机
   * @param scene 场景
   * @param deltaTime 帧间隔时间
   */
  private executeAdaptiveCulling(camera: Camera, scene: Scene, deltaTime: number): void {
    // 根据性能历史动态调整策略
    const avgFPS = this.calculateAveragePerformance();

    if (avgFPS < this.targetFPS * 0.8) {
      // 性能不足，使用激进策略
      this.executeAggressiveCulling(camera, scene);
      this.eventEmitter.emit(OptimizedOcclusionCullingEventType.PERFORMANCE_THRESHOLD_TRIGGERED, 'low', avgFPS);
    } else if (avgFPS > this.targetFPS * 1.1) {
      // 性能充足，使用保守策略
      this.executeConservativeCulling(camera, scene);
    } else {
      // 性能适中，使用平衡策略
      this.executeBalancedCulling(camera, scene);
    }
  }

  /**
   * 创建扩展的视锥体
   * @param camera 相机
   * @param expansionFactor 扩展系数
   * @returns 扩展的视锥体
   */
  private createExpandedFrustum(camera: Camera, expansionFactor: number): THREE.Frustum {
    const threeCamera = camera.getThreeCamera();

    // 创建扩展的投影矩阵
    const expandedProjectionMatrix = threeCamera.projectionMatrix.clone();
    if (threeCamera instanceof THREE.PerspectiveCamera) {
      const expandedFov = threeCamera.fov * expansionFactor;
      expandedProjectionMatrix.makePerspective(
        -Math.tan(THREE.MathUtils.degToRad(expandedFov * 0.5)) * threeCamera.near,
        Math.tan(THREE.MathUtils.degToRad(expandedFov * 0.5)) * threeCamera.near,
        Math.tan(THREE.MathUtils.degToRad(expandedFov * 0.5)) * threeCamera.near / threeCamera.aspect,
        -Math.tan(THREE.MathUtils.degToRad(expandedFov * 0.5)) * threeCamera.near / threeCamera.aspect,
        threeCamera.near,
        threeCamera.far
      );
    }

    // 创建扩展的视锥体
    this.tempMatrix.multiplyMatrices(expandedProjectionMatrix, threeCamera.matrixWorldInverse);
    const expandedFrustum = new THREE.Frustum();
    expandedFrustum.setFromProjectionMatrix(this.tempMatrix);

    return expandedFrustum;
  }

  /**
   * 执行优化的视锥体剔除
   * @param frustum 视锥体
   */
  private performOptimizedFrustumCulling(frustum: THREE.Frustum): void {
    if (!this.world) {
      return;
    }

    const entities = Array.from(this.world.getEntities().values());
    this.cullingStats.totalEntities = entities.length;

    if (this.useSpatialPartitioning && this.octree) {
      // 使用空间分割优化
      this.performSpatialFrustumCulling(frustum);
    } else if (this.useTimeSlicing) {
      // 使用时间切片
      this.performTimeSlicedFrustumCulling(frustum, entities);
    } else {
      // 直接遍历所有实体
      this.performDirectFrustumCulling(frustum, entities);
    }

    this.eventEmitter.emit(OptimizedOcclusionCullingEventType.FRUSTUM_CULLING_COMPLETE, {
      totalEntities: this.cullingStats.totalEntities,
      culledEntities: this.frustumCulledEntities.size,
      cullingTime: this.cullingStats.frustumCullingTime
    });
  }

  /**
   * 执行空间分割的视锥体剔除
   * @param frustum 视锥体
   */
  private performSpatialFrustumCulling(frustum: THREE.Frustum): void {
    if (!this.octree || !this.world) {
      return;
    }

    // 更新八叉树
    this.updateOctree();

    // 查询与视锥体相交的八叉树节点
    const intersectingNodes = this.octree.queryFrustum(frustum);

    // 对相交节点中的实体进行视锥体测试
    for (const node of intersectingNodes) {
      for (const entity of node.entities) {
        this.testEntityFrustumCulling(entity, frustum);
      }
    }
  }

  /**
   * 执行时间切片的视锥体剔除
   * @param frustum 视锥体
   * @param entities 实体列表
   */
  private performTimeSlicedFrustumCulling(frustum: THREE.Frustum, entities: Entity[]): void {
    const startTime = performance.now();
    let processedCount = 0;

    // 从上次的位置继续处理
    while (this.currentBatchIndex < entities.length &&
           (performance.now() - startTime) < this.timeSliceBudget) {

      const entity = entities[this.currentBatchIndex];
      this.testEntityFrustumCulling(entity, frustum);

      this.currentBatchIndex++;
      processedCount++;
    }

    // 如果处理完所有实体，重置索引
    if (this.currentBatchIndex >= entities.length) {
      this.currentBatchIndex = 0;
    }

    Debug.log('OptimizedOcclusionCullingSystem',
      `时间切片处理了 ${processedCount} 个实体，用时 ${(performance.now() - startTime).toFixed(2)}ms`);
  }

  /**
   * 执行直接的视锥体剔除
   * @param frustum 视锥体
   * @param entities 实体列表
   */
  private performDirectFrustumCulling(frustum: THREE.Frustum, entities: Entity[]): void {
    for (const entity of entities) {
      this.testEntityFrustumCulling(entity, frustum);
    }
  }

  /**
   * 测试实体的视锥体剔除
   * @param entity 实体
   * @param frustum 视锥体
   */
  private testEntityFrustumCulling(entity: Entity, frustum: THREE.Frustum): void {
    const cullableComponent = entity.getComponent('CullableComponent') as CullableComponent;
    if (!cullableComponent) {
      return;
    }

    const transform = entity.getComponent('Transform') as Transform;
    if (!transform) {
      return;
    }

    // 获取实体的包围盒或包围球
    const boundingBox = cullableComponent.getBoundingBox();
    const boundingSphere = cullableComponent.getBoundingSphere();

    let isVisible = true;

    if (boundingSphere) {
      // 使用包围球测试（更快）
      this.tempSphere.copy(boundingSphere);
      this.tempSphere.applyMatrix4(transform.getWorldMatrix());
      isVisible = frustum.intersectsSphere(this.tempSphere);
    } else if (boundingBox) {
      // 使用包围盒测试（更精确）
      this.tempBox.copy(boundingBox);
      this.tempBox.applyMatrix4(transform.getWorldMatrix());
      isVisible = frustum.intersectsBox(this.tempBox);
    } else {
      // 使用点测试（最后的选择）
      const position = transform.getWorldPosition();
      isVisible = frustum.containsPoint(position);
    }

    if (!isVisible) {
      this.frustumCulledEntities.add(entity);
      cullableComponent.setVisible(false);
    } else {
      this.visibleEntities.add(entity);
    }
  }

  /**
   * 执行优化的遮挡剔除
   * @param camera 相机
   * @param scene 场景
   * @param threshold 遮挡阈值
   */
  private performOptimizedOcclusionCulling(camera: Camera, scene: Scene, threshold: number): void {
    // 只对通过视锥体剔除的可见实体进行遮挡剔除
    const visibleEntities = Array.from(this.visibleEntities);

    if (this.useHierarchicalCulling) {
      // 使用层次遮挡剔除
      this.performHierarchicalOcclusionCulling(camera, scene, visibleEntities, threshold);
    } else {
      // 使用标准遮挡剔除
      this.performStandardOcclusionCulling(camera, scene, visibleEntities, threshold);
    }

    this.eventEmitter.emit(OptimizedOcclusionCullingEventType.OCCLUSION_CULLING_COMPLETE, {
      visibleEntities: visibleEntities.length,
      occludedEntities: this.occlusionCulledEntities.size,
      cullingTime: this.cullingStats.occlusionCullingTime
    });
  }

  /**
   * 执行层次遮挡剔除
   * @param camera 相机
   * @param scene 场景
   * @param entities 实体列表
   * @param threshold 遮挡阈值
   */
  private performHierarchicalOcclusionCulling(camera: Camera, scene: Scene, entities: Entity[], threshold: number): void {
    // 按距离排序实体（近到远）
    const sortedEntities = this.sortEntitiesByDistance(camera, entities);

    // 分层处理
    const layers = this.groupEntitiesIntoLayers(sortedEntities);

    for (const layer of layers) {
      for (const entity of layer) {
        if (this.isEntityOccluded(entity, camera, threshold)) {
          this.occlusionCulledEntities.add(entity);
          this.visibleEntities.delete(entity);

          const cullableComponent = entity.getComponent('CullableComponent') as CullableComponent;
          if (cullableComponent) {
            cullableComponent.setVisible(false);
          }
        }
      }
    }
  }

  /**
   * 执行标准遮挡剔除
   * @param camera 相机
   * @param scene 场景
   * @param entities 实体列表
   * @param threshold 遮挡阈值
   */
  private performStandardOcclusionCulling(camera: Camera, scene: Scene, entities: Entity[], threshold: number): void {
    for (const entity of entities) {
      if (this.isEntityOccluded(entity, camera, threshold)) {
        this.occlusionCulledEntities.add(entity);
        this.visibleEntities.delete(entity);

        const cullableComponent = entity.getComponent('CullableComponent') as CullableComponent;
        if (cullableComponent) {
          cullableComponent.setVisible(false);
        }
      }
    }
  }

  /**
   * 更新八叉树
   */
  private updateOctree(): void {
    if (!this.octree || !this.world) {
      return;
    }

    // 清空八叉树
    this.octree.clear();

    // 重新插入所有实体
    for (const entity of this.world.getEntities().values()) {
      const cullableComponent = entity.getComponent('CullableComponent') as CullableComponent;
      const transform = entity.getComponent('Transform') as Transform;

      if (cullableComponent && transform) {
        const position = transform.getWorldPosition();
        this.octree.insert(entity, position);
      }
    }
  }

  /**
   * 按距离排序实体
   * @param camera 相机
   * @param entities 实体列表
   * @returns 排序后的实体列表
   */
  private sortEntitiesByDistance(camera: Camera, entities: Entity[]): Entity[] {
    const cameraPosition = camera.getThreeCamera().position;

    return entities.sort((a, b) => {
      const transformA = a.getComponent('Transform') as Transform;
      const transformB = b.getComponent('Transform') as Transform;

      if (!transformA || !transformB) {
        return 0;
      }

      const distanceA = transformA.getWorldPosition().distanceTo(cameraPosition);
      const distanceB = transformB.getWorldPosition().distanceTo(cameraPosition);

      return distanceA - distanceB;
    });
  }

  /**
   * 将实体分组到层次中
   * @param entities 排序后的实体列表
   * @returns 分层的实体列表
   */
  private groupEntitiesIntoLayers(entities: Entity[]): Entity[][] {
    const layers: Entity[][] = [];
    const layerSize = Math.ceil(entities.length / 4); // 分成4层

    for (let i = 0; i < entities.length; i += layerSize) {
      layers.push(entities.slice(i, i + layerSize));
    }

    return layers;
  }

  /**
   * 检查实体是否被遮挡
   * @param entity 实体
   * @param camera 相机
   * @param threshold 遮挡阈值
   * @returns 是否被遮挡
   */
  private isEntityOccluded(entity: Entity, camera: Camera, threshold: number): boolean {
    // 这里应该实现具体的遮挡检测算法
    // 为了简化，这里使用基于距离的简单启发式方法
    const transform = entity.getComponent('Transform') as Transform;
    if (!transform) {
      return false;
    }

    const cameraPosition = camera.getThreeCamera().position;
    const entityPosition = transform.getWorldPosition();
    const distance = entityPosition.distanceTo(cameraPosition);

    // 简单的基于距离的遮挡检测
    // 在实际实现中，这里应该使用更复杂的遮挡查询
    return distance > 100 && Math.random() < threshold;
  }

  /**
   * 更新剔除统计信息
   */
  private updateCullingStats(): void {
    this.cullingStats.frustumCulled = this.frustumCulledEntities.size;
    this.cullingStats.occlusionCulled = this.occlusionCulledEntities.size;
    this.cullingStats.visibleEntities = this.visibleEntities.size;

    if (this.cullingStats.totalEntities > 0) {
      this.cullingStats.cullingRate =
        (this.cullingStats.frustumCulled + this.cullingStats.occlusionCulled) /
        this.cullingStats.totalEntities;
    }
  }

  /**
   * 更新性能历史记录
   */
  private updatePerformanceHistory(): void {
    const fpsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.FPS);
    const currentFPS = fpsMetric ? fpsMetric.value : 60;

    this.performanceHistory.push(currentFPS);

    // 保持历史记录在合理范围内
    if (this.performanceHistory.length > 60) { // 保留最近60帧的数据
      this.performanceHistory.shift();
    }
  }

  /**
   * 计算平均性能
   * @returns 平均FPS
   */
  private calculateAveragePerformance(): number {
    if (this.performanceHistory.length === 0) {
      return this.targetFPS;
    }

    const sum = this.performanceHistory.reduce((acc, fps) => acc + fps, 0);
    return sum / this.performanceHistory.length;
  }

  /**
   * 获取剔除统计信息
   * @returns 剔除统计信息
   */
  public getCullingStats(): CullingStats {
    return { ...this.cullingStats };
  }

  /**
   * 设置剔除策略
   * @param strategy 剔除策略
   */
  public setCullingStrategy(strategy: CullingStrategy): void {
    this.cullingStrategy = strategy;
    Debug.log('OptimizedOcclusionCullingSystem', `剔除策略设置为: ${strategy}`);
  }

  /**
   * 获取剔除策略
   * @returns 剔除策略
   */
  public getCullingStrategy(): CullingStrategy {
    return this.cullingStrategy;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    super.dispose();

    // 清理资源
    this.frustumCulledEntities.clear();
    this.occlusionCulledEntities.clear();
    this.visibleEntities.clear();
    this.batchQueue.length = 0;
    this.performanceHistory.length = 0;

    // 销毁八叉树
    if (this.octree) {
      this.octree.clear();
      this.octree = null;
    }

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();
  }
}
