/**
 * 增强的资产浏览器组件
 * 实现资产预览缓存、资产搜索过滤和优化大量资产加载
 */
import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { 
  Card, Input, Select, Button, Space, Tooltip, Spin, Empty, 
  Row, Col, Tag, Dropdown, Modal, Progress, Alert, Switch
} from 'antd';
import { 
  SearchOutlined, FilterOutlined, AppstoreOutlined, BarsOutlined,
  SortAscendingOutlined, SortDescendingOutlined, ReloadOutlined,
  SettingOutlined, EyeOutlined, DownloadOutlined, DeleteOutlined,
  FolderOutlined, FileImageOutlined, FileTextOutlined, 
  VideoCameraOutlined, AudioOutlined, FileUnknownOutlined
} from '@ant-design/icons';
import { FixedSizeGrid as Grid } from 'react-window';
import { useTranslation } from 'react-i18next';
import { useAssetBrowserService } from '../../hooks/useAssetBrowserService';
import { AssetType } from '../../store/asset/assetSlice';
import AssetPreviewModal from './AssetPreviewModal';
import AssetThumbnail from './AssetThumbnail';
import './EnhancedAssetBrowser.less';

const { Search } = Input;
const { Option } = Select;

/**
 * 视图模式枚举
 */
export enum ViewMode {
  GRID = 'grid',
  LIST = 'list'
}

/**
 * 排序类型枚举
 */
export enum SortType {
  NAME = 'name',
  TYPE = 'type',
  SIZE = 'size',
  DATE = 'date'
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc'
}

/**
 * 资产过滤器接口
 */
export interface AssetFilter {
  types: AssetType[];
  sizeRange: [number, number];
  dateRange: [string, string];
  tags: string[];
}

/**
 * 组件属性接口
 */
interface EnhancedAssetBrowserProps {
  /** 项目ID */
  projectId?: string;
  /** 文件夹ID */
  folderId?: string;
  /** 是否启用虚拟化 */
  enableVirtualization?: boolean;
  /** 网格项大小 */
  gridItemSize?: number;
  /** 最大可见项数 */
  maxVisibleItems?: number;
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 资产选择回调 */
  onAssetSelect?: (assetIds: string[]) => void;
  /** 资产双击回调 */
  onAssetDoubleClick?: (assetId: string) => void;
}

/**
 * 资产类型图标映射
 */
const ASSET_TYPE_ICONS = {
  [AssetType.MODEL]: <FileUnknownOutlined />,
  [AssetType.TEXTURE]: <FileImageOutlined />,
  [AssetType.MATERIAL]: <FileTextOutlined />,
  [AssetType.AUDIO]: <AudioOutlined />,
  [AssetType.SCRIPT]: <FileTextOutlined />,
  [AssetType.PREFAB]: <FileUnknownOutlined />,
  [AssetType.SCENE]: <FolderOutlined />,
  [AssetType.ANIMATION]: <VideoCameraOutlined />,
  [AssetType.PARTICLE]: <FileUnknownOutlined />,
  [AssetType.OTHER]: <FileUnknownOutlined />
};

/**
 * 资产类型颜色映射
 */
const ASSET_TYPE_COLORS = {
  [AssetType.MODEL]: '#1890ff',
  [AssetType.TEXTURE]: '#52c41a',
  [AssetType.MATERIAL]: '#faad14',
  [AssetType.AUDIO]: '#722ed1',
  [AssetType.SCRIPT]: '#13c2c2',
  [AssetType.PREFAB]: '#fa541c',
  [AssetType.SCENE]: '#eb2f96',
  [AssetType.ANIMATION]: '#f5222d',
  [AssetType.PARTICLE]: '#a0d911',
  [AssetType.OTHER]: '#8c8c8c'
};

/**
 * 增强的资产浏览器组件
 */
export const EnhancedAssetBrowser: React.FC<EnhancedAssetBrowserProps> = ({
  projectId,
  folderId,
  enableVirtualization = true,
  gridItemSize = 120,
  maxVisibleItems = 100,
  showPreview = true,
  onAssetSelect,
  onAssetDoubleClick
}) => {
  const { t } = useTranslation();
  const gridRef = useRef<Grid>(null);

  // 使用自定义Hook管理资产浏览器服务
  const {
    assets,
    filteredAssets,
    selectedAssetIds,
    isLoading,
    loadingProgress,
    searchTerm,
    setSearchTerm,
    filter,
    setFilter,
    sortType,
    setSortType,
    sortDirection,
    setSortDirection,
    viewMode,
    setViewMode,
    previewCache,
    selectAsset,
    selectMultipleAssets,
    clearSelection,
    refreshAssets,
    preloadThumbnails,
    getAssetPreview
  } = useAssetBrowserService(projectId, folderId);

  // 状态
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewAssetId, setPreviewAssetId] = useState<string | null>(null);
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [enablePreviewCache, setEnablePreviewCache] = useState(true);

  /**
   * 处理资产选择
   */
  const handleAssetSelect = useCallback((assetId: string, multiSelect: boolean = false) => {
    if (multiSelect) {
      selectMultipleAssets([assetId]);
    } else {
      selectAsset(assetId);
    }
    onAssetSelect?.(multiSelect ? [...selectedAssetIds, assetId] : [assetId]);
  }, [selectAsset, selectMultipleAssets, selectedAssetIds, onAssetSelect]);

  /**
   * 处理资产双击
   */
  const handleAssetDoubleClick = useCallback((assetId: string) => {
    onAssetDoubleClick?.(assetId);
  }, [onAssetDoubleClick]);

  /**
   * 处理预览
   */
  const handlePreview = useCallback((assetId: string) => {
    setPreviewAssetId(assetId);
    setPreviewModalVisible(true);
  }, []);

  /**
   * 网格项渲染器
   */
  const GridItem = useCallback(({ columnIndex, rowIndex, style }: any) => {
    const index = rowIndex * Math.floor((window.innerWidth - 300) / (gridItemSize + 16)) + columnIndex;
    const asset = filteredAssets[index];
    
    if (!asset) return null;

    const isSelected = selectedAssetIds.includes(asset.id);

    return (
      <div style={style} className="asset-grid-item">
        <Card
          hoverable
          className={`asset-card ${isSelected ? 'selected' : ''}`}
          cover={
            <AssetThumbnail
              asset={asset}
              size={gridItemSize - 24}
              enableCache={enablePreviewCache}
              onClick={() => handleAssetSelect(asset.id)}
              onDoubleClick={() => handleAssetDoubleClick(asset.id)}
            />
          }
          actions={[
            <Tooltip title={t('assets.preview')} key="preview">
              <Button 
                type="text" 
                icon={<EyeOutlined />} 
                onClick={() => handlePreview(asset.id)}
              />
            </Tooltip>,
            <Tooltip title={t('assets.download')} key="download">
              <Button type="text" icon={<DownloadOutlined />} />
            </Tooltip>,
            <Tooltip title={t('assets.delete')} key="delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          ]}
        >
          <Card.Meta
            title={
              <div className="asset-title">
                <span className="asset-icon" style={{ color: ASSET_TYPE_COLORS[asset.type] }}>
                  {ASSET_TYPE_ICONS[asset.type]}
                </span>
                <span className="asset-name" title={asset.name}>
                  {asset.name}
                </span>
              </div>
            }
            description={
              <div className="asset-meta">
                <Tag color={ASSET_TYPE_COLORS[asset.type]} size="small">
                  {asset.type}
                </Tag>
                {asset.metadata?.size && (
                  <span className="asset-size">
                    {formatFileSize(asset.metadata.size)}
                  </span>
                )}
              </div>
            }
          />
        </Card>
      </div>
    );
  }, [
    filteredAssets, 
    selectedAssetIds, 
    gridItemSize, 
    enablePreviewCache,
    handleAssetSelect, 
    handleAssetDoubleClick, 
    handlePreview,
    t
  ]);

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div className="asset-browser-toolbar">
      <Row gutter={16} align="middle">
        <Col flex="auto">
          <Search
            placeholder={t('assets.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ width: '100%' }}
            allowClear
          />
        </Col>
        
        <Col>
          <Space>
            <Tooltip title={t('assets.filter')}>
              <Button 
                icon={<FilterOutlined />} 
                onClick={() => setFilterModalVisible(true)}
              />
            </Tooltip>
            
            <Select
              value={sortType}
              onChange={setSortType}
              style={{ width: 100 }}
            >
              <Option value={SortType.NAME}>{t('assets.sortByName')}</Option>
              <Option value={SortType.TYPE}>{t('assets.sortByType')}</Option>
              <Option value={SortType.SIZE}>{t('assets.sortBySize')}</Option>
              <Option value={SortType.DATE}>{t('assets.sortByDate')}</Option>
            </Select>
            
            <Button
              icon={sortDirection === SortDirection.ASC ? <SortAscendingOutlined /> : <SortDescendingOutlined />}
              onClick={() => setSortDirection(
                sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC
              )}
            />
            
            <Button.Group>
              <Button
                type={viewMode === ViewMode.GRID ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => setViewMode(ViewMode.GRID)}
              />
              <Button
                type={viewMode === ViewMode.LIST ? 'primary' : 'default'}
                icon={<BarsOutlined />}
                onClick={() => setViewMode(ViewMode.LIST)}
              />
            </Button.Group>
            
            <Tooltip title={t('assets.refresh')}>
              <Button icon={<ReloadOutlined />} onClick={refreshAssets} />
            </Tooltip>
            
            <Tooltip title={t('assets.settings')}>
              <Button icon={<SettingOutlined />} />
            </Tooltip>
          </Space>
        </Col>
      </Row>
    </div>
  );

  /**
   * 渲染资产网格
   */
  const renderAssetGrid = () => {
    if (isLoading && filteredAssets.length === 0) {
      return (
        <div className="loading-container">
          <Spin size="large" />
          {loadingProgress > 0 && (
            <Progress 
              percent={loadingProgress} 
              style={{ marginTop: 16, width: 200 }}
            />
          )}
        </div>
      );
    }

    if (filteredAssets.length === 0) {
      return (
        <Empty
          description={searchTerm ? t('assets.noSearchResults') : t('assets.noAssets')}
          style={{ marginTop: 50 }}
        />
      );
    }

    if (enableVirtualization && filteredAssets.length > maxVisibleItems) {
      const columnCount = Math.floor((window.innerWidth - 300) / (gridItemSize + 16));
      const rowCount = Math.ceil(filteredAssets.length / columnCount);

      return (
        <Grid
          ref={gridRef}
          columnCount={columnCount}
          columnWidth={gridItemSize + 16}
          height={600}
          rowCount={rowCount}
          rowHeight={gridItemSize + 80}
          className="virtualized-asset-grid"
        >
          {GridItem}
        </Grid>
      );
    }

    // 非虚拟化渲染
    return (
      <div className="asset-grid">
        <Row gutter={[16, 16]}>
          {filteredAssets.map((asset) => (
            <Col key={asset.id} span={6}>
              <GridItem 
                columnIndex={0} 
                rowIndex={0} 
                style={{}} 
                data={{ asset }}
              />
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  return (
    <Card className="enhanced-asset-browser" title={t('assets.title')}>
      {renderToolbar()}
      
      {selectedAssetIds.length > 0 && (
        <Alert
          message={t('assets.selectedCount', { count: selectedAssetIds.length })}
          type="info"
          showIcon
          action={
            <Button size="small" onClick={clearSelection}>
              {t('assets.clearSelection')}
            </Button>
          }
          style={{ margin: '16px 0' }}
        />
      )}

      <div className="asset-browser-content">
        {renderAssetGrid()}
      </div>

      {/* 预览模态框 */}
      {showPreview && (
        <AssetPreviewModal
          visible={previewModalVisible}
          assetId={previewAssetId}
          onClose={() => {
            setPreviewModalVisible(false);
            setPreviewAssetId(null);
          }}
        />
      )}

      {/* 过滤器模态框 */}
      <Modal
        title={t('assets.filterTitle')}
        visible={filterModalVisible}
        onCancel={() => setFilterModalVisible(false)}
        footer={null}
      >
        {/* 过滤器内容 */}
        <div className="asset-filter-content">
          <div className="filter-section">
            <label>{t('assets.enablePreviewCache')}</label>
            <Switch
              checked={enablePreviewCache}
              onChange={setEnablePreviewCache}
            />
          </div>
        </div>
      </Modal>
    </Card>
  );
};

export default EnhancedAssetBrowser;
