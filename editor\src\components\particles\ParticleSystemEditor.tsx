/**
 * 粒子系统编辑器组件
 * 实现可视化粒子编辑、粒子效果预设和实时粒子预览
 */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Slider,
  Button,
  ButtonGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  Chip,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Pause as PauseIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  FolderOpen as LoadIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { ColorPicker } from '../common/ColorPicker';
import { useEngine } from '../../hooks/useEngine';

/**
 * 粒子形状类型
 */
enum ParticleShape {
  POINT = 'point',
  SPRITE = 'sprite',
  BILLBOARD = 'billboard',
  MESH = 'mesh'
}

/**
 * 混合模式
 */
enum BlendMode {
  NORMAL = 'normal',
  ADDITIVE = 'additive',
  SUBTRACTIVE = 'subtractive',
  MULTIPLY = 'multiply'
}

/**
 * 发射器形状
 */
enum EmitterShape {
  POINT = 'point',
  BOX = 'box',
  SPHERE = 'sphere',
  CONE = 'cone',
  CIRCLE = 'circle'
}

/**
 * 粒子系统配置
 */
interface ParticleSystemConfig {
  // 基础设置
  name: string;
  enabled: boolean;
  maxParticles: number;
  
  // 发射器设置
  emitterShape: EmitterShape;
  emissionRate: number;
  burstCount: number;
  duration: number;
  loop: boolean;
  
  // 粒子属性
  particleShape: ParticleShape;
  startLifetime: { min: number; max: number };
  startSpeed: { min: number; max: number };
  startSize: { min: number; max: number };
  startRotation: { min: number; max: number };
  startColor: string;
  
  // 随时间变化
  sizeOverLifetime: boolean;
  colorOverLifetime: boolean;
  velocityOverLifetime: boolean;
  rotationOverLifetime: boolean;
  
  // 物理属性
  gravity: number;
  damping: number;
  
  // 渲染设置
  blendMode: BlendMode;
  sortParticles: boolean;
  
  // 纹理设置
  texture?: string;
  textureSheet?: {
    tilesX: number;
    tilesY: number;
    animation: boolean;
    frameRate: number;
  };
}

/**
 * 粒子效果预设
 */
const PARTICLE_PRESETS: { [key: string]: Partial<ParticleSystemConfig> } = {
  fire: {
    name: '火焰',
    emitterShape: EmitterShape.CIRCLE,
    emissionRate: 50,
    startLifetime: { min: 1, max: 2 },
    startSpeed: { min: 2, max: 5 },
    startSize: { min: 0.5, max: 1.5 },
    startColor: '#ff4500',
    gravity: -2,
    blendMode: BlendMode.ADDITIVE,
    sizeOverLifetime: true,
    colorOverLifetime: true
  },
  smoke: {
    name: '烟雾',
    emitterShape: EmitterShape.CIRCLE,
    emissionRate: 20,
    startLifetime: { min: 3, max: 5 },
    startSpeed: { min: 0.5, max: 1.5 },
    startSize: { min: 1, max: 2 },
    startColor: '#888888',
    gravity: -0.5,
    blendMode: BlendMode.NORMAL,
    sizeOverLifetime: true,
    colorOverLifetime: true
  },
  explosion: {
    name: '爆炸',
    emitterShape: EmitterShape.SPHERE,
    emissionRate: 0,
    burstCount: 100,
    startLifetime: { min: 0.5, max: 1.5 },
    startSpeed: { min: 5, max: 15 },
    startSize: { min: 0.2, max: 0.8 },
    startColor: '#ffaa00',
    gravity: -5,
    blendMode: BlendMode.ADDITIVE,
    sizeOverLifetime: true,
    colorOverLifetime: true
  },
  rain: {
    name: '雨滴',
    emitterShape: EmitterShape.BOX,
    emissionRate: 200,
    startLifetime: { min: 2, max: 3 },
    startSpeed: { min: 10, max: 15 },
    startSize: { min: 0.1, max: 0.2 },
    startColor: '#4488ff',
    gravity: -20,
    blendMode: BlendMode.NORMAL,
    sortParticles: true
  },
  snow: {
    name: '雪花',
    emitterShape: EmitterShape.BOX,
    emissionRate: 50,
    startLifetime: { min: 5, max: 8 },
    startSpeed: { min: 1, max: 3 },
    startSize: { min: 0.3, max: 0.8 },
    startColor: '#ffffff',
    gravity: -2,
    damping: 0.1,
    blendMode: BlendMode.NORMAL,
    rotationOverLifetime: true
  }
};

/**
 * 粒子系统编辑器组件属性
 */
interface ParticleSystemEditorProps {
  onParticleSystemChange?: (config: ParticleSystemConfig) => void;
  onPreviewToggle?: (enabled: boolean) => void;
}

/**
 * 粒子系统编辑器组件
 */
export const ParticleSystemEditor: React.FC<ParticleSystemEditorProps> = ({
  onParticleSystemChange,
  onPreviewToggle
}) => {
  const { engine } = useEngine();
  const [activeTab, setActiveTab] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const [presetDialogOpen, setPresetDialogOpen] = useState(false);
  
  // 粒子系统配置
  const [config, setConfig] = useState<ParticleSystemConfig>({
    name: '新粒子系统',
    enabled: true,
    maxParticles: 1000,
    emitterShape: EmitterShape.POINT,
    emissionRate: 10,
    burstCount: 0,
    duration: 5,
    loop: true,
    particleShape: ParticleShape.SPRITE,
    startLifetime: { min: 1, max: 3 },
    startSpeed: { min: 1, max: 5 },
    startSize: { min: 0.5, max: 1 },
    startRotation: { min: 0, max: 360 },
    startColor: '#ffffff',
    sizeOverLifetime: false,
    colorOverLifetime: false,
    velocityOverLifetime: false,
    rotationOverLifetime: false,
    gravity: 0,
    damping: 0,
    blendMode: BlendMode.NORMAL,
    sortParticles: false
  });

  const particleSystemRef = useRef<any>(null);

  /**
   * 初始化粒子系统
   */
  useEffect(() => {
    if (engine) {
      const particleSystem = engine.getSystem('ParticleSystem');
      if (particleSystem) {
        particleSystemRef.current = particleSystem;
      }
    }
  }, [engine]);

  /**
   * 处理配置变化
   */
  const handleConfigChange = useCallback((key: keyof ParticleSystemConfig, value: any) => {
    setConfig(prev => {
      const newConfig = { ...prev, [key]: value };
      onParticleSystemChange?.(newConfig);
      return newConfig;
    });
  }, [onParticleSystemChange]);

  /**
   * 处理范围值变化
   */
  const handleRangeChange = useCallback((key: keyof ParticleSystemConfig, field: 'min' | 'max', value: number) => {
    setConfig(prev => {
      const currentRange = prev[key] as { min: number; max: number };
      const newRange = { ...currentRange, [field]: value };
      const newConfig = { ...prev, [key]: newRange };
      onParticleSystemChange?.(newConfig);
      return newConfig;
    });
  }, [onParticleSystemChange]);

  /**
   * 播放粒子系统
   */
  const playParticleSystem = useCallback(() => {
    setIsPlaying(true);
    setIsPaused(false);
    // 这里应该调用实际的粒子系统播放方法
    console.log('播放粒子系统');
  }, []);

  /**
   * 暂停粒子系统
   */
  const pauseParticleSystem = useCallback(() => {
    setIsPaused(true);
    // 这里应该调用实际的粒子系统暂停方法
    console.log('暂停粒子系统');
  }, []);

  /**
   * 停止粒子系统
   */
  const stopParticleSystem = useCallback(() => {
    setIsPlaying(false);
    setIsPaused(false);
    // 这里应该调用实际的粒子系统停止方法
    console.log('停止粒子系统');
  }, []);

  /**
   * 重置粒子系统
   */
  const resetParticleSystem = useCallback(() => {
    stopParticleSystem();
    // 这里应该调用实际的粒子系统重置方法
    console.log('重置粒子系统');
  }, [stopParticleSystem]);

  /**
   * 应用预设
   */
  const applyPreset = useCallback((presetKey: string) => {
    const preset = PARTICLE_PRESETS[presetKey];
    if (preset) {
      const newConfig = { ...config, ...preset };
      setConfig(newConfig);
      onParticleSystemChange?.(newConfig);
      setPresetDialogOpen(false);
    }
  }, [config, onParticleSystemChange]);

  /**
   * 切换预览
   */
  const togglePreview = useCallback(() => {
    const newShowPreview = !showPreview;
    setShowPreview(newShowPreview);
    onPreviewToggle?.(newShowPreview);
  }, [showPreview, onPreviewToggle]);

  /**
   * 渲染基础设置面板
   */
  const renderBasicPanel = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="粒子系统名称"
          value={config.name}
          onChange={(e) => handleConfigChange('name', e.target.value)}
        />
      </Grid>

      <Grid item xs={6}>
        <FormControlLabel
          control={
            <Switch
              checked={config.enabled}
              onChange={(e) => handleConfigChange('enabled', e.target.checked)}
            />
          }
          label="启用"
        />
      </Grid>

      <Grid item xs={6}>
        <FormControlLabel
          control={
            <Switch
              checked={config.loop}
              onChange={(e) => handleConfigChange('loop', e.target.checked)}
            />
          }
          label="循环播放"
        />
      </Grid>

      <Grid item xs={12}>
        <Typography gutterBottom>最大粒子数: {config.maxParticles}</Typography>
        <Slider
          value={config.maxParticles}
          onChange={(_, value) => handleConfigChange('maxParticles', value)}
          min={10}
          max={10000}
          step={10}
        />
      </Grid>

      <Grid item xs={12}>
        <Typography gutterBottom>持续时间: {config.duration}s</Typography>
        <Slider
          value={config.duration}
          onChange={(_, value) => handleConfigChange('duration', value)}
          min={0.1}
          max={60}
          step={0.1}
        />
      </Grid>
    </Grid>
  );

  /**
   * 渲染发射器设置面板
   */
  const renderEmitterPanel = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <FormControl fullWidth>
          <InputLabel>发射器形状</InputLabel>
          <Select
            value={config.emitterShape}
            onChange={(e) => handleConfigChange('emitterShape', e.target.value)}
          >
            <MenuItem value={EmitterShape.POINT}>点</MenuItem>
            <MenuItem value={EmitterShape.BOX}>盒子</MenuItem>
            <MenuItem value={EmitterShape.SPHERE}>球体</MenuItem>
            <MenuItem value={EmitterShape.CONE}>锥体</MenuItem>
            <MenuItem value={EmitterShape.CIRCLE}>圆形</MenuItem>
          </Select>
        </FormControl>
      </Grid>

      <Grid item xs={12}>
        <Typography gutterBottom>发射速率: {config.emissionRate}/s</Typography>
        <Slider
          value={config.emissionRate}
          onChange={(_, value) => handleConfigChange('emissionRate', value)}
          min={0}
          max={1000}
          step={1}
        />
      </Grid>

      <Grid item xs={12}>
        <Typography gutterBottom>爆发数量: {config.burstCount}</Typography>
        <Slider
          value={config.burstCount}
          onChange={(_, value) => handleConfigChange('burstCount', value)}
          min={0}
          max={1000}
          step={1}
        />
      </Grid>
    </Grid>
  );

  /**
   * 渲染粒子属性面板
   */
  const renderParticlePanel = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <FormControl fullWidth>
          <InputLabel>粒子形状</InputLabel>
          <Select
            value={config.particleShape}
            onChange={(e) => handleConfigChange('particleShape', e.target.value)}
          >
            <MenuItem value={ParticleShape.POINT}>点</MenuItem>
            <MenuItem value={ParticleShape.SPRITE}>精灵</MenuItem>
            <MenuItem value={ParticleShape.BILLBOARD}>广告牌</MenuItem>
            <MenuItem value={ParticleShape.MESH}>网格</MenuItem>
          </Select>
        </FormControl>
      </Grid>

      <Grid item xs={6}>
        <Typography gutterBottom>生命周期最小值: {config.startLifetime.min}s</Typography>
        <Slider
          value={config.startLifetime.min}
          onChange={(_, value) => handleRangeChange('startLifetime', 'min', value as number)}
          min={0.1}
          max={10}
          step={0.1}
        />
      </Grid>

      <Grid item xs={6}>
        <Typography gutterBottom>生命周期最大值: {config.startLifetime.max}s</Typography>
        <Slider
          value={config.startLifetime.max}
          onChange={(_, value) => handleRangeChange('startLifetime', 'max', value as number)}
          min={0.1}
          max={10}
          step={0.1}
        />
      </Grid>

      <Grid item xs={6}>
        <Typography gutterBottom>初始速度最小值: {config.startSpeed.min}</Typography>
        <Slider
          value={config.startSpeed.min}
          onChange={(_, value) => handleRangeChange('startSpeed', 'min', value as number)}
          min={0}
          max={50}
          step={0.1}
        />
      </Grid>

      <Grid item xs={6}>
        <Typography gutterBottom>初始速度最大值: {config.startSpeed.max}</Typography>
        <Slider
          value={config.startSpeed.max}
          onChange={(_, value) => handleRangeChange('startSpeed', 'max', value as number)}
          min={0}
          max={50}
          step={0.1}
        />
      </Grid>

      <Grid item xs={6}>
        <Typography gutterBottom>初始大小最小值: {config.startSize.min}</Typography>
        <Slider
          value={config.startSize.min}
          onChange={(_, value) => handleRangeChange('startSize', 'min', value as number)}
          min={0.1}
          max={10}
          step={0.1}
        />
      </Grid>

      <Grid item xs={6}>
        <Typography gutterBottom>初始大小最大值: {config.startSize.max}</Typography>
        <Slider
          value={config.startSize.max}
          onChange={(_, value) => handleRangeChange('startSize', 'max', value as number)}
          min={0.1}
          max={10}
          step={0.1}
        />
      </Grid>

      <Grid item xs={12}>
        <Typography gutterBottom>初始颜色</Typography>
        <ColorPicker
          color={config.startColor}
          onChange={(color) => handleConfigChange('startColor', color)}
        />
      </Grid>
    </Grid>
  );

  /**
   * 渲染随时间变化面板
   */
  const renderOverLifetimePanel = () => (
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <FormControlLabel
          control={
            <Switch
              checked={config.sizeOverLifetime}
              onChange={(e) => handleConfigChange('sizeOverLifetime', e.target.checked)}
            />
          }
          label="大小随时间变化"
        />
      </Grid>

      <Grid item xs={6}>
        <FormControlLabel
          control={
            <Switch
              checked={config.colorOverLifetime}
              onChange={(e) => handleConfigChange('colorOverLifetime', e.target.checked)}
            />
          }
          label="颜色随时间变化"
        />
      </Grid>

      <Grid item xs={6}>
        <FormControlLabel
          control={
            <Switch
              checked={config.velocityOverLifetime}
              onChange={(e) => handleConfigChange('velocityOverLifetime', e.target.checked)}
            />
          }
          label="速度随时间变化"
        />
      </Grid>

      <Grid item xs={6}>
        <FormControlLabel
          control={
            <Switch
              checked={config.rotationOverLifetime}
              onChange={(e) => handleConfigChange('rotationOverLifetime', e.target.checked)}
            />
          }
          label="旋转随时间变化"
        />
      </Grid>
    </Grid>
  );

  /**
   * 渲染物理设置面板
   */
  const renderPhysicsPanel = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <Typography gutterBottom>重力: {config.gravity}</Typography>
        <Slider
          value={config.gravity}
          onChange={(_, value) => handleConfigChange('gravity', value)}
          min={-50}
          max={50}
          step={0.1}
        />
      </Grid>

      <Grid item xs={12}>
        <Typography gutterBottom>阻尼: {config.damping}</Typography>
        <Slider
          value={config.damping}
          onChange={(_, value) => handleConfigChange('damping', value)}
          min={0}
          max={1}
          step={0.01}
        />
      </Grid>
    </Grid>
  );

  /**
   * 渲染渲染设置面板
   */
  const renderRenderingPanel = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <FormControl fullWidth>
          <InputLabel>混合模式</InputLabel>
          <Select
            value={config.blendMode}
            onChange={(e) => handleConfigChange('blendMode', e.target.value)}
          >
            <MenuItem value={BlendMode.NORMAL}>正常</MenuItem>
            <MenuItem value={BlendMode.ADDITIVE}>加法</MenuItem>
            <MenuItem value={BlendMode.SUBTRACTIVE}>减法</MenuItem>
            <MenuItem value={BlendMode.MULTIPLY}>乘法</MenuItem>
          </Select>
        </FormControl>
      </Grid>

      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={config.sortParticles}
              onChange={(e) => handleConfigChange('sortParticles', e.target.checked)}
            />
          }
          label="粒子排序"
        />
      </Grid>
    </Grid>
  );
