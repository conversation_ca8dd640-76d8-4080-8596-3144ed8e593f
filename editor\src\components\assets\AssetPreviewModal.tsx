/**
 * 资产预览模态框组件
 * 支持多种资产类型的预览和详细信息显示
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Spin, Descriptions, Tag, Button, Space, Image, Alert } from 'antd';
import { 
  DownloadOutlined, EditOutlined, DeleteOutlined, 
  CopyOutlined, ShareAltOutlined 
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Asset, AssetType } from '../../store/asset/assetSlice';
import './AssetPreviewModal.less';

/**
 * 组件属性接口
 */
interface AssetPreviewModalProps {
  /** 是否可见 */
  visible: boolean;
  /** 资产ID */
  assetId: string | null;
  /** 关闭回调 */
  onClose: () => void;
  /** 编辑回调 */
  onEdit?: (assetId: string) => void;
  /** 删除回调 */
  onDelete?: (assetId: string) => void;
  /** 下载回调 */
  onDownload?: (assetId: string) => void;
}

/**
 * 资产预览模态框组件
 */
export const AssetPreviewModal: React.FC<AssetPreviewModalProps> = ({
  visible,
  assetId,
  onClose,
  onEdit,
  onDelete,
  onDownload
}) => {
  const { t } = useTranslation();
  const [asset, setAsset] = useState<Asset | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [previewError, setPreviewError] = useState<string | null>(null);

  /**
   * 加载资产详情
   */
  const loadAssetDetails = useCallback(async (id: string) => {
    setIsLoading(true);
    setPreviewError(null);

    try {
      const response = await fetch(`/api/assets/${id}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const assetData = await response.json();
      setAsset(assetData);
    } catch (error) {
      console.error('Failed to load asset details:', error);
      setPreviewError(t('assets.loadError'));
    } finally {
      setIsLoading(false);
    }
  }, [t]);

  /**
   * 当资产ID变化时加载详情
   */
  useEffect(() => {
    if (visible && assetId) {
      loadAssetDetails(assetId);
    } else {
      setAsset(null);
      setPreviewError(null);
    }
  }, [visible, assetId, loadAssetDetails]);

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * 格式化日期
   */
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  /**
   * 渲染预览内容
   */
  const renderPreviewContent = () => {
    if (!asset) return null;

    switch (asset.type) {
      case AssetType.TEXTURE:
        return (
          <div className="preview-image">
            <Image
              src={asset.url}
              alt={asset.name}
              style={{ maxWidth: '100%', maxHeight: '400px' }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          </div>
        );

      case AssetType.MODEL:
        return (
          <div className="preview-model">
            <div className="model-placeholder">
              <p>{t('assets.modelPreviewNotSupported')}</p>
              <Button type="primary" onClick={() => onDownload?.(asset.id)}>
                {t('assets.downloadToView')}
              </Button>
            </div>
          </div>
        );

      case AssetType.AUDIO:
        return (
          <div className="preview-audio">
            <audio controls style={{ width: '100%' }}>
              <source src={asset.url} />
              {t('assets.audioNotSupported')}
            </audio>
          </div>
        );

      case AssetType.SCRIPT:
      case AssetType.MATERIAL:
        return (
          <div className="preview-text">
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '4px',
              maxHeight: '400px',
              overflow: 'auto'
            }}>
              {asset.metadata?.content || t('assets.noPreviewAvailable')}
            </pre>
          </div>
        );

      default:
        return (
          <div className="preview-default">
            <Alert
              message={t('assets.previewNotSupported')}
              description={t('assets.previewNotSupportedDesc', { type: asset.type })}
              type="info"
              showIcon
            />
          </div>
        );
    }
  };

  /**
   * 渲染资产信息
   */
  const renderAssetInfo = () => {
    if (!asset) return null;

    return (
      <Descriptions column={2} size="small">
        <Descriptions.Item label={t('assets.name')}>
          {asset.name}
        </Descriptions.Item>
        
        <Descriptions.Item label={t('assets.type')}>
          <Tag color="blue">{asset.type}</Tag>
        </Descriptions.Item>
        
        {asset.metadata?.size && (
          <Descriptions.Item label={t('assets.size')}>
            {formatFileSize(asset.metadata.size)}
          </Descriptions.Item>
        )}
        
        {asset.metadata?.dimensions && (
          <Descriptions.Item label={t('assets.dimensions')}>
            {asset.metadata.dimensions.width} × {asset.metadata.dimensions.height}
          </Descriptions.Item>
        )}
        
        <Descriptions.Item label={t('assets.createdAt')}>
          {formatDate(asset.createdAt)}
        </Descriptions.Item>
        
        <Descriptions.Item label={t('assets.updatedAt')}>
          {formatDate(asset.updatedAt)}
        </Descriptions.Item>
        
        {asset.metadata?.tags && asset.metadata.tags.length > 0 && (
          <Descriptions.Item label={t('assets.tags')} span={2}>
            <Space wrap>
              {asset.metadata.tags.map((tag: string) => (
                <Tag key={tag}>{tag}</Tag>
              ))}
            </Space>
          </Descriptions.Item>
        )}
        
        {asset.url && (
          <Descriptions.Item label={t('assets.url')} span={2}>
            <a href={asset.url} target="_blank" rel="noopener noreferrer">
              {asset.url}
            </a>
          </Descriptions.Item>
        )}
      </Descriptions>
    );
  };

  /**
   * 渲染操作按钮
   */
  const renderActions = () => {
    if (!asset) return null;

    return (
      <Space>
        <Button 
          icon={<DownloadOutlined />}
          onClick={() => onDownload?.(asset.id)}
        >
          {t('assets.download')}
        </Button>
        
        <Button 
          icon={<EditOutlined />}
          onClick={() => onEdit?.(asset.id)}
        >
          {t('assets.edit')}
        </Button>
        
        <Button 
          icon={<CopyOutlined />}
          onClick={() => {
            navigator.clipboard.writeText(asset.url);
          }}
        >
          {t('assets.copyUrl')}
        </Button>
        
        <Button 
          icon={<ShareAltOutlined />}
        >
          {t('assets.share')}
        </Button>
        
        <Button 
          danger
          icon={<DeleteOutlined />}
          onClick={() => onDelete?.(asset.id)}
        >
          {t('assets.delete')}
        </Button>
      </Space>
    );
  };

  return (
    <Modal
      title={asset ? asset.name : t('assets.preview')}
      visible={visible}
      onCancel={onClose}
      width={800}
      footer={renderActions()}
      className="asset-preview-modal"
    >
      {isLoading ? (
        <div className="loading-container">
          <Spin size="large" />
        </div>
      ) : previewError ? (
        <Alert
          message={t('assets.error')}
          description={previewError}
          type="error"
          showIcon
        />
      ) : asset ? (
        <div className="asset-preview-content">
          <div className="preview-section">
            {renderPreviewContent()}
          </div>
          
          <div className="info-section">
            <h4>{t('assets.information')}</h4>
            {renderAssetInfo()}
          </div>
        </div>
      ) : null}
    </Modal>
  );
};

export default AssetPreviewModal;
