/**
 * 数据库连接池管理器
 * 实现智能连接池管理、负载均衡和故障转移
 */
import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Pool, PoolClient, PoolConfig } from 'pg';
import { EventEmitter } from 'events';

/**
 * 连接池配置
 */
export interface PoolConfiguration extends PoolConfig {
  /** 连接池名称 */
  name: string;
  /** 权重（用于负载均衡） */
  weight: number;
  /** 是否为主库 */
  isPrimary: boolean;
  /** 健康检查间隔 */
  healthCheckInterval: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 重试间隔 */
  retryInterval: number;
}

/**
 * 连接池状态
 */
export interface PoolStatus {
  /** 连接池名称 */
  name: string;
  /** 是否健康 */
  isHealthy: boolean;
  /** 是否可用 */
  isAvailable: boolean;
  /** 总连接数 */
  totalConnections: number;
  /** 活跃连接数 */
  activeConnections: number;
  /** 空闲连接数 */
  idleConnections: number;
  /** 等待连接数 */
  waitingConnections: number;
  /** 连接成功率 */
  successRate: number;
  /** 平均响应时间 */
  averageResponseTime: number;
  /** 最后健康检查时间 */
  lastHealthCheck: number;
  /** 错误计数 */
  errorCount: number;
}

/**
 * 负载均衡策略
 */
export enum LoadBalanceStrategy {
  ROUND_ROBIN = 'round_robin',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  RESPONSE_TIME = 'response_time'
}

/**
 * 连接池管理器
 */
@Injectable()
export class ConnectionPoolManager extends EventEmitter implements OnModuleDestroy {
  private readonly logger = new Logger(ConnectionPoolManager.name);

  /** 连接池映射 */
  private pools: Map<string, Pool> = new Map();
  /** 连接池配置 */
  private poolConfigs: Map<string, PoolConfiguration> = new Map();
  /** 连接池状态 */
  private poolStatuses: Map<string, PoolStatus> = new Map();

  /** 主连接池名称 */
  private primaryPoolName?: string;
  /** 读副本连接池名称列表 */
  private replicaPoolNames: string[] = [];

  /** 负载均衡策略 */
  private loadBalanceStrategy: LoadBalanceStrategy = LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN;
  /** 轮询计数器 */
  private roundRobinCounter = 0;

  /** 健康检查定时器 */
  private healthCheckTimers: Map<string, NodeJS.Timeout> = new Map();
  /** 统计定时器 */
  private statsTimer?: NodeJS.Timeout;

  /** 连接统计 */
  private connectionStats = {
    totalConnections: 0,
    successfulConnections: 0,
    failedConnections: 0,
    totalQueries: 0,
    successfulQueries: 0,
    failedQueries: 0
  };

  constructor(private readonly configService: ConfigService) {
    super();
    this.loadBalanceStrategy = this.configService.get<LoadBalanceStrategy>(
      'DB_LOAD_BALANCE_STRATEGY', 
      LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN
    );
  }

  /**
   * 添加连接池
   */
  public async addPool(config: PoolConfiguration): Promise<void> {
    try {
      const pool = new Pool(config);
      
      // 设置事件监听器
      this.setupPoolEventListeners(pool, config.name);
      
      // 测试连接
      await this.testPoolConnection(pool);
      
      // 存储连接池和配置
      this.pools.set(config.name, pool);
      this.poolConfigs.set(config.name, config);
      
      // 初始化状态
      this.initializePoolStatus(config.name);
      
      // 设置为主库或副本
      if (config.isPrimary) {
        this.primaryPoolName = config.name;
      } else {
        this.replicaPoolNames.push(config.name);
      }
      
      // 启动健康检查
      this.startHealthCheck(config.name);
      
      this.logger.log(`连接池 ${config.name} 添加成功`);
      this.emit('poolAdded', config.name);
      
    } catch (error) {
      this.logger.error(`连接池 ${config.name} 添加失败:`, error);
      throw error;
    }
  }

  /**
   * 移除连接池
   */
  public async removePool(poolName: string): Promise<void> {
    const pool = this.pools.get(poolName);
    if (!pool) return;

    try {
      // 停止健康检查
      this.stopHealthCheck(poolName);
      
      // 关闭连接池
      await pool.end();
      
      // 清理数据
      this.pools.delete(poolName);
      this.poolConfigs.delete(poolName);
      this.poolStatuses.delete(poolName);
      
      // 更新主库和副本列表
      if (this.primaryPoolName === poolName) {
        this.primaryPoolName = undefined;
      }
      this.replicaPoolNames = this.replicaPoolNames.filter(name => name !== poolName);
      
      this.logger.log(`连接池 ${poolName} 移除成功`);
      this.emit('poolRemoved', poolName);
      
    } catch (error) {
      this.logger.error(`连接池 ${poolName} 移除失败:`, error);
      throw error;
    }
  }

  /**
   * 获取连接（写操作）
   */
  public async getWriteConnection(): Promise<PoolClient> {
    if (!this.primaryPoolName) {
      throw new Error('没有可用的主数据库连接池');
    }

    return this.getConnectionFromPool(this.primaryPoolName);
  }

  /**
   * 获取连接（读操作）
   */
  public async getReadConnection(): Promise<PoolClient> {
    // 如果没有读副本，使用主库
    if (this.replicaPoolNames.length === 0) {
      return this.getWriteConnection();
    }

    // 选择最佳的读副本
    const poolName = this.selectReadPool();
    return this.getConnectionFromPool(poolName);
  }

  /**
   * 从指定连接池获取连接
   */
  private async getConnectionFromPool(poolName: string): Promise<PoolClient> {
    const pool = this.pools.get(poolName);
    const status = this.poolStatuses.get(poolName);

    if (!pool || !status?.isAvailable) {
      throw new Error(`连接池 ${poolName} 不可用`);
    }

    const startTime = Date.now();

    try {
      const client = await pool.connect();
      
      // 更新统计
      this.connectionStats.totalConnections++;
      this.connectionStats.successfulConnections++;
      
      // 更新连接池状态
      this.updatePoolStatus(poolName);
      
      // 包装客户端以监控查询
      return this.wrapClient(client, poolName, startTime);
      
    } catch (error) {
      this.connectionStats.totalConnections++;
      this.connectionStats.failedConnections++;
      
      // 标记连接池为不健康
      this.markPoolUnhealthy(poolName);
      
      this.logger.error(`从连接池 ${poolName} 获取连接失败:`, error);
      throw error;
    }
  }

  /**
   * 选择读连接池
   */
  private selectReadPool(): string {
    const availablePools = this.replicaPoolNames.filter(name => {
      const status = this.poolStatuses.get(name);
      return status?.isAvailable;
    });

    if (availablePools.length === 0) {
      // 如果没有可用的读副本，返回主库
      return this.primaryPoolName!;
    }

    switch (this.loadBalanceStrategy) {
      case LoadBalanceStrategy.ROUND_ROBIN:
        return this.selectRoundRobin(availablePools);
      
      case LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN:
        return this.selectWeightedRoundRobin(availablePools);
      
      case LoadBalanceStrategy.LEAST_CONNECTIONS:
        return this.selectLeastConnections(availablePools);
      
      case LoadBalanceStrategy.RESPONSE_TIME:
        return this.selectByResponseTime(availablePools);
      
      default:
        return availablePools[0];
    }
  }

  /**
   * 轮询选择
   */
  private selectRoundRobin(pools: string[]): string {
    const index = this.roundRobinCounter % pools.length;
    this.roundRobinCounter++;
    return pools[index];
  }

  /**
   * 加权轮询选择
   */
  private selectWeightedRoundRobin(pools: string[]): string {
    const weights = pools.map(name => {
      const config = this.poolConfigs.get(name);
      return config?.weight || 1;
    });

    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;

    for (let i = 0; i < pools.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return pools[i];
      }
    }

    return pools[0];
  }

  /**
   * 最少连接选择
   */
  private selectLeastConnections(pools: string[]): string {
    let minConnections = Infinity;
    let selectedPool = pools[0];

    for (const poolName of pools) {
      const status = this.poolStatuses.get(poolName);
      if (status && status.activeConnections < minConnections) {
        minConnections = status.activeConnections;
        selectedPool = poolName;
      }
    }

    return selectedPool;
  }

  /**
   * 响应时间选择
   */
  private selectByResponseTime(pools: string[]): string {
    let minResponseTime = Infinity;
    let selectedPool = pools[0];

    for (const poolName of pools) {
      const status = this.poolStatuses.get(poolName);
      if (status && status.averageResponseTime < minResponseTime) {
        minResponseTime = status.averageResponseTime;
        selectedPool = poolName;
      }
    }

    return selectedPool;
  }

  /**
   * 包装客户端以监控查询
   */
  private wrapClient(client: PoolClient, poolName: string, connectionStartTime: number): PoolClient {
    const originalQuery = client.query.bind(client);
    const originalRelease = client.release.bind(client);

    // 包装查询方法
    client.query = async (...args: any[]) => {
      const queryStartTime = Date.now();
      
      try {
        const result = await originalQuery(...args);
        
        // 更新查询统计
        this.connectionStats.totalQueries++;
        this.connectionStats.successfulQueries++;
        
        // 更新响应时间
        this.updateResponseTime(poolName, Date.now() - queryStartTime);
        
        return result;
        
      } catch (error) {
        this.connectionStats.totalQueries++;
        this.connectionStats.failedQueries++;
        
        // 增加错误计数
        this.incrementErrorCount(poolName);
        
        throw error;
      }
    };

    // 包装释放方法
    client.release = (err?: Error | boolean) => {
      // 更新连接时间统计
      const connectionTime = Date.now() - connectionStartTime;
      this.updateConnectionTime(poolName, connectionTime);
      
      return originalRelease(err);
    };

    return client;
  }

  /**
   * 设置连接池事件监听器
   */
  private setupPoolEventListeners(pool: Pool, poolName: string): void {
    pool.on('connect', () => {
      this.logger.debug(`连接池 ${poolName} 建立新连接`);
    });

    pool.on('acquire', () => {
      this.logger.debug(`连接池 ${poolName} 获取连接`);
    });

    pool.on('error', (error) => {
      this.logger.error(`连接池 ${poolName} 发生错误:`, error);
      this.markPoolUnhealthy(poolName);
    });

    pool.on('remove', () => {
      this.logger.debug(`连接池 ${poolName} 移除连接`);
    });
  }

  /**
   * 测试连接池连接
   */
  private async testPoolConnection(pool: Pool): Promise<void> {
    const client = await pool.connect();
    try {
      await client.query('SELECT 1');
    } finally {
      client.release();
    }
  }

  /**
   * 初始化连接池状态
   */
  private initializePoolStatus(poolName: string): void {
    const status: PoolStatus = {
      name: poolName,
      isHealthy: true,
      isAvailable: true,
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingConnections: 0,
      successRate: 1.0,
      averageResponseTime: 0,
      lastHealthCheck: Date.now(),
      errorCount: 0
    };

    this.poolStatuses.set(poolName, status);
  }

  /**
   * 更新连接池状态
   */
  private updatePoolStatus(poolName: string): void {
    const pool = this.pools.get(poolName);
    const status = this.poolStatuses.get(poolName);

    if (!pool || !status) return;

    status.totalConnections = pool.totalCount;
    status.activeConnections = pool.totalCount - pool.idleCount;
    status.idleConnections = pool.idleCount;
    status.waitingConnections = pool.waitingCount;
    
    // 计算成功率
    const totalAttempts = this.connectionStats.successfulConnections + this.connectionStats.failedConnections;
    status.successRate = totalAttempts > 0 ? this.connectionStats.successfulConnections / totalAttempts : 1.0;
  }

  /**
   * 更新响应时间
   */
  private updateResponseTime(poolName: string, responseTime: number): void {
    const status = this.poolStatuses.get(poolName);
    if (!status) return;

    // 使用指数移动平均
    const alpha = 0.1;
    status.averageResponseTime = status.averageResponseTime * (1 - alpha) + responseTime * alpha;
  }

  /**
   * 更新连接时间
   */
  private updateConnectionTime(poolName: string, connectionTime: number): void {
    // 可以在这里记录连接时间统计
    this.logger.debug(`连接池 ${poolName} 连接时间: ${connectionTime}ms`);
  }

  /**
   * 增加错误计数
   */
  private incrementErrorCount(poolName: string): void {
    const status = this.poolStatuses.get(poolName);
    if (status) {
      status.errorCount++;
      
      // 如果错误过多，标记为不健康
      if (status.errorCount > 10) {
        this.markPoolUnhealthy(poolName);
      }
    }
  }

  /**
   * 标记连接池为不健康
   */
  private markPoolUnhealthy(poolName: string): void {
    const status = this.poolStatuses.get(poolName);
    if (status) {
      status.isHealthy = false;
      status.isAvailable = false;
      this.emit('poolUnhealthy', poolName);
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(poolName: string): void {
    const config = this.poolConfigs.get(poolName);
    if (!config) return;

    const timer = setInterval(async () => {
      await this.performHealthCheck(poolName);
    }, config.healthCheckInterval || 30000);

    this.healthCheckTimers.set(poolName, timer);
  }

  /**
   * 停止健康检查
   */
  private stopHealthCheck(poolName: string): void {
    const timer = this.healthCheckTimers.get(poolName);
    if (timer) {
      clearInterval(timer);
      this.healthCheckTimers.delete(poolName);
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(poolName: string): Promise<void> {
    const pool = this.pools.get(poolName);
    const status = this.poolStatuses.get(poolName);

    if (!pool || !status) return;

    try {
      const startTime = Date.now();
      const client = await pool.connect();
      
      try {
        await client.query('SELECT 1');
        
        // 健康检查成功
        status.isHealthy = true;
        status.isAvailable = true;
        status.lastHealthCheck = Date.now();
        status.errorCount = Math.max(0, status.errorCount - 1); // 减少错误计数
        
        // 更新响应时间
        this.updateResponseTime(poolName, Date.now() - startTime);
        
      } finally {
        client.release();
      }
      
    } catch (error) {
      this.logger.error(`连接池 ${poolName} 健康检查失败:`, error);
      this.markPoolUnhealthy(poolName);
    }
  }

  /**
   * 获取所有连接池状态
   */
  public getPoolStatuses(): PoolStatus[] {
    return Array.from(this.poolStatuses.values());
  }

  /**
   * 获取连接统计
   */
  public getConnectionStats(): any {
    return { ...this.connectionStats };
  }

  /**
   * 模块销毁时清理资源
   */
  public async onModuleDestroy(): Promise<void> {
    // 停止所有健康检查
    for (const timer of this.healthCheckTimers.values()) {
      clearInterval(timer);
    }

    if (this.statsTimer) {
      clearInterval(this.statsTimer);
    }

    // 关闭所有连接池
    const closePromises = Array.from(this.pools.values()).map(pool => pool.end());
    await Promise.all(closePromises);

    this.removeAllListeners();
    this.logger.log('连接池管理器已销毁');
  }
}
