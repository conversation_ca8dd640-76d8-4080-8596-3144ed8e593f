/**
 * 资产预览模态框样式
 */
.asset-preview-modal {
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .ant-modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    text-align: left;

    .ant-btn {
      margin-right: 8px;
      border-radius: 6px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }

  .asset-preview-content {
    .preview-section {
      margin-bottom: 24px;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #f0f0f0;
    }

    .info-section {
      h4 {
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }

      .ant-descriptions {
        .ant-descriptions-item-label {
          font-weight: 500;
          color: #666;
        }

        .ant-descriptions-item-content {
          color: #262626;

          a {
            color: #1890ff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }

  .preview-image {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: #fafafa;
    padding: 16px;

    .ant-image {
      max-width: 100%;
      max-height: 400px;
    }
  }

  .preview-model {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: #f5f5f5;
    padding: 24px;

    .model-placeholder {
      text-align: center;
      color: #666;

      p {
        margin-bottom: 16px;
        font-size: 14px;
      }
    }
  }

  .preview-audio {
    padding: 24px;
    background: #fafafa;
    text-align: center;

    audio {
      max-width: 100%;
    }
  }

  .preview-text {
    background: #fafafa;
    padding: 16px;

    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }

  .preview-default {
    padding: 24px;
    text-align: center;
  }

  // 深色主题支持
  &.dark-theme {
    .ant-modal-content {
      background: #1f1f1f;
      color: #fff;
    }

    .ant-modal-header {
      background: #1f1f1f;
      border-color: #434343;

      .ant-modal-title {
        color: #fff;
      }
    }

    .ant-modal-body {
      background: #1f1f1f;
    }

    .ant-modal-footer {
      background: #1f1f1f;
      border-color: #434343;
    }

    .asset-preview-content {
      .preview-section {
        border-color: #434343;
      }

      .info-section {
        h4 {
          color: #fff;
        }

        .ant-descriptions {
          .ant-descriptions-item-label {
            color: #ccc;
          }

          .ant-descriptions-item-content {
            color: #fff;
          }
        }
      }
    }

    .preview-image,
    .preview-model,
    .preview-audio,
    .preview-text {
      background: #2a2a2a;
    }

    .preview-text pre {
      background: #1f1f1f;
      color: #fff;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }

    .ant-modal-body {
      padding: 16px;
    }

    .ant-modal-footer {
      padding: 12px 16px;

      .ant-btn {
        margin-bottom: 8px;
        width: 100%;
        margin-right: 0;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .asset-preview-content {
      .preview-section {
        margin-bottom: 16px;
      }
    }

    .preview-image,
    .preview-model,
    .preview-audio {
      padding: 16px;
    }

    .preview-text {
      padding: 12px;

      pre {
        font-size: 11px;
      }
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    .ant-modal-content {
      border: 2px solid #000;
    }

    .preview-section {
      border: 2px solid #000;
    }

    .ant-btn {
      border: 2px solid #000;

      &.ant-btn-primary {
        background: #0066cc;
        border-color: #0066cc;
      }

      &.ant-btn-danger {
        background: #cc0000;
        border-color: #cc0000;
      }
    }
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    .ant-modal {
      transition: none;
    }

    .ant-btn {
      transition: none;
    }
  }
}
