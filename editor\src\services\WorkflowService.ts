/**
 * 工作流服务
 * 实现操作历史记录、智能操作建议和常用操作流程简化
 */
import { EventEmitter } from 'events';

/**
 * 操作类型枚举
 */
export enum OperationType {
  CREATE = 'create',
  DELETE = 'delete',
  MODIFY = 'modify',
  MOVE = 'move',
  COPY = 'copy',
  PASTE = 'paste',
  UNDO = 'undo',
  REDO = 'redo',
  SELECT = 'select',
  DESELECT = 'deselect',
  IMPORT = 'import',
  EXPORT = 'export',
  SAVE = 'save',
  LOAD = 'load'
}

/**
 * 操作记录接口
 */
export interface OperationRecord {
  /** 操作ID */
  id: string;
  /** 操作类型 */
  type: OperationType;
  /** 操作描述 */
  description: string;
  /** 操作目标 */
  target: string;
  /** 操作参数 */
  params: Record<string, any>;
  /** 操作时间戳 */
  timestamp: number;
  /** 操作用时（毫秒） */
  duration: number;
  /** 操作结果 */
  result: 'success' | 'error' | 'cancelled';
  /** 错误信息 */
  error?: string;
  /** 撤销数据 */
  undoData?: any;
  /** 重做数据 */
  redoData?: any;
}

/**
 * 工作流模式接口
 */
export interface WorkflowPattern {
  /** 模式ID */
  id: string;
  /** 模式名称 */
  name: string;
  /** 模式描述 */
  description: string;
  /** 操作序列 */
  operations: OperationType[];
  /** 使用频率 */
  frequency: number;
  /** 最后使用时间 */
  lastUsed: number;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 智能建议接口
 */
export interface SmartSuggestion {
  /** 建议ID */
  id: string;
  /** 建议类型 */
  type: 'shortcut' | 'workflow' | 'optimization' | 'tip';
  /** 建议标题 */
  title: string;
  /** 建议描述 */
  description: string;
  /** 建议操作 */
  action?: () => void;
  /** 建议优先级 */
  priority: number;
  /** 建议相关性评分 */
  relevance: number;
  /** 是否已显示 */
  shown: boolean;
  /** 是否已接受 */
  accepted: boolean;
}

/**
 * 工作流统计信息
 */
export interface WorkflowStats {
  /** 总操作数 */
  totalOperations: number;
  /** 成功操作数 */
  successfulOperations: number;
  /** 失败操作数 */
  failedOperations: number;
  /** 平均操作时间 */
  averageOperationTime: number;
  /** 最常用操作 */
  mostUsedOperations: { type: OperationType; count: number }[];
  /** 工作流模式数 */
  workflowPatterns: number;
  /** 建议接受率 */
  suggestionAcceptanceRate: number;
}

/**
 * 工作流服务类
 */
export class WorkflowService extends EventEmitter {
  private static instance: WorkflowService | null = null;

  /** 操作历史记录 */
  private operationHistory: OperationRecord[] = [];
  /** 最大历史记录数 */
  private maxHistorySize: number = 1000;
  /** 撤销栈 */
  private undoStack: OperationRecord[] = [];
  /** 重做栈 */
  private redoStack: OperationRecord[] = [];
  /** 最大撤销步数 */
  private maxUndoSteps: number = 50;

  /** 工作流模式 */
  private workflowPatterns: Map<string, WorkflowPattern> = new Map();
  /** 智能建议 */
  private smartSuggestions: SmartSuggestion[] = [];
  /** 操作统计 */
  private operationStats: Map<OperationType, number> = new Map();

  /** 当前操作序列 */
  private currentOperationSequence: OperationType[] = [];
  /** 序列检测窗口大小 */
  private sequenceWindowSize: number = 5;

  /** 是否启用智能建议 */
  private smartSuggestionsEnabled: boolean = true;
  /** 是否启用模式检测 */
  private patternDetectionEnabled: boolean = true;

  /**
   * 获取单例实例
   */
  public static getInstance(): WorkflowService {
    if (!WorkflowService.instance) {
      WorkflowService.instance = new WorkflowService();
    }
    return WorkflowService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.initializeDefaultPatterns();
  }

  /**
   * 初始化默认工作流模式
   */
  private initializeDefaultPatterns(): void {
    const defaultPatterns: WorkflowPattern[] = [
      {
        id: 'create-and-position',
        name: '创建并定位',
        description: '创建对象后立即设置位置',
        operations: [OperationType.CREATE, OperationType.SELECT, OperationType.MODIFY],
        frequency: 0,
        lastUsed: 0,
        enabled: true
      },
      {
        id: 'copy-paste-modify',
        name: '复制粘贴修改',
        description: '复制对象，粘贴后修改属性',
        operations: [OperationType.COPY, OperationType.PASTE, OperationType.MODIFY],
        frequency: 0,
        lastUsed: 0,
        enabled: true
      },
      {
        id: 'select-delete',
        name: '选择删除',
        description: '选择对象后删除',
        operations: [OperationType.SELECT, OperationType.DELETE],
        frequency: 0,
        lastUsed: 0,
        enabled: true
      },
      {
        id: 'import-position-save',
        name: '导入定位保存',
        description: '导入资产，设置位置，保存场景',
        operations: [OperationType.IMPORT, OperationType.MOVE, OperationType.SAVE],
        frequency: 0,
        lastUsed: 0,
        enabled: true
      }
    ];

    defaultPatterns.forEach(pattern => {
      this.workflowPatterns.set(pattern.id, pattern);
    });
  }

  /**
   * 记录操作
   */
  public recordOperation(
    type: OperationType,
    description: string,
    target: string,
    params: Record<string, any> = {},
    undoData?: any,
    redoData?: any
  ): string {
    const operationId = this.generateOperationId();
    const timestamp = Date.now();

    const operation: OperationRecord = {
      id: operationId,
      type,
      description,
      target,
      params,
      timestamp,
      duration: 0,
      result: 'success',
      undoData,
      redoData
    };

    // 添加到历史记录
    this.operationHistory.push(operation);
    
    // 限制历史记录大小
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory.shift();
    }

    // 更新操作统计
    this.updateOperationStats(type);

    // 更新当前操作序列
    this.updateOperationSequence(type);

    // 检测工作流模式
    if (this.patternDetectionEnabled) {
      this.detectWorkflowPatterns();
    }

    // 生成智能建议
    if (this.smartSuggestionsEnabled) {
      this.generateSmartSuggestions();
    }

    this.emit('operationRecorded', operation);
    return operationId;
  }

  /**
   * 完成操作记录
   */
  public completeOperation(
    operationId: string,
    result: 'success' | 'error' | 'cancelled',
    error?: string
  ): void {
    const operation = this.operationHistory.find(op => op.id === operationId);
    if (!operation) return;

    operation.duration = Date.now() - operation.timestamp;
    operation.result = result;
    operation.error = error;

    // 如果操作成功且可撤销，添加到撤销栈
    if (result === 'success' && operation.undoData) {
      this.undoStack.push(operation);
      
      // 限制撤销栈大小
      if (this.undoStack.length > this.maxUndoSteps) {
        this.undoStack.shift();
      }
      
      // 清空重做栈
      this.redoStack.length = 0;
    }

    this.emit('operationCompleted', operation);
  }

  /**
   * 撤销操作
   */
  public undo(): boolean {
    if (this.undoStack.length === 0) return false;

    const operation = this.undoStack.pop()!;
    
    try {
      // 执行撤销逻辑
      this.executeUndo(operation);
      
      // 添加到重做栈
      this.redoStack.push(operation);
      
      // 记录撤销操作
      this.recordOperation(
        OperationType.UNDO,
        `撤销: ${operation.description}`,
        operation.target,
        { originalOperationId: operation.id }
      );

      this.emit('operationUndone', operation);
      return true;
    } catch (error) {
      console.error('Undo failed:', error);
      this.undoStack.push(operation); // 重新放回栈中
      return false;
    }
  }

  /**
   * 重做操作
   */
  public redo(): boolean {
    if (this.redoStack.length === 0) return false;

    const operation = this.redoStack.pop()!;
    
    try {
      // 执行重做逻辑
      this.executeRedo(operation);
      
      // 添加到撤销栈
      this.undoStack.push(operation);
      
      // 记录重做操作
      this.recordOperation(
        OperationType.REDO,
        `重做: ${operation.description}`,
        operation.target,
        { originalOperationId: operation.id }
      );

      this.emit('operationRedone', operation);
      return true;
    } catch (error) {
      console.error('Redo failed:', error);
      this.redoStack.push(operation); // 重新放回栈中
      return false;
    }
  }

  /**
   * 执行撤销逻辑
   */
  private executeUndo(operation: OperationRecord): void {
    // 这里应该根据操作类型执行具体的撤销逻辑
    // 由于这是服务层，具体的撤销逻辑应该由调用方提供
    this.emit('executeUndo', operation);
  }

  /**
   * 执行重做逻辑
   */
  private executeRedo(operation: OperationRecord): void {
    // 这里应该根据操作类型执行具体的重做逻辑
    // 由于这是服务层，具体的重做逻辑应该由调用方提供
    this.emit('executeRedo', operation);
  }

  /**
   * 生成操作ID
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 更新操作统计
   */
  private updateOperationStats(type: OperationType): void {
    const count = this.operationStats.get(type) || 0;
    this.operationStats.set(type, count + 1);
  }

  /**
   * 更新操作序列
   */
  private updateOperationSequence(type: OperationType): void {
    this.currentOperationSequence.push(type);
    
    // 限制序列长度
    if (this.currentOperationSequence.length > this.sequenceWindowSize) {
      this.currentOperationSequence.shift();
    }
  }

  /**
   * 检测工作流模式
   */
  private detectWorkflowPatterns(): void {
    if (this.currentOperationSequence.length < 2) return;

    // 检查是否匹配现有模式
    for (const pattern of this.workflowPatterns.values()) {
      if (this.matchesPattern(this.currentOperationSequence, pattern.operations)) {
        pattern.frequency++;
        pattern.lastUsed = Date.now();
        this.emit('patternDetected', pattern);
      }
    }

    // 检测新模式
    this.detectNewPatterns();
  }

  /**
   * 检查序列是否匹配模式
   */
  private matchesPattern(sequence: OperationType[], pattern: OperationType[]): boolean {
    if (sequence.length < pattern.length) return false;
    
    const startIndex = sequence.length - pattern.length;
    for (let i = 0; i < pattern.length; i++) {
      if (sequence[startIndex + i] !== pattern[i]) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * 检测新模式
   */
  private detectNewPatterns(): void {
    // 简化的新模式检测逻辑
    // 在实际实现中，这里可以使用更复杂的机器学习算法
    if (this.currentOperationSequence.length >= 3) {
      const sequence = [...this.currentOperationSequence];
      const patternId = `auto_${sequence.join('_')}`;
      
      if (!this.workflowPatterns.has(patternId)) {
        const newPattern: WorkflowPattern = {
          id: patternId,
          name: `自动检测模式 ${sequence.join(' → ')}`,
          description: '系统自动检测到的操作模式',
          operations: sequence,
          frequency: 1,
          lastUsed: Date.now(),
          enabled: true
        };
        
        this.workflowPatterns.set(patternId, newPattern);
        this.emit('newPatternDetected', newPattern);
      }
    }
  }

  /**
   * 生成智能建议
   */
  private generateSmartSuggestions(): void {
    this.smartSuggestions = [];

    // 基于操作频率的建议
    this.generateFrequencyBasedSuggestions();

    // 基于工作流模式的建议
    this.generatePatternBasedSuggestions();

    // 基于操作时间的建议
    this.generateTimeBasedSuggestions();

    // 排序建议
    this.smartSuggestions.sort((a, b) => b.priority * b.relevance - a.priority * a.relevance);

    this.emit('suggestionsGenerated', this.smartSuggestions);
  }

  /**
   * 生成基于频率的建议
   */
  private generateFrequencyBasedSuggestions(): void {
    const sortedStats = Array.from(this.operationStats.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);

    for (const [type, count] of sortedStats) {
      if (count >= 5) { // 至少使用5次
        this.smartSuggestions.push({
          id: `freq_${type}`,
          type: 'shortcut',
          title: `为 ${type} 操作设置快捷键`,
          description: `您已经使用了 ${count} 次 ${type} 操作，建议设置快捷键以提高效率`,
          priority: Math.min(count / 10, 5),
          relevance: 0.8,
          shown: false,
          accepted: false
        });
      }
    }
  }

  /**
   * 生成基于模式的建议
   */
  private generatePatternBasedSuggestions(): void {
    const frequentPatterns = Array.from(this.workflowPatterns.values())
      .filter(pattern => pattern.frequency >= 3)
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 2);

    for (const pattern of frequentPatterns) {
      this.smartSuggestions.push({
        id: `pattern_${pattern.id}`,
        type: 'workflow',
        title: `创建 ${pattern.name} 工作流`,
        description: `您经常使用这个操作序列（${pattern.frequency} 次），建议创建自动化工作流`,
        action: () => this.createWorkflowFromPattern(pattern),
        priority: Math.min(pattern.frequency / 5, 5),
        relevance: 0.9,
        shown: false,
        accepted: false
      });
    }
  }

  /**
   * 生成基于时间的建议
   */
  private generateTimeBasedSuggestions(): void {
    const recentOperations = this.operationHistory
      .filter(op => Date.now() - op.timestamp < 300000) // 最近5分钟
      .filter(op => op.duration > 5000); // 耗时超过5秒

    if (recentOperations.length > 0) {
      this.smartSuggestions.push({
        id: 'time_optimization',
        type: 'optimization',
        title: '操作耗时优化建议',
        description: `检测到 ${recentOperations.length} 个耗时较长的操作，建议查看优化建议`,
        priority: 3,
        relevance: 0.7,
        shown: false,
        accepted: false
      });
    }
  }

  /**
   * 从模式创建工作流
   */
  private createWorkflowFromPattern(pattern: WorkflowPattern): void {
    // 这里应该实现创建自动化工作流的逻辑
    this.emit('createWorkflow', pattern);
  }

  /**
   * 获取操作历史
   */
  public getOperationHistory(limit?: number): OperationRecord[] {
    const history = [...this.operationHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * 获取工作流模式
   */
  public getWorkflowPatterns(): WorkflowPattern[] {
    return Array.from(this.workflowPatterns.values())
      .sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * 获取智能建议
   */
  public getSmartSuggestions(): SmartSuggestion[] {
    return this.smartSuggestions.filter(suggestion => !suggestion.shown);
  }

  /**
   * 标记建议为已显示
   */
  public markSuggestionShown(suggestionId: string): void {
    const suggestion = this.smartSuggestions.find(s => s.id === suggestionId);
    if (suggestion) {
      suggestion.shown = true;
      this.emit('suggestionShown', suggestion);
    }
  }

  /**
   * 接受建议
   */
  public acceptSuggestion(suggestionId: string): void {
    const suggestion = this.smartSuggestions.find(s => s.id === suggestionId);
    if (suggestion) {
      suggestion.accepted = true;
      suggestion.action?.();
      this.emit('suggestionAccepted', suggestion);
    }
  }

  /**
   * 拒绝建议
   */
  public rejectSuggestion(suggestionId: string): void {
    const suggestion = this.smartSuggestions.find(s => s.id === suggestionId);
    if (suggestion) {
      this.emit('suggestionRejected', suggestion);
    }
  }

  /**
   * 获取工作流统计
   */
  public getWorkflowStats(): WorkflowStats {
    const totalOperations = this.operationHistory.length;
    const successfulOperations = this.operationHistory.filter(op => op.result === 'success').length;
    const failedOperations = this.operationHistory.filter(op => op.result === 'error').length;

    const totalDuration = this.operationHistory.reduce((sum, op) => sum + op.duration, 0);
    const averageOperationTime = totalOperations > 0 ? totalDuration / totalOperations : 0;

    const mostUsedOperations = Array.from(this.operationStats.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const totalSuggestions = this.smartSuggestions.length;
    const acceptedSuggestions = this.smartSuggestions.filter(s => s.accepted).length;
    const suggestionAcceptanceRate = totalSuggestions > 0 ? acceptedSuggestions / totalSuggestions : 0;

    return {
      totalOperations,
      successfulOperations,
      failedOperations,
      averageOperationTime,
      mostUsedOperations,
      workflowPatterns: this.workflowPatterns.size,
      suggestionAcceptanceRate
    };
  }

  /**
   * 清除历史记录
   */
  public clearHistory(): void {
    this.operationHistory.length = 0;
    this.undoStack.length = 0;
    this.redoStack.length = 0;
    this.operationStats.clear();
    this.currentOperationSequence.length = 0;
    this.emit('historyCleared');
  }

  /**
   * 导出工作流数据
   */
  public exportWorkflowData(): any {
    return {
      patterns: Array.from(this.workflowPatterns.values()),
      stats: Object.fromEntries(this.operationStats),
      settings: {
        maxHistorySize: this.maxHistorySize,
        maxUndoSteps: this.maxUndoSteps,
        smartSuggestionsEnabled: this.smartSuggestionsEnabled,
        patternDetectionEnabled: this.patternDetectionEnabled
      }
    };
  }

  /**
   * 导入工作流数据
   */
  public importWorkflowData(data: any): void {
    if (data.patterns) {
      this.workflowPatterns.clear();
      data.patterns.forEach((pattern: WorkflowPattern) => {
        this.workflowPatterns.set(pattern.id, pattern);
      });
    }

    if (data.stats) {
      this.operationStats.clear();
      Object.entries(data.stats).forEach(([type, count]) => {
        this.operationStats.set(type as OperationType, count as number);
      });
    }

    if (data.settings) {
      this.maxHistorySize = data.settings.maxHistorySize || this.maxHistorySize;
      this.maxUndoSteps = data.settings.maxUndoSteps || this.maxUndoSteps;
      this.smartSuggestionsEnabled = data.settings.smartSuggestionsEnabled !== undefined
        ? data.settings.smartSuggestionsEnabled : this.smartSuggestionsEnabled;
      this.patternDetectionEnabled = data.settings.patternDetectionEnabled !== undefined
        ? data.settings.patternDetectionEnabled : this.patternDetectionEnabled;
    }

    this.emit('workflowDataImported', data);
  }

  /**
   * 设置配置
   */
  public setConfig(config: {
    maxHistorySize?: number;
    maxUndoSteps?: number;
    smartSuggestionsEnabled?: boolean;
    patternDetectionEnabled?: boolean;
  }): void {
    if (config.maxHistorySize !== undefined) {
      this.maxHistorySize = config.maxHistorySize;
    }
    if (config.maxUndoSteps !== undefined) {
      this.maxUndoSteps = config.maxUndoSteps;
    }
    if (config.smartSuggestionsEnabled !== undefined) {
      this.smartSuggestionsEnabled = config.smartSuggestionsEnabled;
    }
    if (config.patternDetectionEnabled !== undefined) {
      this.patternDetectionEnabled = config.patternDetectionEnabled;
    }

    this.emit('configChanged', config);
  }

  /**
   * 获取配置
   */
  public getConfig(): any {
    return {
      maxHistorySize: this.maxHistorySize,
      maxUndoSteps: this.maxUndoSteps,
      smartSuggestionsEnabled: this.smartSuggestionsEnabled,
      patternDetectionEnabled: this.patternDetectionEnabled
    };
  }

  /**
   * 销毁服务
   */
  public dispose(): void {
    this.clearHistory();
    this.workflowPatterns.clear();
    this.smartSuggestions.length = 0;
    this.removeAllListeners();
    WorkflowService.instance = null;
  }
}
