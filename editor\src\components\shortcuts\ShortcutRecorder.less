/**
 * 快捷键录制器样式
 */
.shortcut-recorder {
  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-form {
    .ant-form-item {
      margin-bottom: 20px;

      .ant-form-item-label {
        padding-bottom: 4px;

        label {
          font-weight: 500;
          color: #262626;
        }
      }

      .ant-input,
      .ant-select-selector {
        border-radius: 6px;
        border-color: #d9d9d9;
        transition: all 0.3s;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus,
        &.ant-input-focused,
        &.ant-select-focused .ant-select-selector {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }

  .recording-area {
    margin: 20px 0;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;

    .recording-display {
      margin-bottom: 16px;

      .ant-input {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        background: #fff;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        padding: 12px 16px;
        height: auto;
        transition: all 0.3s;

        &:focus {
          border-color: #1890ff;
          border-style: solid;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &.ant-input-status-error {
          border-color: #ff4d4f;
          background: #fff2f0;
        }

        &.ant-input-status-warning {
          border-color: #faad14;
          background: #fffbe6;
        }

        &::placeholder {
          color: #bfbfbf;
          font-style: italic;
        }
      }

      .ant-input-prefix {
        color: #8c8c8c;
        margin-right: 8px;
      }
    }

    .recording-controls {
      text-align: center;

      .ant-btn {
        border-radius: 6px;
        font-weight: 500;

        &.ant-btn-primary {
          background: #1890ff;
          border-color: #1890ff;
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);

          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
          }
        }

        &.ant-btn-danger {
          background: #ff4d4f;
          border-color: #ff4d4f;
          color: #fff;
          box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);

          &:hover {
            background: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 77, 79, 0.4);
          }
        }

        .anticon {
          margin-right: 6px;
        }
      }
    }

    .ant-alert {
      border-radius: 6px;
      margin-top: 16px;

      &.ant-alert-info {
        background: #e6f7ff;
        border-color: #91d5ff;

        .ant-alert-icon {
          color: #1890ff;
        }
      }

      &.ant-alert-warning {
        background: #fffbe6;
        border-color: #ffe58f;

        .ant-alert-icon {
          color: #faad14;
        }

        .ant-alert-description {
          .ant-tag {
            margin: 2px 4px;
            border-radius: 4px;
          }
        }
      }

      &.ant-alert-error {
        background: #fff2f0;
        border-color: #ffccc7;

        .ant-alert-icon {
          color: #ff4d4f;
        }
      }
    }
  }

  .ant-space {
    &.ant-space-horizontal {
      .ant-space-item {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }

  .ant-switch {
    &.ant-switch-checked {
      background-color: #1890ff;
    }
  }

  .ant-select {
    .ant-select-selector {
      border-radius: 6px;
    }
  }

  // 录制状态动画
  .recording-display {
    .ant-input {
      animation: recording-pulse 1.5s ease-in-out infinite;
    }
  }

  @keyframes recording-pulse {
    0% {
      border-color: #1890ff;
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }
    50% {
      border-color: #40a9ff;
      box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
    }
    100% {
      border-color: #1890ff;
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }

    .ant-modal-body {
      padding: 16px;
    }

    .recording-area {
      padding: 16px;
      margin: 16px 0;
    }

    .ant-space {
      &.ant-space-horizontal {
        flex-direction: column;
        width: 100%;

        .ant-space-item {
          width: 100%;
        }
      }
    }
  }

  // 深色主题支持
  &.dark-theme {
    .ant-modal-content {
      background: #1f1f1f;
      color: #fff;
    }

    .ant-modal-header {
      background: #1f1f1f;
      border-color: #434343;

      .ant-modal-title {
        color: #fff;
      }
    }

    .ant-form {
      .ant-form-item-label label {
        color: #fff;
      }

      .ant-input,
      .ant-select-selector {
        background: #2a2a2a;
        border-color: #434343;
        color: #fff;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus,
        &.ant-input-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
        }
      }
    }

    .recording-area {
      background: #2a2a2a;
      border-color: #434343;

      .recording-display .ant-input {
        background: #1f1f1f;
        border-color: #434343;
        color: #fff;

        &::placeholder {
          color: #666;
        }
      }
    }

    .ant-alert {
      &.ant-alert-info {
        background: #111b26;
        border-color: #1890ff;
        color: #fff;
      }

      &.ant-alert-warning {
        background: #2a2111;
        border-color: #faad14;
        color: #fff;
      }

      &.ant-alert-error {
        background: #2a1f1f;
        border-color: #ff4d4f;
        color: #fff;
      }
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    .ant-modal-content {
      border: 2px solid #000;
    }

    .ant-input,
    .ant-select-selector {
      border: 2px solid #000;

      &:focus,
      &.ant-input-focused {
        border-color: #0066cc;
        box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
      }
    }

    .recording-area {
      border: 2px solid #000;

      .recording-display .ant-input {
        border: 2px solid #000;
      }
    }

    .ant-btn {
      border: 2px solid #000;

      &.ant-btn-primary {
        background: #0066cc;
        border-color: #0066cc;
      }

      &.ant-btn-danger {
        background: #cc0000;
        border-color: #cc0000;
      }
    }
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    .recording-display .ant-input {
      animation: none;
    }

    .ant-btn {
      transition: none;

      &:hover {
        transform: none;
      }
    }
  }
}
