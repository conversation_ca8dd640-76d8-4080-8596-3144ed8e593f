/**
 * 智能建议面板样式
 */
.smart-suggestion-panel {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;

  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;

    .ant-card-head-title {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }

    .ant-card-extra {
      .ant-btn {
        border: none;
        background: transparent;
        color: #8c8c8c;

        &:hover {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.1);
        }
      }
    }
  }

  .ant-card-body {
    padding: 16px;
  }

  .suggestions-list {
    .suggestion-item {
      padding: 12px 0;
      border-bottom: 1px solid #f5f5f5;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #fafafa;
        border-radius: 6px;
        margin: 0 -8px;
        padding-left: 8px;
        padding-right: 8px;
      }

      &.suggestion-shortcut {
        .ant-list-item-meta-avatar .ant-avatar {
          background: #1890ff;
        }
      }

      &.suggestion-workflow {
        .ant-list-item-meta-avatar .ant-avatar {
          background: #52c41a;
        }
      }

      &.suggestion-optimization {
        .ant-list-item-meta-avatar .ant-avatar {
          background: #faad14;
        }
      }

      &.suggestion-tip {
        .ant-list-item-meta-avatar .ant-avatar {
          background: #722ed1;
        }
      }

      .suggestion-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 8px;
      }

      .suggestion-description {
        p {
          margin: 0 0 8px 0;
          color: #666;
          font-size: 13px;
          line-height: 1.5;
        }

        .suggestion-meta {
          font-size: 12px;
          color: #999;

          .ant-space-item {
            &:not(:last-child)::after {
              content: '•';
              margin: 0 8px;
              color: #d9d9d9;
            }
          }
        }
      }

      .ant-list-item-action {
        margin-left: 16px;

        .ant-btn {
          border-radius: 4px;
          font-size: 12px;
          height: 28px;
          padding: 0 12px;

          &.ant-btn-primary {
            background: #1890ff;
            border-color: #1890ff;

            &:hover {
              background: #40a9ff;
              border-color: #40a9ff;
            }
          }

          &.ant-btn-danger {
            &:hover {
              background: #ff7875;
              border-color: #ff7875;
            }
          }
        }
      }
    }
  }

  .ant-tag {
    border-radius: 4px;
    font-size: 11px;
    padding: 2px 6px;
    margin: 0 2px;

    &[data-color="#1890ff"] {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &[data-color="#52c41a"] {
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &[data-color="#faad14"] {
      background: #fffbe6;
      border-color: #ffe58f;
      color: #faad14;
    }

    &[data-color="#722ed1"] {
      background: #f9f0ff;
      border-color: #d3adf7;
      color: #722ed1;
    }

    &.ant-tag-red {
      background: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }

    &.ant-tag-orange {
      background: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }

    &.ant-tag-blue {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }
  }

  .ant-alert {
    border-radius: 6px;
    margin-bottom: 16px;

    &.ant-alert-info {
      background: #e6f7ff;
      border-color: #91d5ff;

      .ant-alert-icon {
        color: #1890ff;
      }
    }
  }

  .ant-empty {
    margin: 20px 0;

    .ant-empty-description {
      color: #999;
      font-size: 13px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .suggestions-list {
      .suggestion-item {
        .ant-list-item-action {
          margin-left: 8px;

          .ant-btn {
            padding: 0 8px;
            font-size: 11px;
            height: 24px;
          }
        }

        .suggestion-title {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
  }

  // 深色主题支持
  &.dark-theme {
    background: #1f1f1f;
    border-color: #434343;

    .ant-card-head {
      background: #1f1f1f;
      border-color: #434343;

      .ant-card-head-title {
        color: #fff;
      }

      .ant-card-extra .ant-btn {
        color: #ccc;

        &:hover {
          color: #40a9ff;
          background: rgba(64, 169, 255, 0.1);
        }
      }
    }

    .ant-card-body {
      background: #1f1f1f;
    }

    .suggestions-list {
      .suggestion-item {
        border-color: #434343;

        &:hover {
          background: #2a2a2a;
        }

        .suggestion-title {
          color: #fff;
        }

        .suggestion-description {
          p {
            color: #ccc;
          }

          .suggestion-meta {
            color: #999;
          }
        }
      }
    }

    .ant-alert {
      &.ant-alert-info {
        background: #111b26;
        border-color: #1890ff;
        color: #fff;
      }
    }

    .ant-empty-description {
      color: #666;
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    border: 2px solid #000;

    .suggestions-list {
      .suggestion-item {
        border-color: #000;

        &:hover {
          background: #f0f0f0;
        }
      }
    }

    .ant-tag {
      border: 1px solid #000;
    }

    .ant-btn {
      border: 2px solid #000;

      &.ant-btn-primary {
        background: #0066cc;
        border-color: #0066cc;
      }

      &.ant-btn-danger {
        background: #cc0000;
        border-color: #cc0000;
      }
    }
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    .suggestion-item {
      transition: none;
    }

    .ant-btn {
      transition: none;
    }
  }

  // 紧凑模式
  &.compact {
    .ant-card-head {
      padding: 8px 12px;

      .ant-card-head-title {
        font-size: 13px;
      }
    }

    .ant-card-body {
      padding: 12px;
    }

    .suggestions-list {
      .suggestion-item {
        padding: 8px 0;

        .suggestion-title {
          font-size: 13px;
        }

        .suggestion-description p {
          font-size: 12px;
        }

        .ant-list-item-action .ant-btn {
          height: 24px;
          padding: 0 8px;
          font-size: 11px;
        }
      }
    }
  }
}
