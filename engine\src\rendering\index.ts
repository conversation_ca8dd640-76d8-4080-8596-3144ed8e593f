/**
 * 渲染模块
 * 导出所有渲染相关的类和接口
 */

// 导出渲染器
export { Renderer } from './Renderer';
export type { RendererOptions } from './Renderer';

// 导出渲染系统
export { RenderSystem } from './RenderSystem';

// 导出相机
export { Camera, CameraType } from './Camera';
export type { CameraOptions } from './Camera';

// 导出光源
export { Light, LightType } from './Light';
export type { LightOptions } from './Light';

// 导出后处理系统
export * from './postprocessing';

// 导出材质系统
export * from './materials';

// 导出光照系统
export * from './lights';

// 导出优化系统
export * from './optimization';

// 导出全局光照系统
export * from './global-illumination';

// 导出体积渲染系统
export * from './volumetric';
