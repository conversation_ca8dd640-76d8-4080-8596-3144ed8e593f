/**
 * 智能批处理系统
 * 实现智能批处理策略、实例化渲染支持和优化绘制调用数量
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { BatchingSystem, BatchingSystemOptions, BatchGroup, BatchingSystemEventType } from './BatchingSystem';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor, PerformanceMetricType } from '../../utils/PerformanceMonitor';

/**
 * 智能批处理策略类型
 */
export enum SmartBatchingStrategy {
  /** 性能优先 */
  PERFORMANCE_FIRST = 'performance_first',
  /** 质量优先 */
  QUALITY_FIRST = 'quality_first',
  /** 平衡模式 */
  BALANCED = 'balanced',
  /** 自适应模式 */
  ADAPTIVE = 'adaptive'
}

/**
 * 批处理分析结果
 */
export interface BatchingAnalysis {
  /** 可批处理的实体组 */
  batchableGroups: Entity[][];
  /** 实例化候选组 */
  instanceCandidates: Entity[][];
  /** 合并候选组 */
  mergeCandidates: Entity[][];
  /** 预期绘制调用减少数量 */
  expectedDrawCallReduction: number;
  /** 预期内存使用变化 */
  expectedMemoryChange: number;
  /** 批处理效率评分 */
  efficiencyScore: number;
}

/**
 * 智能批处理系统配置接口
 */
export interface SmartBatchingSystemOptions extends BatchingSystemOptions {
  /** 智能批处理策略 */
  smartStrategy?: SmartBatchingStrategy;
  /** 是否启用智能分析 */
  useSmartAnalysis?: boolean;
  /** 是否启用动态重组 */
  useDynamicReorganization?: boolean;
  /** 是否启用性能监控 */
  usePerformanceMonitoring?: boolean;
  /** 分析间隔（毫秒） */
  analysisInterval?: number;
  /** 重组阈值 */
  reorganizationThreshold?: number;
  /** 最小批处理效率 */
  minBatchingEfficiency?: number;
  /** 是否启用预测性批处理 */
  usePredictiveBatching?: boolean;
  /** 是否启用层次批处理 */
  useHierarchicalBatching?: boolean;
  /** 目标绘制调用数量 */
  targetDrawCalls?: number;
}

/**
 * 批处理统计信息
 */
export interface SmartBatchingStats {
  /** 总实体数 */
  totalEntities: number;
  /** 批处理组数 */
  batchGroupCount: number;
  /** 实例化批处理数 */
  instancedBatchCount: number;
  /** 合并批处理数 */
  mergedBatchCount: number;
  /** 绘制调用数 */
  drawCalls: number;
  /** 绘制调用减少率 */
  drawCallReduction: number;
  /** 内存使用量（MB） */
  memoryUsage: number;
  /** 批处理效率 */
  batchingEfficiency: number;
  /** 分析时间（毫秒） */
  analysisTime: number;
  /** 重组次数 */
  reorganizationCount: number;
}

/**
 * 智能批处理系统事件类型
 */
export enum SmartBatchingEventType {
  /** 智能分析开始 */
  ANALYSIS_START = 'analysis_start',
  /** 智能分析完成 */
  ANALYSIS_COMPLETE = 'analysis_complete',
  /** 动态重组开始 */
  REORGANIZATION_START = 'reorganization_start',
  /** 动态重组完成 */
  REORGANIZATION_COMPLETE = 'reorganization_complete',
  /** 策略变更 */
  STRATEGY_CHANGED = 'strategy_changed',
  /** 性能阈值触发 */
  PERFORMANCE_THRESHOLD_TRIGGERED = 'performance_threshold_triggered'
}

/**
 * 智能批处理系统
 */
export class SmartBatchingSystem extends BatchingSystem {
  /** 智能批处理策略 */
  private smartStrategy: SmartBatchingStrategy;
  /** 是否启用智能分析 */
  private useSmartAnalysis: boolean;
  /** 是否启用动态重组 */
  private useDynamicReorganization: boolean;
  /** 是否启用性能监控 */
  private usePerformanceMonitoring: boolean;
  /** 分析间隔（毫秒） */
  private analysisInterval: number;
  /** 重组阈值 */
  private reorganizationThreshold: number;
  /** 最小批处理效率 */
  private minBatchingEfficiency: number;
  /** 是否启用预测性批处理 */
  private usePredictiveBatching: boolean;
  /** 是否启用层次批处理 */
  private useHierarchicalBatching: boolean;
  /** 目标绘制调用数量 */
  private targetDrawCalls: number;

  /** 智能事件发射器 */
  private smartEventEmitter: EventEmitter = new EventEmitter();
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();

  /** 上次分析时间 */
  private lastAnalysisTime: number = 0;
  /** 批处理分析结果 */
  private currentAnalysis: BatchingAnalysis | null = null;
  /** 智能批处理统计 */
  private smartStats: SmartBatchingStats = {
    totalEntities: 0,
    batchGroupCount: 0,
    instancedBatchCount: 0,
    mergedBatchCount: 0,
    drawCalls: 0,
    drawCallReduction: 0,
    memoryUsage: 0,
    batchingEfficiency: 0,
    analysisTime: 0,
    reorganizationCount: 0
  };

  /** 性能历史记录 */
  private performanceHistory: number[] = [];
  /** 绘制调用历史记录 */
  private drawCallHistory: number[] = [];

  /** 实体相似度缓存 */
  private similarityCache: Map<string, number> = new Map();
  /** 批处理候选缓存 */
  private candidateCache: Map<string, Entity[]> = new Map();

  /**
   * 创建智能批处理系统
   * @param options 系统配置
   */
  constructor(options: SmartBatchingSystemOptions = {}) {
    super(options);

    // 设置智能批处理选项
    this.smartStrategy = options.smartStrategy || SmartBatchingStrategy.BALANCED;
    this.useSmartAnalysis = options.useSmartAnalysis !== undefined ? options.useSmartAnalysis : true;
    this.useDynamicReorganization = options.useDynamicReorganization !== undefined ? options.useDynamicReorganization : true;
    this.usePerformanceMonitoring = options.usePerformanceMonitoring !== undefined ? options.usePerformanceMonitoring : true;
    this.analysisInterval = options.analysisInterval || 2000; // 2秒
    this.reorganizationThreshold = options.reorganizationThreshold || 0.2; // 20%性能下降
    this.minBatchingEfficiency = options.minBatchingEfficiency || 0.6; // 60%效率
    this.usePredictiveBatching = options.usePredictiveBatching !== undefined ? options.usePredictiveBatching : true;
    this.useHierarchicalBatching = options.useHierarchicalBatching !== undefined ? options.useHierarchicalBatching : true;
    this.targetDrawCalls = options.targetDrawCalls || 100;
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return 'SmartBatchingSystem';
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    if (!this.isEnabled()) {
      return;
    }

    const currentTime = performance.now();

    // 执行智能分析
    if (this.useSmartAnalysis && currentTime - this.lastAnalysisTime >= this.analysisInterval) {
      this.performSmartAnalysis();
      this.lastAnalysisTime = currentTime;
    }

    // 更新性能监控
    if (this.usePerformanceMonitoring) {
      this.updatePerformanceMonitoring();
    }

    // 检查是否需要动态重组
    if (this.useDynamicReorganization && this.shouldReorganize()) {
      this.performDynamicReorganization();
    }
  }

  /**
   * 执行智能分析
   */
  private performSmartAnalysis(): void {
    if (!this.world) {
      return;
    }

    const startTime = performance.now();
    this.smartEventEmitter.emit(SmartBatchingEventType.ANALYSIS_START);

    // 收集所有可批处理的实体
    const entities = this.collectBatchableEntities();
    
    // 分析批处理机会
    this.currentAnalysis = this.analyzeBatchingOpportunities(entities);
    
    // 应用智能策略
    this.applySmartStrategy(this.currentAnalysis);

    // 更新统计信息
    this.smartStats.analysisTime = performance.now() - startTime;
    this.updateSmartStats();

    this.smartEventEmitter.emit(SmartBatchingEventType.ANALYSIS_COMPLETE, this.currentAnalysis);
    
    Debug.log('SmartBatchingSystem', 
      `智能分析完成，用时 ${this.smartStats.analysisTime.toFixed(2)}ms，效率评分: ${this.currentAnalysis.efficiencyScore.toFixed(2)}`);
  }

  /**
   * 收集可批处理的实体
   * @returns 实体列表
   */
  private collectBatchableEntities(): Entity[] {
    if (!this.world) {
      return [];
    }

    const entities: Entity[] = [];
    
    for (const entity of this.world.getEntities().values()) {
      // 检查实体是否有网格组件
      const meshComponent = entity.getComponent('MeshComponent');
      const transform = entity.getComponent('Transform') as Transform;
      
      if (meshComponent && transform) {
        entities.push(entity);
      }
    }

    return entities;
  }

  /**
   * 分析批处理机会
   * @param entities 实体列表
   * @returns 批处理分析结果
   */
  private analyzeBatchingOpportunities(entities: Entity[]): BatchingAnalysis {
    const batchableGroups: Entity[][] = [];
    const instanceCandidates: Entity[][] = [];
    const mergeCandidates: Entity[][] = [];

    // 按相似度分组实体
    const similarityGroups = this.groupEntitiesBySimilarity(entities);
    
    for (const group of similarityGroups) {
      if (group.length < 2) {
        continue; // 单个实体无法批处理
      }

      // 检查是否适合实例化
      if (this.canUseInstancing(group)) {
        instanceCandidates.push(group);
        batchableGroups.push(group);
      }
      // 检查是否适合合并
      else if (this.canUseMerging(group)) {
        mergeCandidates.push(group);
        batchableGroups.push(group);
      }
    }

    // 计算预期效果
    const expectedDrawCallReduction = this.calculateDrawCallReduction(batchableGroups);
    const expectedMemoryChange = this.calculateMemoryChange(batchableGroups);
    const efficiencyScore = this.calculateEfficiencyScore(batchableGroups, expectedDrawCallReduction, expectedMemoryChange);

    return {
      batchableGroups,
      instanceCandidates,
      mergeCandidates,
      expectedDrawCallReduction,
      expectedMemoryChange,
      efficiencyScore
    };
  }

  /**
   * 按相似度分组实体
   * @param entities 实体列表
   * @returns 分组后的实体列表
   */
  private groupEntitiesBySimilarity(entities: Entity[]): Entity[][] {
    const groups: Entity[][] = [];
    const processed = new Set<Entity>();

    for (const entity of entities) {
      if (processed.has(entity)) {
        continue;
      }

      const similarEntities = [entity];
      processed.add(entity);

      // 查找相似的实体
      for (const otherEntity of entities) {
        if (processed.has(otherEntity)) {
          continue;
        }

        const similarity = this.calculateEntitySimilarity(entity, otherEntity);
        if (similarity > 0.8) { // 80%相似度阈值
          similarEntities.push(otherEntity);
          processed.add(otherEntity);
        }
      }

      if (similarEntities.length > 1) {
        groups.push(similarEntities);
      }
    }

    return groups;
  }

  /**
   * 计算实体相似度
   * @param entity1 实体1
   * @param entity2 实体2
   * @returns 相似度（0-1）
   */
  private calculateEntitySimilarity(entity1: Entity, entity2: Entity): number {
    const cacheKey = `${entity1.id}_${entity2.id}`;
    const cachedSimilarity = this.similarityCache.get(cacheKey);
    if (cachedSimilarity !== undefined) {
      return cachedSimilarity;
    }

    let similarity = 0;
    let factors = 0;

    // 检查几何体相似度
    const mesh1 = this.getEntityMesh(entity1);
    const mesh2 = this.getEntityMesh(entity2);

    if (mesh1 && mesh2) {
      if (mesh1.geometry.uuid === mesh2.geometry.uuid) {
        similarity += 0.4; // 几何体相同权重40%
      }
      factors += 0.4;

      // 检查材质相似度
      if (this.areMaterialsSimilar(mesh1.material, mesh2.material)) {
        similarity += 0.3; // 材质相似权重30%
      }
      factors += 0.3;

      // 检查尺寸相似度
      const scale1 = this.getEntityScale(entity1);
      const scale2 = this.getEntityScale(entity2);
      const scaleSimilarity = this.calculateScaleSimilarity(scale1, scale2);
      similarity += scaleSimilarity * 0.2; // 尺寸相似权重20%
      factors += 0.2;

      // 检查位置相近度
      const distance = this.getEntityDistance(entity1, entity2);
      const proximityScore = Math.max(0, 1 - distance / 100); // 100单位内认为相近
      similarity += proximityScore * 0.1; // 位置相近权重10%
      factors += 0.1;
    }

    const finalSimilarity = factors > 0 ? similarity / factors : 0;
    this.similarityCache.set(cacheKey, finalSimilarity);

    return finalSimilarity;
  }

  /**
   * 检查是否可以使用合并
   * @param entities 实体列表
   * @returns 是否可以合并
   */
  private canUseMerging(entities: Entity[]): boolean {
    if (entities.length < 2) {
      return false;
    }

    // 检查所有实体是否都是静态的
    for (const entity of entities) {
      const transform = entity.getComponent('Transform') as Transform;
      if (!transform || this.isEntityDynamic(entity)) {
        return false;
      }
    }

    // 检查几何体复杂度
    const totalVertices = entities.reduce((sum, entity) => {
      const mesh = this.getEntityMesh(entity);
      return sum + (mesh ? this.getGeometryVertexCount(mesh.geometry) : 0);
    }, 0);

    return totalVertices <= this.maxBatchSize * 1000; // 限制总顶点数
  }

  /**
   * 应用智能策略
   * @param analysis 分析结果
   */
  private applySmartStrategy(analysis: BatchingAnalysis): void {
    switch (this.smartStrategy) {
      case SmartBatchingStrategy.PERFORMANCE_FIRST:
        this.applyPerformanceFirstStrategy(analysis);
        break;
      case SmartBatchingStrategy.QUALITY_FIRST:
        this.applyQualityFirstStrategy(analysis);
        break;
      case SmartBatchingStrategy.BALANCED:
        this.applyBalancedStrategy(analysis);
        break;
      case SmartBatchingStrategy.ADAPTIVE:
        this.applyAdaptiveStrategy(analysis);
        break;
    }
  }

  /**
   * 应用性能优先策略
   * @param analysis 分析结果
   */
  private applyPerformanceFirstStrategy(analysis: BatchingAnalysis): void {
    // 优先创建实例化批处理（性能最好）
    for (const group of analysis.instanceCandidates) {
      if (group.length >= 3) { // 至少3个实例才值得
        this.createInstancedBatch(group, `性能优先实例化批处理_${group.length}`);
      }
    }

    // 然后创建合并批处理
    for (const group of analysis.mergeCandidates) {
      if (group.length >= 5) { // 合并需要更多实体才值得
        this.createStaticBatch(group, `性能优先合并批处理_${group.length}`);
      }
    }
  }

  /**
   * 应用质量优先策略
   * @param analysis 分析结果
   */
  private applyQualityFirstStrategy(analysis: BatchingAnalysis): void {
    // 质量优先，只在确保质量的情况下进行批处理
    for (const group of analysis.instanceCandidates) {
      if (group.length >= 5 && this.calculateGroupQualityScore(group) > 0.8) {
        this.createInstancedBatch(group, `质量优先实例化批处理_${group.length}`);
      }
    }

    for (const group of analysis.mergeCandidates) {
      if (group.length >= 8 && this.calculateGroupQualityScore(group) > 0.9) {
        this.createStaticBatch(group, `质量优先合并批处理_${group.length}`);
      }
    }
  }

  /**
   * 应用平衡策略
   * @param analysis 分析结果
   */
  private applyBalancedStrategy(analysis: BatchingAnalysis): void {
    // 平衡性能和质量
    for (const group of analysis.instanceCandidates) {
      if (group.length >= 4) {
        this.createInstancedBatch(group, `平衡实例化批处理_${group.length}`);
      }
    }

    for (const group of analysis.mergeCandidates) {
      if (group.length >= 6) {
        this.createStaticBatch(group, `平衡合并批处理_${group.length}`);
      }
    }
  }

  /**
   * 应用自适应策略
   * @param analysis 分析结果
   */
  private applyAdaptiveStrategy(analysis: BatchingAnalysis): void {
    // 根据当前性能状况动态调整
    const avgFPS = this.calculateAveragePerformance();
    const currentDrawCalls = this.getCurrentDrawCalls();

    if (avgFPS < 30 || currentDrawCalls > this.targetDrawCalls * 1.5) {
      // 性能不足，使用激进批处理
      this.applyPerformanceFirstStrategy(analysis);
      this.smartEventEmitter.emit(SmartBatchingEventType.PERFORMANCE_THRESHOLD_TRIGGERED, 'low', avgFPS);
    } else if (avgFPS > 50 && currentDrawCalls < this.targetDrawCalls * 0.8) {
      // 性能充足，使用质量优先
      this.applyQualityFirstStrategy(analysis);
    } else {
      // 性能适中，使用平衡策略
      this.applyBalancedStrategy(analysis);
    }
  }

  /**
   * 更新性能监控
   */
  private updatePerformanceMonitoring(): void {
    const fpsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.FPS);
    const drawCallsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.DRAW_CALLS);

    if (fpsMetric) {
      this.performanceHistory.push(fpsMetric.value);
      if (this.performanceHistory.length > 60) { // 保留最近60帧
        this.performanceHistory.shift();
      }
    }

    if (drawCallsMetric) {
      this.drawCallHistory.push(drawCallsMetric.value);
      if (this.drawCallHistory.length > 60) {
        this.drawCallHistory.shift();
      }
    }
  }

  /**
   * 检查是否需要重组
   * @returns 是否需要重组
   */
  private shouldReorganize(): boolean {
    if (this.performanceHistory.length < 10) {
      return false;
    }

    // 计算性能下降
    const recentPerformance = this.performanceHistory.slice(-10);
    const oldPerformance = this.performanceHistory.slice(-20, -10);

    if (oldPerformance.length === 0) {
      return false;
    }

    const recentAvg = recentPerformance.reduce((sum, fps) => sum + fps, 0) / recentPerformance.length;
    const oldAvg = oldPerformance.reduce((sum, fps) => sum + fps, 0) / oldPerformance.length;

    const performanceChange = (recentAvg - oldAvg) / oldAvg;

    return performanceChange < -this.reorganizationThreshold;
  }

  /**
   * 执行动态重组
   */
  private performDynamicReorganization(): void {
    this.smartEventEmitter.emit(SmartBatchingEventType.REORGANIZATION_START);

    // 清除现有批处理
    this.clearAllBatches();

    // 重新分析和批处理
    this.performSmartAnalysis();

    this.smartStats.reorganizationCount++;
    this.smartEventEmitter.emit(SmartBatchingEventType.REORGANIZATION_COMPLETE);

    Debug.log('SmartBatchingSystem', '执行动态重组完成');
  }

  /**
   * 清除所有批处理
   */
  private clearAllBatches(): void {
    // 这里应该实现清除所有批处理的逻辑
    // 由于BatchingSystem没有提供清除方法，这里只是示例
    Debug.log('SmartBatchingSystem', '清除所有批处理');
  }

  /**
   * 计算平均性能
   * @returns 平均FPS
   */
  private calculateAveragePerformance(): number {
    if (this.performanceHistory.length === 0) {
      return 60; // 默认值
    }

    const sum = this.performanceHistory.reduce((acc, fps) => acc + fps, 0);
    return sum / this.performanceHistory.length;
  }

  /**
   * 获取当前绘制调用数
   * @returns 绘制调用数
   */
  private getCurrentDrawCalls(): number {
    const drawCallsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.DRAW_CALLS);
    return drawCallsMetric ? drawCallsMetric.value : 0;
  }

  /**
   * 计算绘制调用减少数量
   * @param batchableGroups 可批处理组
   * @returns 减少的绘制调用数
   */
  private calculateDrawCallReduction(batchableGroups: Entity[][]): number {
    let reduction = 0;
    for (const group of batchableGroups) {
      reduction += group.length - 1; // 每组减少 n-1 个绘制调用
    }
    return reduction;
  }

  /**
   * 计算内存变化
   * @param batchableGroups 可批处理组
   * @returns 内存变化（MB）
   */
  private calculateMemoryChange(batchableGroups: Entity[][]): number {
    // 简化计算，实际应该更复杂
    let memoryChange = 0;
    for (const group of batchableGroups) {
      // 实例化通常节省内存，合并可能增加内存
      if (this.canUseInstancing(group)) {
        memoryChange -= group.length * 0.1; // 每个实例节省0.1MB
      } else {
        memoryChange += group.length * 0.05; // 合并可能增加0.05MB
      }
    }
    return memoryChange;
  }

  /**
   * 计算效率评分
   * @param batchableGroups 可批处理组
   * @param drawCallReduction 绘制调用减少数
   * @param memoryChange 内存变化
   * @returns 效率评分（0-1）
   */
  private calculateEfficiencyScore(batchableGroups: Entity[][], drawCallReduction: number, memoryChange: number): number {
    if (batchableGroups.length === 0) {
      return 0;
    }

    // 基于绘制调用减少和内存影响计算效率
    const drawCallScore = Math.min(drawCallReduction / 100, 1); // 最多100个绘制调用减少
    const memoryScore = Math.max(0, 1 + memoryChange / 10); // 内存增加会降低评分

    return (drawCallScore * 0.7 + memoryScore * 0.3);
  }

  /**
   * 获取实体网格
   * @param entity 实体
   * @returns 网格对象
   */
  private getEntityMesh(entity: Entity): THREE.Mesh | null {
    const meshComponent = entity.getComponent('MeshComponent');
    if (meshComponent && typeof (meshComponent as any).getMesh === 'function') {
      const mesh = (meshComponent as any).getMesh();
      return mesh instanceof THREE.Mesh ? mesh : null;
    }
    return null;
  }

  /**
   * 检查材质是否相似
   * @param material1 材质1
   * @param material2 材质2
   * @returns 是否相似
   */
  private areMaterialsSimilar(material1: THREE.Material | THREE.Material[], material2: THREE.Material | THREE.Material[]): boolean {
    // 简化实现，实际应该更复杂
    if (Array.isArray(material1) || Array.isArray(material2)) {
      return false; // 多材质暂不支持
    }

    return material1.type === material2.type;
  }

  /**
   * 获取实体缩放
   * @param entity 实体
   * @returns 缩放向量
   */
  private getEntityScale(entity: Entity): THREE.Vector3 {
    const transform = entity.getComponent('Transform') as Transform;
    return transform ? transform.getScale() : new THREE.Vector3(1, 1, 1);
  }

  /**
   * 计算缩放相似度
   * @param scale1 缩放1
   * @param scale2 缩放2
   * @returns 相似度（0-1）
   */
  private calculateScaleSimilarity(scale1: THREE.Vector3, scale2: THREE.Vector3): number {
    const diff = scale1.clone().sub(scale2);
    const distance = diff.length();
    return Math.max(0, 1 - distance / 2); // 缩放差异在2以内认为相似
  }

  /**
   * 获取实体间距离
   * @param entity1 实体1
   * @param entity2 实体2
   * @returns 距离
   */
  private getEntityDistance(entity1: Entity, entity2: Entity): number {
    const transform1 = entity1.getComponent('Transform') as Transform;
    const transform2 = entity2.getComponent('Transform') as Transform;

    if (!transform1 || !transform2) {
      return Infinity;
    }

    return transform1.getWorldPosition().distanceTo(transform2.getWorldPosition());
  }

  /**
   * 检查实体是否是动态的
   * @param entity 实体
   * @returns 是否动态
   */
  private isEntityDynamic(entity: Entity): boolean {
    // 简化实现，检查是否有动画或物理组件
    return entity.hasComponent('AnimationComponent') || entity.hasComponent('PhysicsComponent');
  }

  /**
   * 获取几何体顶点数
   * @param geometry 几何体
   * @returns 顶点数
   */
  private getGeometryVertexCount(geometry: THREE.BufferGeometry): number {
    const positionAttribute = geometry.getAttribute('position');
    return positionAttribute ? positionAttribute.count : 0;
  }

  /**
   * 计算组质量评分
   * @param group 实体组
   * @returns 质量评分（0-1）
   */
  private calculateGroupQualityScore(group: Entity[]): number {
    // 基于相似度和一致性计算质量评分
    let totalSimilarity = 0;
    let comparisons = 0;

    for (let i = 0; i < group.length; i++) {
      for (let j = i + 1; j < group.length; j++) {
        totalSimilarity += this.calculateEntitySimilarity(group[i], group[j]);
        comparisons++;
      }
    }

    return comparisons > 0 ? totalSimilarity / comparisons : 0;
  }

  /**
   * 更新智能统计信息
   */
  private updateSmartStats(): void {
    if (!this.world) {
      return;
    }

    this.smartStats.totalEntities = this.world.getEntities().size;
    this.smartStats.batchGroupCount = this.getBatchGroups().size;

    let instancedCount = 0;
    let mergedCount = 0;

    for (const group of this.getBatchGroups().values()) {
      if (group.isInstanced) {
        instancedCount++;
      } else {
        mergedCount++;
      }
    }

    this.smartStats.instancedBatchCount = instancedCount;
    this.smartStats.mergedBatchCount = mergedCount;
    this.smartStats.drawCalls = this.getCurrentDrawCalls();

    // 计算绘制调用减少率
    const originalDrawCalls = this.smartStats.totalEntities;
    this.smartStats.drawCallReduction = originalDrawCalls > 0 ?
      (originalDrawCalls - this.smartStats.drawCalls) / originalDrawCalls : 0;

    // 计算批处理效率
    this.smartStats.batchingEfficiency = this.currentAnalysis ?
      this.currentAnalysis.efficiencyScore : 0;
  }

  /**
   * 获取批处理组
   * @returns 批处理组映射
   */
  private getBatchGroups(): Map<string, BatchGroup> {
    // 访问父类的私有属性，这里需要类型断言
    return (this as any).batchGroups || new Map();
  }

  /**
   * 设置智能策略
   * @param strategy 智能策略
   */
  public setSmartStrategy(strategy: SmartBatchingStrategy): void {
    const oldStrategy = this.smartStrategy;
    this.smartStrategy = strategy;
    this.smartEventEmitter.emit(SmartBatchingEventType.STRATEGY_CHANGED, strategy, oldStrategy);
    Debug.log('SmartBatchingSystem', `智能策略变更: ${oldStrategy} -> ${strategy}`);
  }

  /**
   * 获取智能策略
   * @returns 智能策略
   */
  public getSmartStrategy(): SmartBatchingStrategy {
    return this.smartStrategy;
  }

  /**
   * 获取智能统计信息
   * @returns 智能统计信息
   */
  public getSmartStats(): SmartBatchingStats {
    return { ...this.smartStats };
  }

  /**
   * 获取当前分析结果
   * @returns 分析结果
   */
  public getCurrentAnalysis(): BatchingAnalysis | null {
    return this.currentAnalysis;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: (...args: any[]) => void): void {
    this.smartEventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener: (...args: any[]) => void): void {
    this.smartEventEmitter.off(type, listener);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    super.dispose();

    // 清理缓存
    this.similarityCache.clear();
    this.candidateCache.clear();
    this.performanceHistory.length = 0;
    this.drawCallHistory.length = 0;

    // 移除所有事件监听器
    this.smartEventEmitter.removeAllListeners();
  }
}
