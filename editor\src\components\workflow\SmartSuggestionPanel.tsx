/**
 * 智能建议面板组件
 * 显示实时的智能操作建议
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, List, Button, Space, Tag, Avatar, Tooltip, Alert, Empty } from 'antd';
import {
  BulbOutlined, CheckOutlined, CloseOutlined, SettingOutlined,
  ThunderboltOutlined, RocketOutlined, TipOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { WorkflowService, SmartSuggestion } from '../../services/WorkflowService';
import './SmartSuggestionPanel.less';

/**
 * 组件属性接口
 */
interface SmartSuggestionPanelProps {
  /** 是否显示 */
  visible?: boolean;
  /** 最大显示建议数 */
  maxSuggestions?: number;
  /** 是否自动隐藏已接受的建议 */
  autoHideAccepted?: boolean;
  /** 建议接受回调 */
  onSuggestionAccepted?: (suggestion: SmartSuggestion) => void;
  /** 建议拒绝回调 */
  onSuggestionRejected?: (suggestion: SmartSuggestion) => void;
}

/**
 * 建议类型图标映射
 */
const SUGGESTION_TYPE_ICONS = {
  shortcut: <ThunderboltOutlined />,
  workflow: <RocketOutlined />,
  optimization: <SettingOutlined />,
  tip: <TipOutlined />
};

/**
 * 建议类型颜色映射
 */
const SUGGESTION_TYPE_COLORS = {
  shortcut: '#1890ff',
  workflow: '#52c41a',
  optimization: '#faad14',
  tip: '#722ed1'
};

/**
 * 智能建议面板组件
 */
export const SmartSuggestionPanel: React.FC<SmartSuggestionPanelProps> = ({
  visible = true,
  maxSuggestions = 5,
  autoHideAccepted = true,
  onSuggestionAccepted,
  onSuggestionRejected
}) => {
  const { t } = useTranslation();
  const workflowService = useMemo(() => WorkflowService.getInstance(), []);

  // 状态
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]);
  const [isEnabled, setIsEnabled] = useState(true);

  /**
   * 加载建议
   */
  const loadSuggestions = useCallback(() => {
    if (!isEnabled) return;
    
    const newSuggestions = workflowService.getSmartSuggestions()
      .slice(0, maxSuggestions);
    setSuggestions(newSuggestions);
  }, [workflowService, maxSuggestions, isEnabled]);

  /**
   * 处理接受建议
   */
  const handleAcceptSuggestion = useCallback((suggestion: SmartSuggestion) => {
    workflowService.acceptSuggestion(suggestion.id);
    onSuggestionAccepted?.(suggestion);
    
    if (autoHideAccepted) {
      setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
    } else {
      loadSuggestions();
    }
  }, [workflowService, onSuggestionAccepted, autoHideAccepted, loadSuggestions]);

  /**
   * 处理拒绝建议
   */
  const handleRejectSuggestion = useCallback((suggestion: SmartSuggestion) => {
    workflowService.rejectSuggestion(suggestion.id);
    workflowService.markSuggestionShown(suggestion.id);
    onSuggestionRejected?.(suggestion);
    
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
  }, [workflowService, onSuggestionRejected]);

  /**
   * 处理忽略建议
   */
  const handleIgnoreSuggestion = useCallback((suggestion: SmartSuggestion) => {
    workflowService.markSuggestionShown(suggestion.id);
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
  }, [workflowService]);

  /**
   * 获取建议优先级标签
   */
  const getPriorityTag = (priority: number) => {
    if (priority >= 4) {
      return <Tag color="red">高优先级</Tag>;
    } else if (priority >= 2) {
      return <Tag color="orange">中优先级</Tag>;
    } else {
      return <Tag color="blue">低优先级</Tag>;
    }
  };

  /**
   * 渲染建议项
   */
  const renderSuggestionItem = (suggestion: SmartSuggestion) => (
    <List.Item
      key={suggestion.id}
      className={`suggestion-item suggestion-${suggestion.type}`}
      actions={[
        <Tooltip title={t('workflow.acceptSuggestion')} key="accept">
          <Button
            type="primary"
            size="small"
            icon={<CheckOutlined />}
            onClick={() => handleAcceptSuggestion(suggestion)}
          />
        </Tooltip>,
        <Tooltip title={t('workflow.rejectSuggestion')} key="reject">
          <Button
            danger
            size="small"
            icon={<CloseOutlined />}
            onClick={() => handleRejectSuggestion(suggestion)}
          />
        </Tooltip>,
        <Tooltip title={t('workflow.ignoreSuggestion')} key="ignore">
          <Button
            size="small"
            onClick={() => handleIgnoreSuggestion(suggestion)}
          >
            忽略
          </Button>
        </Tooltip>
      ]}
    >
      <List.Item.Meta
        avatar={
          <Avatar
            icon={SUGGESTION_TYPE_ICONS[suggestion.type]}
            style={{ backgroundColor: SUGGESTION_TYPE_COLORS[suggestion.type] }}
          />
        }
        title={
          <div className="suggestion-title">
            <span>{suggestion.title}</span>
            <Space style={{ marginLeft: 8 }}>
              <Tag color={SUGGESTION_TYPE_COLORS[suggestion.type]}>
                {suggestion.type}
              </Tag>
              {getPriorityTag(suggestion.priority)}
            </Space>
          </div>
        }
        description={
          <div className="suggestion-description">
            <p>{suggestion.description}</p>
            <div className="suggestion-meta">
              <Space>
                <span>相关性: {(suggestion.relevance * 100).toFixed(0)}%</span>
                <span>优先级: {suggestion.priority}</span>
              </Space>
            </div>
          </div>
        }
      />
    </List.Item>
  );

  /**
   * 监听建议生成事件
   */
  useEffect(() => {
    const handleSuggestionsGenerated = () => {
      loadSuggestions();
    };

    workflowService.on('suggestionsGenerated', handleSuggestionsGenerated);
    
    return () => {
      workflowService.off('suggestionsGenerated', handleSuggestionsGenerated);
    };
  }, [workflowService, loadSuggestions]);

  /**
   * 初始加载
   */
  useEffect(() => {
    loadSuggestions();
  }, [loadSuggestions]);

  if (!visible || !isEnabled) {
    return null;
  }

  return (
    <Card
      className="smart-suggestion-panel"
      title={
        <Space>
          <BulbOutlined />
          {t('workflow.smartSuggestions')}
          {suggestions.length > 0 && (
            <Tag color="blue">{suggestions.length}</Tag>
          )}
        </Space>
      }
      extra={
        <Space>
          <Tooltip title={t('workflow.toggleSuggestions')}>
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => setIsEnabled(!isEnabled)}
            />
          </Tooltip>
        </Space>
      }
      size="small"
    >
      {suggestions.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('workflow.noActiveSuggestions')}
          style={{ margin: '20px 0' }}
        />
      ) : (
        <>
          <Alert
            message={t('workflow.suggestionsPanelDesc')}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
            closable
          />
          
          <List
            itemLayout="horizontal"
            dataSource={suggestions}
            renderItem={renderSuggestionItem}
            className="suggestions-list"
          />
        </>
      )}
    </Card>
  );
};

export default SmartSuggestionPanel;
