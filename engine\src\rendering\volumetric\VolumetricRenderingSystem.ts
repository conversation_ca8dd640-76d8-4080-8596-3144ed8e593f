/**
 * 体积渲染系统
 * 实现体积光效果、雾效渲染和大气散射
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Scene } from '../../scene/Scene';
import type { Camera } from '../Camera';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 体积渲染质量级别
 */
export enum VolumetricQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  ULTRA = 'ultra'
}

/**
 * 体积光配置
 */
export interface VolumetricLightConfig {
  /** 是否启用体积光 */
  enabled: boolean;
  /** 光线步进数量 */
  steps: number;
  /** 散射强度 */
  scattering: number;
  /** 密度 */
  density: number;
  /** 衰减系数 */
  attenuation: number;
  /** 噪声强度 */
  noiseIntensity: number;
  /** 噪声频率 */
  noiseFrequency: number;
}

/**
 * 雾效配置
 */
export interface FogConfig {
  /** 是否启用雾效 */
  enabled: boolean;
  /** 雾的颜色 */
  color: THREE.Color;
  /** 雾的密度 */
  density: number;
  /** 近距离 */
  near: number;
  /** 远距离 */
  far: number;
  /** 高度衰减 */
  heightFalloff: number;
  /** 风向 */
  windDirection: THREE.Vector3;
  /** 风速 */
  windSpeed: number;
}

/**
 * 大气散射配置
 */
export interface AtmosphericScatteringConfig {
  /** 是否启用大气散射 */
  enabled: boolean;
  /** 瑞利散射系数 */
  rayleighCoefficient: THREE.Vector3;
  /** 米氏散射系数 */
  mieCoefficient: number;
  /** 米氏方向性 */
  mieDirectionalG: number;
  /** 大气厚度 */
  atmosphereThickness: number;
  /** 太阳强度 */
  sunIntensity: number;
  /** 太阳位置 */
  sunPosition: THREE.Vector3;
}

/**
 * 体积渲染系统配置
 */
export interface VolumetricRenderingConfig {
  /** 质量级别 */
  quality: VolumetricQuality;
  /** 体积光配置 */
  volumetricLight: VolumetricLightConfig;
  /** 雾效配置 */
  fog: FogConfig;
  /** 大气散射配置 */
  atmosphericScattering: AtmosphericScatteringConfig;
  /** 是否启用调试模式 */
  debug: boolean;
  /** 性能监控 */
  performanceMonitoring: boolean;
}

/**
 * 体积渲染系统
 */
export class VolumetricRenderingSystem extends System {
  /** 配置 */
  private config: Required<VolumetricRenderingConfig>;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null = null;
  /** 活跃场景 */
  private activeScene: Scene | null = null;
  /** 活跃相机 */
  private activeCamera: Camera | null = null;
  /** 体积光材质 */
  private volumetricLightMaterial: THREE.ShaderMaterial | null = null;
  /** 雾效材质 */
  private fogMaterial: THREE.ShaderMaterial | null = null;
  /** 大气散射材质 */
  private atmosphericScatteringMaterial: THREE.ShaderMaterial | null = null;
  /** 渲染目标 */
  private renderTargets: Map<string, THREE.WebGLRenderTarget> = new Map();
  /** 全屏四边形 */
  private fullscreenQuad: THREE.Mesh;
  /** 正交相机 */
  private orthoCamera: THREE.OrthographicCamera;
  /** 后处理场景 */
  private postProcessScene: THREE.Scene;
  /** 噪声纹理 */
  private noiseTexture: THREE.Texture | null = null;
  /** 时间 */
  private time: number = 0;
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   */
  constructor(config: Partial<VolumetricRenderingConfig> = {}) {
    super();
    
    this.config = {
      quality: VolumetricQuality.MEDIUM,
      volumetricLight: {
        enabled: true,
        steps: 32,
        scattering: 0.1,
        density: 0.5,
        attenuation: 0.8,
        noiseIntensity: 0.1,
        noiseFrequency: 1.0
      },
      fog: {
        enabled: true,
        color: new THREE.Color(0.5, 0.6, 0.7),
        density: 0.01,
        near: 10,
        far: 1000,
        heightFalloff: 0.1,
        windDirection: new THREE.Vector3(1, 0, 0),
        windSpeed: 0.5
      },
      atmosphericScattering: {
        enabled: false,
        rayleighCoefficient: new THREE.Vector3(5.8e-6, 13.5e-6, 33.1e-6),
        mieCoefficient: 21e-6,
        mieDirectionalG: 0.758,
        atmosphereThickness: 1.0,
        sunIntensity: 1000,
        sunPosition: new THREE.Vector3(0, 1, 0)
      },
      debug: false,
      performanceMonitoring: true,
      ...config
    };

    // 创建后处理场景
    this.postProcessScene = new THREE.Scene();
    this.orthoCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
    this.fullscreenQuad = new THREE.Mesh(
      new THREE.PlaneGeometry(2, 2),
      new THREE.MeshBasicMaterial()
    );
    this.postProcessScene.add(this.fullscreenQuad);
  }

  /**
   * 初始化系统
   */
  public async initialize(world: World): Promise<void> {
    await super.initialize(world);
    
    // 获取渲染器
    const renderSystem = world.getSystem('RenderSystem');
    if (renderSystem && 'getRenderer' in renderSystem) {
      const renderer = (renderSystem as any).getRenderer();
      if (renderer && 'getThreeRenderer' in renderer) {
        this.renderer = (renderer as any).getThreeRenderer();
      }
    }

    if (!this.renderer) {
      throw new Error('无法获取WebGL渲染器');
    }

    // 初始化体积渲染系统
    this.initializeVolumetricRendering();
    
    Debug.log('VolumetricRenderingSystem', '体积渲染系统初始化完成');
  }

  /**
   * 初始化体积渲染系统
   */
  private initializeVolumetricRendering(): void {
    // 创建噪声纹理
    this.createNoiseTexture();
    
    // 初始化体积光材质
    if (this.config.volumetricLight.enabled) {
      this.initializeVolumetricLightMaterial();
    }

    // 初始化雾效材质
    if (this.config.fog.enabled) {
      this.initializeFogMaterial();
    }

    // 初始化大气散射材质
    if (this.config.atmosphericScattering.enabled) {
      this.initializeAtmosphericScatteringMaterial();
    }

    // 创建渲染目标
    this.createRenderTargets();
  }

  /**
   * 创建噪声纹理
   */
  private createNoiseTexture(): void {
    const size = 128;
    const data = new Uint8Array(size * size * 4);
    
    for (let i = 0; i < size * size; i++) {
      const stride = i * 4;
      
      // 生成3D噪声
      const x = (i % size) / size;
      const y = Math.floor(i / size) / size;
      
      const noise = this.generateNoise(x * 8, y * 8, 0) * 0.5 + 0.5;
      
      data[stride] = noise * 255;
      data[stride + 1] = noise * 255;
      data[stride + 2] = noise * 255;
      data[stride + 3] = 255;
    }
    
    this.noiseTexture = new THREE.DataTexture(data, size, size, THREE.RGBAFormat);
    this.noiseTexture.wrapS = THREE.RepeatWrapping;
    this.noiseTexture.wrapT = THREE.RepeatWrapping;
    this.noiseTexture.needsUpdate = true;
  }

  /**
   * 生成噪声
   */
  private generateNoise(x: number, y: number, z: number): number {
    // 简化的Perlin噪声实现
    const p = [151,160,137,91,90,15,131,13,201,95,96,53,194,233,7,225,140,36,103,30,69,142,8,99,37,240,21,10,23,190,6,148,247,120,234,75,0,26,197,62,94,252,219,203,117,35,11,32,57,177,33,88,237,149,56,87,174,20,125,136,171,168,68,175,74,165,71,134,139,48,27,166,77,146,158,231,83,111,229,122,60,211,133,230,220,105,92,41,55,46,245,40,244,102,143,54,65,25,63,161,1,216,80,73,209,76,132,187,208,89,18,169,200,196,135,130,116,188,159,86,164,100,109,198,173,186,3,64,52,217,226,250,124,123,5,202,38,147,118,126,255,82,85,212,207,206,59,227,47,16,58,17,182,189,28,42,223,183,170,213,119,248,152,2,44,154,163,70,221,153,101,155,167,43,172,9,129,22,39,253,19,98,108,110,79,113,224,232,178,185,112,104,218,246,97,228,251,34,242,193,238,210,144,12,191,179,162,241,81,51,145,235,249,14,239,107,49,192,214,31,181,199,106,157,184,84,204,176,115,121,50,45,127,4,150,254,138,236,205,93,222,114,67,29,24,72,243,141,128,195,78,66,215,61,156,180];
    
    const fade = (t: number) => t * t * t * (t * (t * 6 - 15) + 10);
    const lerp = (t: number, a: number, b: number) => a + t * (b - a);
    const grad = (hash: number, x: number, y: number, z: number) => {
      const h = hash & 15;
      const u = h < 8 ? x : y;
      const v = h < 4 ? y : h === 12 || h === 14 ? x : z;
      return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
    };
    
    const X = Math.floor(x) & 255;
    const Y = Math.floor(y) & 255;
    const Z = Math.floor(z) & 255;
    
    x -= Math.floor(x);
    y -= Math.floor(y);
    z -= Math.floor(z);
    
    const u = fade(x);
    const v = fade(y);
    const w = fade(z);
    
    const A = p[X] + Y;
    const AA = p[A] + Z;
    const AB = p[A + 1] + Z;
    const B = p[X + 1] + Y;
    const BA = p[B] + Z;
    const BB = p[B + 1] + Z;
    
    return lerp(w, lerp(v, lerp(u, grad(p[AA], x, y, z),
                                   grad(p[BA], x - 1, y, z)),
                          lerp(u, grad(p[AB], x, y - 1, z),
                                   grad(p[BB], x - 1, y - 1, z))),
                   lerp(v, lerp(u, grad(p[AA + 1], x, y, z - 1),
                                   grad(p[BA + 1], x - 1, y, z - 1)),
                          lerp(u, grad(p[AB + 1], x, y - 1, z - 1),
                                   grad(p[BB + 1], x - 1, y - 1, z - 1))));
  }

  /**
   * 初始化体积光材质
   */
  private initializeVolumetricLightMaterial(): void {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform sampler2D tDiffuse;
      uniform sampler2D tDepth;
      uniform sampler2D tNoise;
      uniform vec3 cameraPosition;
      uniform mat4 cameraMatrixWorld;
      uniform mat4 projectionMatrix;
      uniform vec3 lightPosition;
      uniform vec3 lightColor;
      uniform float lightIntensity;
      uniform int steps;
      uniform float scattering;
      uniform float density;
      uniform float attenuation;
      uniform float noiseIntensity;
      uniform float noiseFrequency;
      uniform float time;

      varying vec2 vUv;

      // 重建世界空间位置
      vec3 reconstructWorldPosition(vec2 uv, float depth) {
        vec4 clipSpacePos = vec4(uv * 2.0 - 1.0, depth * 2.0 - 1.0, 1.0);
        vec4 viewSpacePos = inverse(projectionMatrix) * clipSpacePos;
        viewSpacePos /= viewSpacePos.w;
        vec4 worldSpacePos = cameraMatrixWorld * viewSpacePos;
        return worldSpacePos.xyz;
      }

      // 计算体积光
      vec3 calculateVolumetricLight(vec3 rayStart, vec3 rayEnd) {
        vec3 rayDir = rayEnd - rayStart;
        float rayLength = length(rayDir);
        rayDir = normalize(rayDir);

        float stepSize = rayLength / float(steps);
        vec3 step = rayDir * stepSize;

        vec3 currentPos = rayStart;
        vec3 scatteredLight = vec3(0.0);

        for(int i = 0; i < steps; i++) {
          // 计算到光源的距离
          vec3 lightDir = lightPosition - currentPos;
          float lightDistance = length(lightDir);
          lightDir = normalize(lightDir);

          // 计算衰减
          float distanceAttenuation = 1.0 / (1.0 + attenuation * lightDistance * lightDistance);

          // 采样噪声
          vec3 noiseCoord = currentPos * noiseFrequency + vec3(time * 0.1);
          float noise = texture2D(tNoise, noiseCoord.xy).r;
          noise = noise * 2.0 - 1.0;

          // 计算密度
          float currentDensity = density * (1.0 + noise * noiseIntensity);

          // 计算散射
          float phase = 1.0; // 简化的相位函数

          // 累积散射光
          vec3 lightContribution = lightColor * lightIntensity * scattering * currentDensity * phase * distanceAttenuation * stepSize;
          scatteredLight += lightContribution;

          currentPos += step;
        }

        return scatteredLight;
      }

      void main() {
        vec4 diffuse = texture2D(tDiffuse, vUv);
        float depth = texture2D(tDepth, vUv).r;

        // 重建世界空间位置
        vec3 worldPos = reconstructWorldPosition(vUv, depth);

        // 计算体积光
        vec3 volumetricLight = calculateVolumetricLight(cameraPosition, worldPos);

        gl_FragColor = vec4(diffuse.rgb + volumetricLight, diffuse.a);
      }
    `;

    this.volumetricLightMaterial = new THREE.ShaderMaterial({
      uniforms: {
        tDiffuse: { value: null },
        tDepth: { value: null },
        tNoise: { value: this.noiseTexture },
        cameraPosition: { value: new THREE.Vector3() },
        cameraMatrixWorld: { value: new THREE.Matrix4() },
        projectionMatrix: { value: new THREE.Matrix4() },
        lightPosition: { value: new THREE.Vector3() },
        lightColor: { value: new THREE.Vector3(1, 1, 1) },
        lightIntensity: { value: 1.0 },
        steps: { value: this.config.volumetricLight.steps },
        scattering: { value: this.config.volumetricLight.scattering },
        density: { value: this.config.volumetricLight.density },
        attenuation: { value: this.config.volumetricLight.attenuation },
        noiseIntensity: { value: this.config.volumetricLight.noiseIntensity },
        noiseFrequency: { value: this.config.volumetricLight.noiseFrequency },
        time: { value: 0 }
      },
      vertexShader,
      fragmentShader
    });
  }

  /**
   * 初始化雾效材质
   */
  private initializeFogMaterial(): void {
    const vertexShader = `
      varying vec2 vUv;
      varying vec3 vWorldPosition;

      void main() {
        vUv = uv;
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform sampler2D tDiffuse;
      uniform sampler2D tDepth;
      uniform sampler2D tNoise;
      uniform vec3 cameraPosition;
      uniform vec3 fogColor;
      uniform float fogDensity;
      uniform float fogNear;
      uniform float fogFar;
      uniform float heightFalloff;
      uniform vec3 windDirection;
      uniform float windSpeed;
      uniform float time;

      varying vec2 vUv;
      varying vec3 vWorldPosition;

      float calculateFogFactor(float distance, float height) {
        // 距离雾
        float distanceFog = 1.0 - exp(-fogDensity * distance);

        // 高度雾
        float heightFog = exp(-heightFalloff * max(0.0, height));

        return distanceFog * heightFog;
      }

      vec3 calculateFogWithWind(vec3 worldPos) {
        // 计算风的影响
        vec3 windOffset = windDirection * windSpeed * time;
        vec3 noiseCoord = (worldPos + windOffset) * 0.01;

        float noise = texture2D(tNoise, noiseCoord.xy).r;
        noise = noise * 2.0 - 1.0;

        return fogColor * (1.0 + noise * 0.2);
      }

      void main() {
        vec4 diffuse = texture2D(tDiffuse, vUv);
        float depth = texture2D(tDepth, vUv).r;

        // 计算距离和高度
        float distance = length(vWorldPosition - cameraPosition);
        float height = vWorldPosition.y;

        // 计算雾因子
        float fogFactor = calculateFogFactor(distance, height);
        fogFactor = smoothstep(fogNear, fogFar, fogFactor);

        // 计算带风的雾颜色
        vec3 finalFogColor = calculateFogWithWind(vWorldPosition);

        // 混合雾效
        vec3 finalColor = mix(diffuse.rgb, finalFogColor, fogFactor);

        gl_FragColor = vec4(finalColor, diffuse.a);
      }
    `;

    this.fogMaterial = new THREE.ShaderMaterial({
      uniforms: {
        tDiffuse: { value: null },
        tDepth: { value: null },
        tNoise: { value: this.noiseTexture },
        cameraPosition: { value: new THREE.Vector3() },
        fogColor: { value: this.config.fog.color },
        fogDensity: { value: this.config.fog.density },
        fogNear: { value: this.config.fog.near },
        fogFar: { value: this.config.fog.far },
        heightFalloff: { value: this.config.fog.heightFalloff },
        windDirection: { value: this.config.fog.windDirection },
        windSpeed: { value: this.config.fog.windSpeed },
        time: { value: 0 }
      },
      vertexShader,
      fragmentShader
    });
  }

  /**
   * 初始化大气散射材质
   */
  private initializeAtmosphericScatteringMaterial(): void {
    const vertexShader = `
      varying vec2 vUv;
      varying vec3 vWorldPosition;
      varying vec3 vViewDirection;

      void main() {
        vUv = uv;
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        vViewDirection = normalize(worldPosition.xyz - cameraPosition);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform sampler2D tDiffuse;
      uniform sampler2D tDepth;
      uniform vec3 cameraPosition;
      uniform vec3 sunPosition;
      uniform float sunIntensity;
      uniform vec3 rayleighCoefficient;
      uniform float mieCoefficient;
      uniform float mieDirectionalG;
      uniform float atmosphereThickness;

      varying vec2 vUv;
      varying vec3 vWorldPosition;
      varying vec3 vViewDirection;

      // 瑞利相位函数
      float rayleighPhase(float cosTheta) {
        return 3.0 / (16.0 * 3.14159265) * (1.0 + cosTheta * cosTheta);
      }

      // 米氏相位函数
      float miePhase(float cosTheta, float g) {
        float g2 = g * g;
        float phase = 1.0 - g2;
        phase /= (2.0 + g2);
        phase /= pow(1.0 + g2 - 2.0 * g * cosTheta, 1.5);
        return phase / (4.0 * 3.14159265);
      }

      // 计算大气散射
      vec3 calculateAtmosphericScattering(vec3 viewDir, vec3 sunDir) {
        float cosTheta = dot(viewDir, sunDir);

        // 计算光学深度
        float opticalDepth = atmosphereThickness;

        // 瑞利散射
        vec3 rayleighScattering = rayleighCoefficient * rayleighPhase(cosTheta);
        rayleighScattering *= exp(-rayleighCoefficient * opticalDepth);

        // 米氏散射
        float mieScattering = mieCoefficient * miePhase(cosTheta, mieDirectionalG);
        mieScattering *= exp(-mieCoefficient * opticalDepth);

        // 组合散射
        vec3 totalScattering = rayleighScattering + vec3(mieScattering);

        return totalScattering * sunIntensity;
      }

      void main() {
        vec4 diffuse = texture2D(tDiffuse, vUv);

        vec3 sunDir = normalize(sunPosition);
        vec3 scattering = calculateAtmosphericScattering(vViewDirection, sunDir);

        gl_FragColor = vec4(diffuse.rgb + scattering, diffuse.a);
      }
    `;

    this.atmosphericScatteringMaterial = new THREE.ShaderMaterial({
      uniforms: {
        tDiffuse: { value: null },
        tDepth: { value: null },
        cameraPosition: { value: new THREE.Vector3() },
        sunPosition: { value: this.config.atmosphericScattering.sunPosition },
        sunIntensity: { value: this.config.atmosphericScattering.sunIntensity },
        rayleighCoefficient: { value: this.config.atmosphericScattering.rayleighCoefficient },
        mieCoefficient: { value: this.config.atmosphericScattering.mieCoefficient },
        mieDirectionalG: { value: this.config.atmosphericScattering.mieDirectionalG },
        atmosphereThickness: { value: this.config.atmosphericScattering.atmosphereThickness }
      },
      vertexShader,
      fragmentShader
    });
  }

  /**
   * 创建渲染目标
   */
  private createRenderTargets(): void {
    const size = this.getRenderTargetSize();

    const options = {
      format: THREE.RGBAFormat,
      type: THREE.FloatType,
      minFilter: THREE.LinearFilter,
      magFilter: THREE.LinearFilter,
      generateMipmaps: false
    };

    // 体积光渲染目标
    if (this.config.volumetricLight.enabled) {
      this.renderTargets.set('volumetricLight', new THREE.WebGLRenderTarget(size.width, size.height, options));
    }

    // 雾效渲染目标
    if (this.config.fog.enabled) {
      this.renderTargets.set('fog', new THREE.WebGLRenderTarget(size.width, size.height, options));
    }

    // 大气散射渲染目标
    if (this.config.atmosphericScattering.enabled) {
      this.renderTargets.set('atmosphericScattering', new THREE.WebGLRenderTarget(size.width, size.height, options));
    }
  }

  /**
   * 获取渲染目标大小
   */
  private getRenderTargetSize(): { width: number; height: number } {
    if (!this.renderer) return { width: 512, height: 512 };

    const canvas = this.renderer.domElement;
    const baseWidth = canvas.width;
    const baseHeight = canvas.height;

    switch (this.config.quality) {
      case VolumetricQuality.LOW:
        return { width: baseWidth * 0.25, height: baseHeight * 0.25 };
      case VolumetricQuality.MEDIUM:
        return { width: baseWidth * 0.5, height: baseHeight * 0.5 };
      case VolumetricQuality.HIGH:
        return { width: baseWidth * 0.75, height: baseHeight * 0.75 };
      case VolumetricQuality.ULTRA:
        return { width: baseWidth, height: baseHeight };
      default:
        return { width: baseWidth * 0.5, height: baseHeight * 0.5 };
    }
  }

  /**
   * 设置活跃场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;
  }

  /**
   * 设置活跃相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 更新系统
   */
  public update(deltaTime: number): void {
    if (!this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }

    this.time += deltaTime;

    // 性能监控开始
    const startTime = this.config.performanceMonitoring ? performance.now() : 0;

    // 更新体积光
    if (this.config.volumetricLight.enabled && this.volumetricLightMaterial) {
      this.updateVolumetricLight();
    }

    // 更新雾效
    if (this.config.fog.enabled && this.fogMaterial) {
      this.updateFog();
    }

    // 更新大气散射
    if (this.config.atmosphericScattering.enabled && this.atmosphericScatteringMaterial) {
      this.updateAtmosphericScattering();
    }

    // 性能监控结束
    if (this.config.performanceMonitoring) {
      const endTime = performance.now();
      this.performanceMonitor.recordMetric('volumetric_update_time', endTime - startTime);
    }

    // 发出更新事件
    this.eventEmitter.emit('volumetricUpdated', {
      deltaTime,
      time: this.time
    });
  }

  /**
   * 更新体积光
   */
  private updateVolumetricLight(): void {
    if (!this.volumetricLightMaterial || !this.activeCamera) return;

    const camera = this.activeCamera.getThreeCamera();

    // 更新相机相关uniforms
    this.volumetricLightMaterial.uniforms.cameraPosition.value.copy(camera.position);
    this.volumetricLightMaterial.uniforms.cameraMatrixWorld.value.copy(camera.matrixWorld);
    this.volumetricLightMaterial.uniforms.projectionMatrix.value.copy(camera.projectionMatrix);
    this.volumetricLightMaterial.uniforms.time.value = this.time;

    // 更新光源信息（使用场景中的第一个方向光）
    if (this.activeScene) {
      const threeScene = this.activeScene.getThreeScene();
      threeScene.traverse((object) => {
        if (object instanceof THREE.DirectionalLight) {
          this.volumetricLightMaterial!.uniforms.lightPosition.value.copy(object.position);
          this.volumetricLightMaterial!.uniforms.lightColor.value.copy(object.color);
          this.volumetricLightMaterial!.uniforms.lightIntensity.value = object.intensity;
        }
      });
    }
  }

  /**
   * 更新雾效
   */
  private updateFog(): void {
    if (!this.fogMaterial || !this.activeCamera) return;

    const camera = this.activeCamera.getThreeCamera();

    // 更新相机位置和时间
    this.fogMaterial.uniforms.cameraPosition.value.copy(camera.position);
    this.fogMaterial.uniforms.time.value = this.time;
  }

  /**
   * 更新大气散射
   */
  private updateAtmosphericScattering(): void {
    if (!this.atmosphericScatteringMaterial || !this.activeCamera) return;

    const camera = this.activeCamera.getThreeCamera();

    // 更新相机位置
    this.atmosphericScatteringMaterial.uniforms.cameraPosition.value.copy(camera.position);
  }

  /**
   * 渲染体积效果
   */
  public render(
    inputTextures: {
      diffuse: THREE.Texture;
      depth: THREE.Texture;
    }
  ): THREE.Texture {
    if (!this.renderer) return inputTextures.diffuse;

    let currentTexture = inputTextures.diffuse;

    // 渲染体积光
    if (this.config.volumetricLight.enabled && this.volumetricLightMaterial) {
      currentTexture = this.renderVolumetricLight(currentTexture, inputTextures.depth);
    }

    // 渲染雾效
    if (this.config.fog.enabled && this.fogMaterial) {
      currentTexture = this.renderFog(currentTexture, inputTextures.depth);
    }

    // 渲染大气散射
    if (this.config.atmosphericScattering.enabled && this.atmosphericScatteringMaterial) {
      currentTexture = this.renderAtmosphericScattering(currentTexture, inputTextures.depth);
    }

    return currentTexture;
  }

  /**
   * 渲染体积光
   */
  private renderVolumetricLight(diffuseTexture: THREE.Texture, depthTexture: THREE.Texture): THREE.Texture {
    if (!this.volumetricLightMaterial || !this.renderer) return diffuseTexture;

    const target = this.renderTargets.get('volumetricLight');
    if (!target) return diffuseTexture;

    // 更新材质uniforms
    this.volumetricLightMaterial.uniforms.tDiffuse.value = diffuseTexture;
    this.volumetricLightMaterial.uniforms.tDepth.value = depthTexture;

    // 渲染
    this.fullscreenQuad.material = this.volumetricLightMaterial;
    const originalTarget = this.renderer.getRenderTarget();
    this.renderer.setRenderTarget(target);
    this.renderer.render(this.postProcessScene, this.orthoCamera);
    this.renderer.setRenderTarget(originalTarget);

    return target.texture;
  }

  /**
   * 渲染雾效
   */
  private renderFog(diffuseTexture: THREE.Texture, depthTexture: THREE.Texture): THREE.Texture {
    if (!this.fogMaterial || !this.renderer) return diffuseTexture;

    const target = this.renderTargets.get('fog');
    if (!target) return diffuseTexture;

    // 更新材质uniforms
    this.fogMaterial.uniforms.tDiffuse.value = diffuseTexture;
    this.fogMaterial.uniforms.tDepth.value = depthTexture;

    // 渲染
    this.fullscreenQuad.material = this.fogMaterial;
    const originalTarget = this.renderer.getRenderTarget();
    this.renderer.setRenderTarget(target);
    this.renderer.render(this.postProcessScene, this.orthoCamera);
    this.renderer.setRenderTarget(originalTarget);

    return target.texture;
  }

  /**
   * 渲染大气散射
   */
  private renderAtmosphericScattering(diffuseTexture: THREE.Texture, depthTexture: THREE.Texture): THREE.Texture {
    if (!this.atmosphericScatteringMaterial || !this.renderer) return diffuseTexture;

    const target = this.renderTargets.get('atmosphericScattering');
    if (!target) return diffuseTexture;

    // 更新材质uniforms
    this.atmosphericScatteringMaterial.uniforms.tDiffuse.value = diffuseTexture;
    this.atmosphericScatteringMaterial.uniforms.tDepth.value = depthTexture;

    // 渲染
    this.fullscreenQuad.material = this.atmosphericScatteringMaterial;
    const originalTarget = this.renderer.getRenderTarget();
    this.renderer.setRenderTarget(target);
    this.renderer.render(this.postProcessScene, this.orthoCamera);
    this.renderer.setRenderTarget(originalTarget);

    return target.texture;
  }

  /**
   * 设置体积渲染质量
   */
  public setQuality(quality: VolumetricQuality): void {
    this.config.quality = quality;

    // 更新渲染目标大小
    const size = this.getRenderTargetSize();
    this.renderTargets.forEach(target => {
      target.setSize(size.width, size.height);
    });

    Debug.log('VolumetricRenderingSystem', `体积渲染质量设置为: ${quality}`);
  }

  /**
   * 启用/禁用体积光
   */
  public setVolumetricLightEnabled(enabled: boolean): void {
    this.config.volumetricLight.enabled = enabled;

    if (enabled && !this.volumetricLightMaterial) {
      this.initializeVolumetricLightMaterial();
    }

    Debug.log('VolumetricRenderingSystem', `体积光${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 启用/禁用雾效
   */
  public setFogEnabled(enabled: boolean): void {
    this.config.fog.enabled = enabled;

    if (enabled && !this.fogMaterial) {
      this.initializeFogMaterial();
    }

    Debug.log('VolumetricRenderingSystem', `雾效${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 启用/禁用大气散射
   */
  public setAtmosphericScatteringEnabled(enabled: boolean): void {
    this.config.atmosphericScattering.enabled = enabled;

    if (enabled && !this.atmosphericScatteringMaterial) {
      this.initializeAtmosphericScatteringMaterial();
    }

    Debug.log('VolumetricRenderingSystem', `大气散射${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 设置雾的颜色
   */
  public setFogColor(color: THREE.Color): void {
    this.config.fog.color.copy(color);

    if (this.fogMaterial) {
      this.fogMaterial.uniforms.fogColor.value.copy(color);
    }
  }

  /**
   * 设置雾的密度
   */
  public setFogDensity(density: number): void {
    this.config.fog.density = density;

    if (this.fogMaterial) {
      this.fogMaterial.uniforms.fogDensity.value = density;
    }
  }

  /**
   * 设置太阳位置
   */
  public setSunPosition(position: THREE.Vector3): void {
    this.config.atmosphericScattering.sunPosition.copy(position);

    if (this.atmosphericScatteringMaterial) {
      this.atmosphericScatteringMaterial.uniforms.sunPosition.value.copy(position);
    }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): any {
    return {
      quality: this.config.quality,
      volumetricLightEnabled: this.config.volumetricLight.enabled,
      fogEnabled: this.config.fog.enabled,
      atmosphericScatteringEnabled: this.config.atmosphericScattering.enabled,
      volumetricLightSteps: this.config.volumetricLight.steps,
      renderTargetCount: this.renderTargets.size
    };
  }

  /**
   * 监听事件
   */
  public on(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   */
  public off(event: string, callback: Function): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清理材质
    if (this.volumetricLightMaterial) {
      this.volumetricLightMaterial.dispose();
      this.volumetricLightMaterial = null;
    }

    if (this.fogMaterial) {
      this.fogMaterial.dispose();
      this.fogMaterial = null;
    }

    if (this.atmosphericScatteringMaterial) {
      this.atmosphericScatteringMaterial.dispose();
      this.atmosphericScatteringMaterial = null;
    }

    // 清理渲染目标
    this.renderTargets.forEach(target => {
      target.dispose();
    });
    this.renderTargets.clear();

    // 清理噪声纹理
    if (this.noiseTexture) {
      this.noiseTexture.dispose();
      this.noiseTexture = null;
    }

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    Debug.log('VolumetricRenderingSystem', '体积渲染系统已销毁');
  }
}
