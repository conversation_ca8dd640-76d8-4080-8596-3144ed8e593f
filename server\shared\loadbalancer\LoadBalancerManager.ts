/**
 * 负载均衡管理器
 * 实现多种负载均衡算法、服务发现和健康检查集成
 */
import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';

/**
 * 负载均衡算法枚举
 */
export enum LoadBalanceAlgorithm {
  ROUND_ROBIN = 'round_robin',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  WEIGHTED_LEAST_CONNECTIONS = 'weighted_least_connections',
  IP_HASH = 'ip_hash',
  CONSISTENT_HASH = 'consistent_hash',
  RANDOM = 'random',
  WEIGHTED_RANDOM = 'weighted_random'
}

/**
 * 服务实例接口
 */
export interface ServiceInstance {
  /** 实例ID */
  id: string;
  /** 服务名称 */
  serviceName: string;
  /** 主机地址 */
  host: string;
  /** 端口 */
  port: number;
  /** 权重 */
  weight: number;
  /** 是否健康 */
  healthy: boolean;
  /** 当前连接数 */
  connections: number;
  /** 响应时间 */
  responseTime: number;
  /** 最后健康检查时间 */
  lastHealthCheck: number;
  /** 元数据 */
  metadata: Record<string, any>;
}

/**
 * 负载均衡器配置
 */
export interface LoadBalancerConfig {
  /** 负载均衡器名称 */
  name: string;
  /** 目标服务名称 */
  targetService: string;
  /** 负载均衡算法 */
  algorithm: LoadBalanceAlgorithm;
  /** 健康检查配置 */
  healthCheck: {
    enabled: boolean;
    interval: number;
    timeout: number;
    path?: string;
    expectedStatus?: number;
  };
  /** 故障转移配置 */
  failover: {
    enabled: boolean;
    maxRetries: number;
    retryInterval: number;
  };
  /** 会话保持配置 */
  sessionAffinity: {
    enabled: boolean;
    cookieName?: string;
    timeout?: number;
  };
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 负载均衡统计
 */
export interface LoadBalancerStats {
  /** 负载均衡器名称 */
  name: string;
  /** 总请求数 */
  totalRequests: number;
  /** 成功请求数 */
  successfulRequests: number;
  /** 失败请求数 */
  failedRequests: number;
  /** 平均响应时间 */
  averageResponseTime: number;
  /** 当前活跃连接数 */
  activeConnections: number;
  /** 健康实例数 */
  healthyInstances: number;
  /** 总实例数 */
  totalInstances: number;
  /** 最后更新时间 */
  lastUpdated: number;
}

/**
 * 负载均衡管理器
 */
@Injectable()
export class LoadBalancerManager extends EventEmitter implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(LoadBalancerManager.name);

  /** 负载均衡器配置映射 */
  private loadBalancers: Map<string, LoadBalancerConfig> = new Map();
  /** 服务实例映射 */
  private serviceInstances: Map<string, ServiceInstance[]> = new Map();
  /** 轮询计数器 */
  private roundRobinCounters: Map<string, number> = new Map();
  /** 会话映射 */
  private sessionMap: Map<string, string> = new Map();
  /** 负载均衡统计 */
  private stats: Map<string, LoadBalancerStats> = new Map();

  /** 健康检查定时器 */
  private healthCheckTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor(private readonly configService: ConfigService) {
    super();
  }

  /**
   * 模块初始化
   */
  public async onModuleInit(): Promise<void> {
    await this.initializeDefaultLoadBalancers();
    this.logger.log('负载均衡管理器已启动');
  }

  /**
   * 初始化默认负载均衡器
   */
  private async initializeDefaultLoadBalancers(): Promise<void> {
    // API网关负载均衡器
    this.registerLoadBalancer({
      name: 'api_gateway_lb',
      targetService: 'api_gateway',
      algorithm: LoadBalanceAlgorithm.WEIGHTED_ROUND_ROBIN,
      healthCheck: {
        enabled: true,
        interval: 30000,
        timeout: 5000,
        path: '/health',
        expectedStatus: 200
      },
      failover: {
        enabled: true,
        maxRetries: 3,
        retryInterval: 1000
      },
      sessionAffinity: {
        enabled: false
      },
      enabled: true
    });

    // 数据库负载均衡器
    this.registerLoadBalancer({
      name: 'database_lb',
      targetService: 'database',
      algorithm: LoadBalanceAlgorithm.LEAST_CONNECTIONS,
      healthCheck: {
        enabled: true,
        interval: 60000,
        timeout: 10000
      },
      failover: {
        enabled: true,
        maxRetries: 2,
        retryInterval: 2000
      },
      sessionAffinity: {
        enabled: false
      },
      enabled: true
    });

    // 注册默认服务实例
    await this.registerDefaultInstances();
  }

  /**
   * 注册默认服务实例
   */
  private async registerDefaultInstances(): Promise<void> {
    // API网关实例
    this.registerServiceInstance({
      id: 'api_gateway_1',
      serviceName: 'api_gateway',
      host: 'localhost',
      port: 3000,
      weight: 10,
      healthy: true,
      connections: 0,
      responseTime: 0,
      lastHealthCheck: Date.now(),
      metadata: { region: 'us-east-1', zone: 'a' }
    });

    this.registerServiceInstance({
      id: 'api_gateway_2',
      serviceName: 'api_gateway',
      host: 'localhost',
      port: 3001,
      weight: 8,
      healthy: true,
      connections: 0,
      responseTime: 0,
      lastHealthCheck: Date.now(),
      metadata: { region: 'us-east-1', zone: 'b' }
    });

    // 数据库实例
    this.registerServiceInstance({
      id: 'database_primary',
      serviceName: 'database',
      host: 'localhost',
      port: 5432,
      weight: 10,
      healthy: true,
      connections: 0,
      responseTime: 0,
      lastHealthCheck: Date.now(),
      metadata: { role: 'primary' }
    });

    this.registerServiceInstance({
      id: 'database_replica_1',
      serviceName: 'database',
      host: 'localhost',
      port: 5433,
      weight: 5,
      healthy: true,
      connections: 0,
      responseTime: 0,
      lastHealthCheck: Date.now(),
      metadata: { role: 'replica' }
    });
  }

  /**
   * 注册负载均衡器
   */
  public registerLoadBalancer(config: LoadBalancerConfig): void {
    this.loadBalancers.set(config.name, config);
    
    // 初始化统计信息
    this.stats.set(config.name, {
      name: config.name,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      activeConnections: 0,
      healthyInstances: 0,
      totalInstances: 0,
      lastUpdated: Date.now()
    });

    // 启动健康检查
    if (config.healthCheck.enabled) {
      this.startHealthCheck(config.name);
    }

    this.logger.log(`注册负载均衡器: ${config.name}`);
  }

  /**
   * 注册服务实例
   */
  public registerServiceInstance(instance: ServiceInstance): void {
    if (!this.serviceInstances.has(instance.serviceName)) {
      this.serviceInstances.set(instance.serviceName, []);
    }

    const instances = this.serviceInstances.get(instance.serviceName)!;
    const existingIndex = instances.findIndex(i => i.id === instance.id);

    if (existingIndex >= 0) {
      instances[existingIndex] = instance;
    } else {
      instances.push(instance);
    }

    this.logger.log(`注册服务实例: ${instance.serviceName}/${instance.id}`);
    this.emit('instanceRegistered', instance);
  }

  /**
   * 注销服务实例
   */
  public unregisterServiceInstance(serviceName: string, instanceId: string): void {
    const instances = this.serviceInstances.get(serviceName);
    if (!instances) return;

    const index = instances.findIndex(i => i.id === instanceId);
    if (index >= 0) {
      const removed = instances.splice(index, 1)[0];
      this.logger.log(`注销服务实例: ${serviceName}/${instanceId}`);
      this.emit('instanceUnregistered', removed);
    }
  }

  /**
   * 选择服务实例
   */
  public selectInstance(
    serviceName: string,
    clientId?: string,
    clientIP?: string
  ): ServiceInstance | null {
    const loadBalancer = this.getLoadBalancerForService(serviceName);
    if (!loadBalancer || !loadBalancer.enabled) {
      return this.getFirstHealthyInstance(serviceName);
    }

    const instances = this.getHealthyInstances(serviceName);
    if (instances.length === 0) {
      return null;
    }

    // 检查会话保持
    if (loadBalancer.sessionAffinity.enabled && clientId) {
      const sessionInstance = this.getSessionInstance(clientId, instances);
      if (sessionInstance) {
        return sessionInstance;
      }
    }

    // 根据算法选择实例
    const selectedInstance = this.selectByAlgorithm(
      loadBalancer.algorithm,
      instances,
      serviceName,
      clientIP
    );

    // 设置会话保持
    if (loadBalancer.sessionAffinity.enabled && clientId && selectedInstance) {
      this.setSessionInstance(clientId, selectedInstance.id);
    }

    // 更新统计
    this.updateStats(loadBalancer.name, selectedInstance);

    return selectedInstance;
  }

  /**
   * 根据算法选择实例
   */
  private selectByAlgorithm(
    algorithm: LoadBalanceAlgorithm,
    instances: ServiceInstance[],
    serviceName: string,
    clientIP?: string
  ): ServiceInstance | null {
    switch (algorithm) {
      case LoadBalanceAlgorithm.ROUND_ROBIN:
        return this.selectRoundRobin(instances, serviceName);

      case LoadBalanceAlgorithm.WEIGHTED_ROUND_ROBIN:
        return this.selectWeightedRoundRobin(instances, serviceName);

      case LoadBalanceAlgorithm.LEAST_CONNECTIONS:
        return this.selectLeastConnections(instances);

      case LoadBalanceAlgorithm.WEIGHTED_LEAST_CONNECTIONS:
        return this.selectWeightedLeastConnections(instances);

      case LoadBalanceAlgorithm.IP_HASH:
        return this.selectIPHash(instances, clientIP);

      case LoadBalanceAlgorithm.CONSISTENT_HASH:
        return this.selectConsistentHash(instances, clientIP);

      case LoadBalanceAlgorithm.RANDOM:
        return this.selectRandom(instances);

      case LoadBalanceAlgorithm.WEIGHTED_RANDOM:
        return this.selectWeightedRandom(instances);

      default:
        return instances[0];
    }
  }

  /**
   * 轮询选择
   */
  private selectRoundRobin(instances: ServiceInstance[], serviceName: string): ServiceInstance {
    const counter = this.roundRobinCounters.get(serviceName) || 0;
    const index = counter % instances.length;
    this.roundRobinCounters.set(serviceName, counter + 1);
    return instances[index];
  }

  /**
   * 加权轮询选择
   */
  private selectWeightedRoundRobin(instances: ServiceInstance[], serviceName: string): ServiceInstance {
    const totalWeight = instances.reduce((sum, instance) => sum + instance.weight, 0);
    const counter = this.roundRobinCounters.get(serviceName) || 0;
    
    let currentWeight = (counter % totalWeight) + 1;
    this.roundRobinCounters.set(serviceName, counter + 1);

    for (const instance of instances) {
      currentWeight -= instance.weight;
      if (currentWeight <= 0) {
        return instance;
      }
    }

    return instances[0];
  }

  /**
   * 最少连接选择
   */
  private selectLeastConnections(instances: ServiceInstance[]): ServiceInstance {
    return instances.reduce((min, instance) => 
      instance.connections < min.connections ? instance : min
    );
  }

  /**
   * 加权最少连接选择
   */
  private selectWeightedLeastConnections(instances: ServiceInstance[]): ServiceInstance {
    return instances.reduce((min, instance) => {
      const minRatio = min.connections / min.weight;
      const instanceRatio = instance.connections / instance.weight;
      return instanceRatio < minRatio ? instance : min;
    });
  }

  /**
   * IP哈希选择
   */
  private selectIPHash(instances: ServiceInstance[], clientIP?: string): ServiceInstance {
    if (!clientIP) {
      return instances[0];
    }

    const hash = this.hashString(clientIP);
    const index = hash % instances.length;
    return instances[index];
  }

  /**
   * 一致性哈希选择
   */
  private selectConsistentHash(instances: ServiceInstance[], clientIP?: string): ServiceInstance {
    if (!clientIP) {
      return instances[0];
    }

    // 简化的一致性哈希实现
    const hash = this.hashString(clientIP);
    const sortedInstances = instances
      .map(instance => ({ instance, hash: this.hashString(instance.id) }))
      .sort((a, b) => a.hash - b.hash);

    for (const item of sortedInstances) {
      if (item.hash >= hash) {
        return item.instance;
      }
    }

    return sortedInstances[0].instance;
  }

  /**
   * 随机选择
   */
  private selectRandom(instances: ServiceInstance[]): ServiceInstance {
    const index = Math.floor(Math.random() * instances.length);
    return instances[index];
  }

  /**
   * 加权随机选择
   */
  private selectWeightedRandom(instances: ServiceInstance[]): ServiceInstance {
    const totalWeight = instances.reduce((sum, instance) => sum + instance.weight, 0);
    let random = Math.random() * totalWeight;

    for (const instance of instances) {
      random -= instance.weight;
      if (random <= 0) {
        return instance;
      }
    }

    return instances[0];
  }

  /**
   * 字符串哈希函数
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 获取健康实例
   */
  private getHealthyInstances(serviceName: string): ServiceInstance[] {
    const instances = this.serviceInstances.get(serviceName) || [];
    return instances.filter(instance => instance.healthy);
  }

  /**
   * 获取第一个健康实例
   */
  private getFirstHealthyInstance(serviceName: string): ServiceInstance | null {
    const instances = this.getHealthyInstances(serviceName);
    return instances.length > 0 ? instances[0] : null;
  }

  /**
   * 获取服务的负载均衡器
   */
  private getLoadBalancerForService(serviceName: string): LoadBalancerConfig | undefined {
    return Array.from(this.loadBalancers.values())
      .find(lb => lb.targetService === serviceName);
  }

  /**
   * 获取会话实例
   */
  private getSessionInstance(clientId: string, instances: ServiceInstance[]): ServiceInstance | null {
    const instanceId = this.sessionMap.get(clientId);
    if (!instanceId) return null;

    return instances.find(instance => instance.id === instanceId) || null;
  }

  /**
   * 设置会话实例
   */
  private setSessionInstance(clientId: string, instanceId: string): void {
    this.sessionMap.set(clientId, instanceId);
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(loadBalancerName: string): void {
    const config = this.loadBalancers.get(loadBalancerName);
    if (!config) return;

    const timer = setInterval(async () => {
      await this.performHealthCheck(config);
    }, config.healthCheck.interval);

    this.healthCheckTimers.set(loadBalancerName, timer);
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(config: LoadBalancerConfig): Promise<void> {
    const instances = this.serviceInstances.get(config.targetService) || [];

    for (const instance of instances) {
      try {
        const startTime = Date.now();
        
        // 这里应该实现实际的健康检查逻辑
        // 简化实现：模拟健康检查
        const isHealthy = Math.random() > 0.1; // 90%健康率
        
        instance.healthy = isHealthy;
        instance.responseTime = Date.now() - startTime;
        instance.lastHealthCheck = Date.now();

        if (!isHealthy) {
          this.logger.warn(`实例 ${instance.serviceName}/${instance.id} 健康检查失败`);
          this.emit('instanceUnhealthy', instance);
        }
      } catch (error) {
        instance.healthy = false;
        this.logger.error(`健康检查异常: ${instance.serviceName}/${instance.id}`, error);
      }
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(loadBalancerName: string, selectedInstance: ServiceInstance | null): void {
    const stats = this.stats.get(loadBalancerName);
    if (!stats) return;

    stats.totalRequests++;
    if (selectedInstance) {
      stats.successfulRequests++;
      selectedInstance.connections++;
    } else {
      stats.failedRequests++;
    }

    const config = this.loadBalancers.get(loadBalancerName);
    if (config) {
      const instances = this.serviceInstances.get(config.targetService) || [];
      stats.healthyInstances = instances.filter(i => i.healthy).length;
      stats.totalInstances = instances.length;
      stats.activeConnections = instances.reduce((sum, i) => sum + i.connections, 0);
    }

    stats.lastUpdated = Date.now();
  }

  /**
   * 获取负载均衡统计
   */
  public getLoadBalancerStats(name?: string): LoadBalancerStats[] {
    if (name) {
      const stats = this.stats.get(name);
      return stats ? [stats] : [];
    }
    return Array.from(this.stats.values());
  }

  /**
   * 获取服务实例列表
   */
  public getServiceInstances(serviceName?: string): ServiceInstance[] {
    if (serviceName) {
      return this.serviceInstances.get(serviceName) || [];
    }
    
    const allInstances: ServiceInstance[] = [];
    for (const instances of this.serviceInstances.values()) {
      allInstances.push(...instances);
    }
    return allInstances;
  }

  /**
   * 模块销毁时清理资源
   */
  public async onModuleDestroy(): Promise<void> {
    // 清理健康检查定时器
    for (const timer of this.healthCheckTimers.values()) {
      clearInterval(timer);
    }

    this.removeAllListeners();
    this.logger.log('负载均衡管理器已销毁');
  }
}
