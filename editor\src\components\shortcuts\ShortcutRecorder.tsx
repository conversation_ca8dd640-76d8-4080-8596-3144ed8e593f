/**
 * 快捷键录制器组件
 * 用于录制和编辑快捷键组合
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Modal, Input, Button, Space, Alert, Form, Switch, Select, Tag } from 'antd';
import { KeyboardOutlined, RecordOutlined, StopOutlined, ClearOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ShortcutService, ShortcutDefinition, ShortcutCategory, ShortcutContext } from '../../services/ShortcutService';
import './ShortcutRecorder.less';

const { Option } = Select;

/**
 * 组件属性接口
 */
interface ShortcutRecorderProps {
  /** 是否可见 */
  visible: boolean;
  /** 要编辑的快捷键 */
  shortcut: ShortcutDefinition;
  /** 保存回调 */
  onSave: (shortcut: ShortcutDefinition) => void;
  /** 取消回调 */
  onCancel: () => void;
}

/**
 * 快捷键录制器组件
 */
export const ShortcutRecorder: React.FC<ShortcutRecorderProps> = ({
  visible,
  shortcut,
  onSave,
  onCancel
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const shortcutService = useMemo(() => ShortcutService.getInstance(), []);

  // 状态
  const [isRecording, setIsRecording] = useState(false);
  const [recordedCombination, setRecordedCombination] = useState('');
  const [conflicts, setConflicts] = useState<ShortcutDefinition[]>([]);
  const [isValid, setIsValid] = useState(true);
  const [validationMessage, setValidationMessage] = useState('');

  /**
   * 开始录制
   */
  const startRecording = useCallback(() => {
    setIsRecording(true);
    setRecordedCombination('');
    setConflicts([]);
    setIsValid(true);
    setValidationMessage('');

    shortcutService.startRecording((combination) => {
      setRecordedCombination(combination);
      setIsRecording(false);
      shortcutService.stopRecording();

      // 验证快捷键
      const valid = shortcutService.isValidCombination(combination);
      setIsValid(valid);

      if (!valid) {
        setValidationMessage(t('shortcuts.invalidCombination'));
        return;
      }

      // 检查冲突
      const conflictShortcuts = shortcutService.checkConflicts(combination, shortcut.id);
      setConflicts(conflictShortcuts);

      if (conflictShortcuts.length > 0) {
        setValidationMessage(t('shortcuts.conflictDetected'));
      } else {
        setValidationMessage('');
      }

      // 更新表单
      form.setFieldsValue({ combination });
    });
  }, [shortcutService, shortcut.id, form, t]);

  /**
   * 停止录制
   */
  const stopRecording = useCallback(() => {
    setIsRecording(false);
    shortcutService.stopRecording();
  }, [shortcutService]);

  /**
   * 清除录制
   */
  const clearRecording = useCallback(() => {
    setRecordedCombination('');
    setConflicts([]);
    setIsValid(true);
    setValidationMessage('');
    form.setFieldsValue({ combination: shortcut.combination });
  }, [form, shortcut.combination]);

  /**
   * 处理保存
   */
  const handleSave = useCallback(() => {
    form.validateFields().then((values) => {
      const updatedShortcut: ShortcutDefinition = {
        ...shortcut,
        ...values,
        combination: recordedCombination || values.combination
      };

      onSave(updatedShortcut);
    });
  }, [form, shortcut, recordedCombination, onSave]);

  /**
   * 处理组合键输入变化
   */
  const handleCombinationChange = useCallback((value: string) => {
    if (value === recordedCombination) return;

    // 验证快捷键
    const valid = shortcutService.isValidCombination(value);
    setIsValid(valid);

    if (!valid) {
      setValidationMessage(t('shortcuts.invalidCombination'));
      setConflicts([]);
      return;
    }

    // 检查冲突
    const conflictShortcuts = shortcutService.checkConflicts(value, shortcut.id);
    setConflicts(conflictShortcuts);

    if (conflictShortcuts.length > 0) {
      setValidationMessage(t('shortcuts.conflictDetected'));
    } else {
      setValidationMessage('');
    }
  }, [shortcutService, recordedCombination, shortcut.id, t]);

  /**
   * 渲染录制区域
   */
  const renderRecordingArea = () => (
    <div className="recording-area">
      <div className="recording-display">
        <Input
          value={recordedCombination || form.getFieldValue('combination')}
          placeholder={t('shortcuts.pressKeysToRecord')}
          readOnly
          size="large"
          prefix={<KeyboardOutlined />}
          status={!isValid ? 'error' : conflicts.length > 0 ? 'warning' : undefined}
        />
      </div>

      <div className="recording-controls">
        <Space>
          {!isRecording ? (
            <Button
              type="primary"
              icon={<RecordOutlined />}
              onClick={startRecording}
            >
              {t('shortcuts.startRecording')}
            </Button>
          ) : (
            <Button
              danger
              icon={<StopOutlined />}
              onClick={stopRecording}
            >
              {t('shortcuts.stopRecording')}
            </Button>
          )}

          <Button
            icon={<ClearOutlined />}
            onClick={clearRecording}
            disabled={isRecording}
          >
            {t('shortcuts.clear')}
          </Button>
        </Space>
      </div>

      {isRecording && (
        <Alert
          message={t('shortcuts.recordingInProgress')}
          description={t('shortcuts.recordingDesc')}
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}

      {validationMessage && (
        <Alert
          message={validationMessage}
          type={conflicts.length > 0 ? 'warning' : 'error'}
          showIcon
          style={{ marginTop: 16 }}
          description={
            conflicts.length > 0 && (
              <div>
                {t('shortcuts.conflictsWith')}:
                <div style={{ marginTop: 8 }}>
                  {conflicts.map(conflict => (
                    <Tag key={conflict.id} color="orange" style={{ marginBottom: 4 }}>
                      {conflict.description}
                    </Tag>
                  ))}
                </div>
              </div>
            )
          }
        />
      )}
    </div>
  );

  /**
   * 初始化表单
   */
  useEffect(() => {
    if (visible && shortcut) {
      form.setFieldsValue({
        description: shortcut.description,
        combination: shortcut.combination,
        category: shortcut.category,
        context: shortcut.context,
        enabled: shortcut.enabled,
        global: shortcut.global,
        priority: shortcut.priority
      });
      setRecordedCombination('');
      setConflicts([]);
      setIsValid(true);
      setValidationMessage('');
    }
  }, [visible, shortcut, form]);

  /**
   * 清理录制状态
   */
  useEffect(() => {
    return () => {
      if (isRecording) {
        shortcutService.stopRecording();
      }
    };
  }, [isRecording, shortcutService]);

  return (
    <Modal
      title={t('shortcuts.editShortcut')}
      visible={visible}
      onCancel={onCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t('common.cancel')}
        </Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
          disabled={!isValid || isRecording}
        >
          {t('common.save')}
        </Button>
      ]}
      className="shortcut-recorder"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={shortcut}
      >
        <Form.Item
          label={t('shortcuts.description')}
          name="description"
          rules={[{ required: true, message: t('shortcuts.descriptionRequired') }]}
        >
          <Input placeholder={t('shortcuts.descriptionPlaceholder')} />
        </Form.Item>

        <Form.Item
          label={t('shortcuts.combination')}
          name="combination"
          rules={[
            { required: true, message: t('shortcuts.combinationRequired') },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                const valid = shortcutService.isValidCombination(value);
                return valid ? Promise.resolve() : Promise.reject(new Error(t('shortcuts.invalidCombination')));
              }
            }
          ]}
        >
          <Input
            placeholder={t('shortcuts.combinationPlaceholder')}
            onChange={(e) => handleCombinationChange(e.target.value)}
          />
        </Form.Item>

        {renderRecordingArea()}

        <Form.Item
          label={t('shortcuts.category')}
          name="category"
          rules={[{ required: true, message: t('shortcuts.categoryRequired') }]}
        >
          <Select placeholder={t('shortcuts.selectCategory')}>
            {Object.values(ShortcutCategory).map(category => (
              <Option key={category} value={category}>
                {category}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label={t('shortcuts.context')}
          name="context"
        >
          <Select placeholder={t('shortcuts.selectContext')} allowClear>
            {Object.values(ShortcutContext).map(context => (
              <Option key={context} value={context}>
                {context}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Space size="large">
          <Form.Item
            label={t('shortcuts.enabled')}
            name="enabled"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('shortcuts.global')}
            name="global"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('shortcuts.priority')}
            name="priority"
          >
            <Select style={{ width: 100 }}>
              <Option value={1}>1</Option>
              <Option value={2}>2</Option>
              <Option value={3}>3</Option>
              <Option value={4}>4</Option>
              <Option value={5}>5</Option>
            </Select>
          </Form.Item>
        </Space>
      </Form>
    </Modal>
  );
};

export default ShortcutRecorder;
