/**
 * 增强的资产浏览器样式
 */
.enhanced-asset-browser {
  height: 100%;
  display: flex;
  flex-direction: column;

  .ant-card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
    overflow: hidden;
  }

  .asset-browser-toolbar {
    margin-bottom: 16px;
    
    .ant-input-search {
      .ant-input {
        border-radius: 6px;
      }
    }

    .ant-select {
      .ant-select-selector {
        border-radius: 6px;
      }
    }

    .ant-btn-group {
      .ant-btn {
        border-radius: 0;
        
        &:first-child {
          border-top-left-radius: 6px;
          border-bottom-left-radius: 6px;
        }
        
        &:last-child {
          border-top-right-radius: 6px;
          border-bottom-right-radius: 6px;
        }
      }
    }
  }

  .asset-browser-content {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    
    .ant-progress {
      margin-top: 16px;
    }
  }

  .asset-grid {
    height: 100%;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .virtualized-asset-grid {
    .asset-grid-item {
      padding: 8px;
    }
  }

  .asset-card {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.selected {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .ant-card-cover {
      height: 120px;
      overflow: hidden;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ant-card-body {
      padding: 12px;
    }

    .ant-card-actions {
      background: #fafafa;
      border-top: 1px solid #f0f0f0;

      .ant-btn {
        border: none;
        background: transparent;
        color: #666;
        transition: color 0.3s;

        &:hover {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.1);
        }
      }
    }
  }

  .asset-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .asset-icon {
      font-size: 16px;
    }

    .asset-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 13px;
      font-weight: 500;
    }
  }

  .asset-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;

    .asset-size {
      font-size: 11px;
      color: #999;
    }
  }

  .asset-filter-content {
    .filter-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      label {
        font-weight: 500;
        color: #262626;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .asset-grid {
      .ant-col {
        span: 8; // 3列
      }
    }
  }

  @media (max-width: 768px) {
    .asset-browser-toolbar {
      .ant-row {
        flex-direction: column;
        gap: 12px;
      }
    }

    .asset-grid {
      .ant-col {
        span: 12; // 2列
      }
    }

    .asset-card {
      .ant-card-cover {
        height: 100px;
      }
    }
  }

  @media (max-width: 480px) {
    .asset-grid {
      .ant-col {
        span: 24; // 1列
      }
    }
  }

  // 深色主题支持
  &.dark-theme {
    .asset-card {
      background: #1f1f1f;
      border-color: #434343;

      .ant-card-cover {
        background: #2a2a2a;
      }

      .ant-card-actions {
        background: #2a2a2a;
        border-color: #434343;
      }

      &.selected {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
      }
    }

    .asset-title .asset-name {
      color: #fff;
    }

    .asset-meta .asset-size {
      color: #999;
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    .asset-card {
      border: 2px solid #000;

      &.selected {
        border-color: #0066cc;
        box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
      }
    }
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    .asset-card {
      transition: none;

      &:hover {
        transform: none;
      }
    }
  }
}
