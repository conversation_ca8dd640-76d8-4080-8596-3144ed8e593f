/**
 * 工作流管理器样式
 */
.workflow-manager {
  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-tabs {
    .ant-tabs-tab {
      font-weight: 500;
      
      &.ant-tabs-tab-active {
        color: #1890ff;
      }
    }

    .ant-tabs-content-holder {
      padding-top: 16px;
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
    }

    .ant-table-tbody > tr {
      &:hover {
        background: #f5f5f5;
      }

      td {
        padding: 12px 16px;
      }
    }
  }

  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;

    &.ant-tag-blue {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &.ant-tag-green {
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &.ant-tag-red {
      background: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }

    &.ant-tag-orange {
      background: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }
  }

  .ant-statistic {
    .ant-statistic-title {
      font-size: 14px;
      color: #8c8c8c;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }
  }

  .ant-list {
    .ant-list-item {
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          .ant-avatar {
            background: #1890ff;
          }
        }

        .ant-list-item-meta-title {
          font-weight: 500;
          color: #262626;
          margin-bottom: 8px;
        }

        .ant-list-item-meta-description {
          color: #666;
          line-height: 1.5;

          .ant-tag {
            margin: 2px 4px 2px 0;
          }
        }
      }

      .ant-list-item-action {
        margin-left: 16px;

        .ant-btn {
          border-radius: 4px;
        }
      }
    }
  }

  .ant-progress {
    .ant-progress-bg {
      background: #1890ff;
    }
  }

  .ant-empty {
    margin: 40px 0;

    .ant-empty-description {
      color: #999;
    }
  }

  .ant-divider {
    margin: 24px 0;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }

    .ant-modal-body {
      padding: 16px;
    }

    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 12px;
        font-size: 12px;
      }
    }

    .ant-statistic {
      .ant-statistic-content {
        font-size: 20px;
      }
    }

    .ant-list {
      .ant-list-item {
        .ant-list-item-action {
          margin-left: 8px;

          .ant-btn {
            padding: 4px 8px;
            font-size: 12px;
          }
        }
      }
    }
  }

  // 深色主题支持
  &.dark-theme {
    .ant-modal-content {
      background: #1f1f1f;
      color: #fff;
    }

    .ant-modal-header {
      background: #1f1f1f;
      border-color: #434343;

      .ant-modal-title {
        color: #fff;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #2a2a2a;
        color: #fff;
        border-color: #434343;
      }

      .ant-table-tbody > tr {
        background: #1f1f1f;
        border-color: #434343;

        &:hover {
          background: #262626;
        }

        td {
          color: #fff;
          border-color: #434343;
        }
      }
    }

    .ant-statistic {
      .ant-statistic-title {
        color: #ccc;
      }

      .ant-statistic-content {
        color: #fff;
      }
    }

    .ant-list {
      .ant-list-item {
        border-color: #434343;

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            color: #fff;
          }

          .ant-list-item-meta-description {
            color: #ccc;
          }
        }
      }
    }

    .ant-empty-description {
      color: #666;
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    .ant-modal-content {
      border: 2px solid #000;
    }

    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        border: 1px solid #000;
      }
    }

    .ant-tag {
      border: 1px solid #000;
    }

    .ant-btn {
      border: 2px solid #000;

      &.ant-btn-primary {
        background: #0066cc;
        border-color: #0066cc;
      }

      &.ant-btn-danger {
        background: #cc0000;
        border-color: #cc0000;
      }
    }
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    .ant-table-tbody > tr,
    .ant-btn,
    .ant-progress {
      transition: none;
    }
  }
}
