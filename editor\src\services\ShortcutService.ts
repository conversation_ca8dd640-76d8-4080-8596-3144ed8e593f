/**
 * 快捷键服务
 * 实现完整快捷键框架、自定义快捷键支持和快捷键冲突检测
 */
import { EventEmitter } from 'events';

/**
 * 快捷键定义接口
 */
export interface ShortcutDefinition {
  /** 快捷键ID */
  id: string;
  /** 快捷键组合 */
  combination: string;
  /** 描述 */
  description: string;
  /** 分类 */
  category: string;
  /** 回调函数 */
  callback: (event: KeyboardEvent) => void;
  /** 是否启用 */
  enabled: boolean;
  /** 是否全局 */
  global: boolean;
  /** 上下文 */
  context?: string;
  /** 优先级 */
  priority: number;
  /** 是否可自定义 */
  customizable: boolean;
}

/**
 * 快捷键冲突信息
 */
export interface ShortcutConflict {
  /** 快捷键组合 */
  combination: string;
  /** 冲突的快捷键列表 */
  conflicts: ShortcutDefinition[];
  /** 冲突类型 */
  type: 'exact' | 'partial';
}

/**
 * 快捷键上下文
 */
export enum ShortcutContext {
  GLOBAL = 'global',
  EDITOR = 'editor',
  SCENE = 'scene',
  PROPERTIES = 'properties',
  ASSETS = 'assets',
  CONSOLE = 'console',
  MODAL = 'modal'
}

/**
 * 快捷键分类
 */
export enum ShortcutCategory {
  FILE = 'file',
  EDIT = 'edit',
  VIEW = 'view',
  TOOLS = 'tools',
  WINDOW = 'window',
  HELP = 'help',
  CUSTOM = 'custom'
}

/**
 * 快捷键服务类
 */
export class ShortcutService extends EventEmitter {
  private static instance: ShortcutService | null = null;

  /** 快捷键映射 */
  private shortcuts: Map<string, ShortcutDefinition> = new Map();
  /** 组合键映射 */
  private combinationMap: Map<string, string[]> = new Map();
  /** 当前上下文 */
  private currentContext: string = ShortcutContext.GLOBAL;
  /** 是否启用 */
  private enabled: boolean = true;
  /** 是否正在录制 */
  private isRecording: boolean = false;
  /** 录制回调 */
  private recordingCallback: ((combination: string) => void) | null = null;

  /**
   * 获取单例实例
   */
  public static getInstance(): ShortcutService {
    if (!ShortcutService.instance) {
      ShortcutService.instance = new ShortcutService();
    }
    return ShortcutService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.initializeDefaultShortcuts();
    this.bindEvents();
  }

  /**
   * 初始化默认快捷键
   */
  private initializeDefaultShortcuts(): void {
    const defaultShortcuts: Omit<ShortcutDefinition, 'callback'>[] = [
      // 文件操作
      { id: 'file.new', combination: 'Ctrl+N', description: '新建', category: ShortcutCategory.FILE, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'file.open', combination: 'Ctrl+O', description: '打开', category: ShortcutCategory.FILE, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'file.save', combination: 'Ctrl+S', description: '保存', category: ShortcutCategory.FILE, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'file.saveAs', combination: 'Ctrl+Shift+S', description: '另存为', category: ShortcutCategory.FILE, enabled: true, global: false, priority: 1, customizable: true },
      
      // 编辑操作
      { id: 'edit.undo', combination: 'Ctrl+Z', description: '撤销', category: ShortcutCategory.EDIT, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'edit.redo', combination: 'Ctrl+Y', description: '重做', category: ShortcutCategory.EDIT, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'edit.copy', combination: 'Ctrl+C', description: '复制', category: ShortcutCategory.EDIT, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'edit.paste', combination: 'Ctrl+V', description: '粘贴', category: ShortcutCategory.EDIT, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'edit.cut', combination: 'Ctrl+X', description: '剪切', category: ShortcutCategory.EDIT, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'edit.selectAll', combination: 'Ctrl+A', description: '全选', category: ShortcutCategory.EDIT, enabled: true, global: false, priority: 1, customizable: true },
      
      // 视图操作
      { id: 'view.zoomIn', combination: 'Ctrl+=', description: '放大', category: ShortcutCategory.VIEW, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'view.zoomOut', combination: 'Ctrl+-', description: '缩小', category: ShortcutCategory.VIEW, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'view.resetZoom', combination: 'Ctrl+0', description: '重置缩放', category: ShortcutCategory.VIEW, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'view.fullscreen', combination: 'F11', description: '全屏', category: ShortcutCategory.VIEW, enabled: true, global: true, priority: 1, customizable: true },
      
      // 工具操作
      { id: 'tools.search', combination: 'Ctrl+F', description: '搜索', category: ShortcutCategory.TOOLS, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'tools.replace', combination: 'Ctrl+H', description: '替换', category: ShortcutCategory.TOOLS, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'tools.console', combination: 'F12', description: '控制台', category: ShortcutCategory.TOOLS, enabled: true, global: false, priority: 1, customizable: true },
      
      // 窗口操作
      { id: 'window.close', combination: 'Ctrl+W', description: '关闭窗口', category: ShortcutCategory.WINDOW, enabled: true, global: false, priority: 1, customizable: true },
      { id: 'window.minimize', combination: 'Ctrl+M', description: '最小化', category: ShortcutCategory.WINDOW, enabled: true, global: false, priority: 1, customizable: true },
      
      // 场景编辑器特定
      { id: 'scene.delete', combination: 'Delete', description: '删除选中对象', category: ShortcutCategory.EDIT, enabled: true, global: false, context: ShortcutContext.SCENE, priority: 1, customizable: true },
      { id: 'scene.duplicate', combination: 'Ctrl+D', description: '复制选中对象', category: ShortcutCategory.EDIT, enabled: true, global: false, context: ShortcutContext.SCENE, priority: 1, customizable: true },
      { id: 'scene.focus', combination: 'F', description: '聚焦到选中对象', category: ShortcutCategory.VIEW, enabled: true, global: false, context: ShortcutContext.SCENE, priority: 1, customizable: true },
      { id: 'scene.frame', combination: 'Shift+F', description: '框选所有对象', category: ShortcutCategory.VIEW, enabled: true, global: false, context: ShortcutContext.SCENE, priority: 1, customizable: true }
    ];

    // 注册默认快捷键（不带回调函数）
    defaultShortcuts.forEach(shortcut => {
      this.registerShortcut({
        ...shortcut,
        callback: () => {
          this.emit('shortcut', shortcut.id);
        }
      });
    });
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    document.addEventListener('keyup', this.handleKeyUp.bind(this));
  }

  /**
   * 处理按键按下事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    if (!this.enabled) return;

    // 如果正在录制，记录按键组合
    if (this.isRecording) {
      const combination = this.getKeyCombination(event);
      this.recordingCallback?.(combination);
      event.preventDefault();
      return;
    }

    // 检查是否在输入框中
    if (this.isInputElement(event.target as Element)) {
      return;
    }

    const combination = this.getKeyCombination(event);
    const shortcutIds = this.combinationMap.get(combination) || [];

    // 按优先级和上下文过滤快捷键
    const validShortcuts = shortcutIds
      .map(id => this.shortcuts.get(id)!)
      .filter(shortcut => shortcut.enabled)
      .filter(shortcut => this.isValidContext(shortcut))
      .sort((a, b) => b.priority - a.priority);

    if (validShortcuts.length > 0) {
      const shortcut = validShortcuts[0];
      event.preventDefault();
      event.stopPropagation();
      
      try {
        shortcut.callback(event);
        this.emit('shortcutExecuted', shortcut.id, combination);
      } catch (error) {
        console.error(`Error executing shortcut ${shortcut.id}:`, error);
        this.emit('shortcutError', shortcut.id, error);
      }
    }
  }

  /**
   * 处理按键释放事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    // 可以在这里处理需要按键释放的快捷键
  }

  /**
   * 获取按键组合字符串
   */
  private getKeyCombination(event: KeyboardEvent): string {
    const parts: string[] = [];
    
    if (event.ctrlKey) parts.push('Ctrl');
    if (event.altKey) parts.push('Alt');
    if (event.shiftKey) parts.push('Shift');
    if (event.metaKey) parts.push('Meta');
    
    // 处理特殊键
    const key = this.normalizeKey(event.key);
    if (key && !['Control', 'Alt', 'Shift', 'Meta'].includes(key)) {
      parts.push(key);
    }
    
    return parts.join('+');
  }

  /**
   * 标准化按键名称
   */
  private normalizeKey(key: string): string {
    const keyMap: Record<string, string> = {
      ' ': 'Space',
      'ArrowUp': 'Up',
      'ArrowDown': 'Down',
      'ArrowLeft': 'Left',
      'ArrowRight': 'Right',
      'Escape': 'Esc',
      'Enter': 'Enter',
      'Backspace': 'Backspace',
      'Tab': 'Tab',
      'Delete': 'Delete',
      'Insert': 'Insert',
      'Home': 'Home',
      'End': 'End',
      'PageUp': 'PageUp',
      'PageDown': 'PageDown'
    };

    return keyMap[key] || key.toUpperCase();
  }

  /**
   * 检查是否是输入元素
   */
  private isInputElement(element: Element): boolean {
    const inputTags = ['INPUT', 'TEXTAREA', 'SELECT'];
    const isContentEditable = element.getAttribute('contenteditable') === 'true';
    
    return inputTags.includes(element.tagName) || isContentEditable;
  }

  /**
   * 检查快捷键是否在有效上下文中
   */
  private isValidContext(shortcut: ShortcutDefinition): boolean {
    if (shortcut.global) return true;
    if (!shortcut.context) return true;
    return shortcut.context === this.currentContext;
  }

  /**
   * 注册快捷键
   */
  public registerShortcut(shortcut: ShortcutDefinition): void {
    // 检查冲突
    const conflicts = this.checkConflicts(shortcut.combination, shortcut.id);
    if (conflicts.length > 0) {
      this.emit('shortcutConflict', {
        combination: shortcut.combination,
        conflicts,
        type: 'exact'
      } as ShortcutConflict);
    }

    this.shortcuts.set(shortcut.id, shortcut);
    
    // 更新组合键映射
    if (!this.combinationMap.has(shortcut.combination)) {
      this.combinationMap.set(shortcut.combination, []);
    }
    this.combinationMap.get(shortcut.combination)!.push(shortcut.id);

    this.emit('shortcutRegistered', shortcut);
  }

  /**
   * 注销快捷键
   */
  public unregisterShortcut(id: string): void {
    const shortcut = this.shortcuts.get(id);
    if (!shortcut) return;

    this.shortcuts.delete(id);
    
    // 更新组合键映射
    const shortcutIds = this.combinationMap.get(shortcut.combination);
    if (shortcutIds) {
      const index = shortcutIds.indexOf(id);
      if (index > -1) {
        shortcutIds.splice(index, 1);
        if (shortcutIds.length === 0) {
          this.combinationMap.delete(shortcut.combination);
        }
      }
    }

    this.emit('shortcutUnregistered', id);
  }

  /**
   * 更新快捷键
   */
  public updateShortcut(id: string, updates: Partial<ShortcutDefinition>): void {
    const shortcut = this.shortcuts.get(id);
    if (!shortcut) return;

    // 如果更新了组合键，需要更新映射
    if (updates.combination && updates.combination !== shortcut.combination) {
      // 从旧映射中移除
      const oldShortcutIds = this.combinationMap.get(shortcut.combination);
      if (oldShortcutIds) {
        const index = oldShortcutIds.indexOf(id);
        if (index > -1) {
          oldShortcutIds.splice(index, 1);
          if (oldShortcutIds.length === 0) {
            this.combinationMap.delete(shortcut.combination);
          }
        }
      }

      // 添加到新映射
      if (!this.combinationMap.has(updates.combination)) {
        this.combinationMap.set(updates.combination, []);
      }
      this.combinationMap.get(updates.combination)!.push(id);
    }

    // 更新快捷键
    Object.assign(shortcut, updates);
    this.emit('shortcutUpdated', id, updates);
  }

  /**
   * 检查快捷键冲突
   */
  public checkConflicts(combination: string, excludeId?: string): ShortcutDefinition[] {
    const conflicts: ShortcutDefinition[] = [];
    const shortcutIds = this.combinationMap.get(combination) || [];

    for (const id of shortcutIds) {
      if (id === excludeId) continue;
      const shortcut = this.shortcuts.get(id);
      if (shortcut) {
        conflicts.push(shortcut);
      }
    }

    return conflicts;
  }

  /**
   * 获取所有快捷键冲突
   */
  public getAllConflicts(): ShortcutConflict[] {
    const conflicts: ShortcutConflict[] = [];

    for (const [combination, shortcutIds] of this.combinationMap.entries()) {
      if (shortcutIds.length > 1) {
        const conflictShortcuts = shortcutIds
          .map(id => this.shortcuts.get(id)!)
          .filter(Boolean);

        conflicts.push({
          combination,
          conflicts: conflictShortcuts,
          type: 'exact'
        });
      }
    }

    return conflicts;
  }

  /**
   * 设置当前上下文
   */
  public setContext(context: string): void {
    this.currentContext = context;
    this.emit('contextChanged', context);
  }

  /**
   * 获取当前上下文
   */
  public getContext(): string {
    return this.currentContext;
  }

  /**
   * 启用/禁用快捷键系统
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    this.emit('enabledChanged', enabled);
  }

  /**
   * 获取快捷键是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 开始录制快捷键
   */
  public startRecording(callback: (combination: string) => void): void {
    this.isRecording = true;
    this.recordingCallback = callback;
    this.emit('recordingStarted');
  }

  /**
   * 停止录制快捷键
   */
  public stopRecording(): void {
    this.isRecording = false;
    this.recordingCallback = null;
    this.emit('recordingStopped');
  }

  /**
   * 获取快捷键
   */
  public getShortcut(id: string): ShortcutDefinition | undefined {
    return this.shortcuts.get(id);
  }

  /**
   * 获取所有快捷键
   */
  public getAllShortcuts(): ShortcutDefinition[] {
    return Array.from(this.shortcuts.values());
  }

  /**
   * 按分类获取快捷键
   */
  public getShortcutsByCategory(category: string): ShortcutDefinition[] {
    return Array.from(this.shortcuts.values())
      .filter(shortcut => shortcut.category === category);
  }

  /**
   * 按上下文获取快捷键
   */
  public getShortcutsByContext(context: string): ShortcutDefinition[] {
    return Array.from(this.shortcuts.values())
      .filter(shortcut => shortcut.context === context || shortcut.global);
  }

  /**
   * 搜索快捷键
   */
  public searchShortcuts(query: string): ShortcutDefinition[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.shortcuts.values())
      .filter(shortcut =>
        shortcut.description.toLowerCase().includes(lowerQuery) ||
        shortcut.combination.toLowerCase().includes(lowerQuery) ||
        shortcut.id.toLowerCase().includes(lowerQuery)
      );
  }

  /**
   * 导出快捷键配置
   */
  public exportConfig(): Record<string, any> {
    const config: Record<string, any> = {};

    for (const [id, shortcut] of this.shortcuts.entries()) {
      if (shortcut.customizable) {
        config[id] = {
          combination: shortcut.combination,
          enabled: shortcut.enabled
        };
      }
    }

    return config;
  }

  /**
   * 导入快捷键配置
   */
  public importConfig(config: Record<string, any>): void {
    for (const [id, settings] of Object.entries(config)) {
      const shortcut = this.shortcuts.get(id);
      if (shortcut && shortcut.customizable) {
        this.updateShortcut(id, settings);
      }
    }

    this.emit('configImported', config);
  }

  /**
   * 重置为默认配置
   */
  public resetToDefaults(): void {
    // 清除所有快捷键
    this.shortcuts.clear();
    this.combinationMap.clear();

    // 重新初始化默认快捷键
    this.initializeDefaultShortcuts();

    this.emit('configReset');
  }

  /**
   * 验证快捷键组合是否有效
   */
  public isValidCombination(combination: string): boolean {
    // 基本格式检查
    if (!combination || typeof combination !== 'string') {
      return false;
    }

    const parts = combination.split('+');
    if (parts.length === 0) {
      return false;
    }

    // 检查修饰键
    const modifiers = ['Ctrl', 'Alt', 'Shift', 'Meta'];
    const hasModifier = parts.some(part => modifiers.includes(part));

    // 检查是否有实际的按键（非修饰键）
    const hasKey = parts.some(part => !modifiers.includes(part));

    return hasKey;
  }

  /**
   * 获取快捷键组合的显示文本
   */
  public getDisplayText(combination: string): string {
    const parts = combination.split('+');
    const displayParts: string[] = [];

    // 按特定顺序显示修饰键
    if (parts.includes('Ctrl')) displayParts.push('Ctrl');
    if (parts.includes('Alt')) displayParts.push('Alt');
    if (parts.includes('Shift')) displayParts.push('Shift');
    if (parts.includes('Meta')) displayParts.push('Cmd');

    // 添加主键
    const mainKey = parts.find(part => !['Ctrl', 'Alt', 'Shift', 'Meta'].includes(part));
    if (mainKey) {
      displayParts.push(mainKey);
    }

    return displayParts.join(' + ');
  }

  /**
   * 销毁服务
   */
  public dispose(): void {
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    document.removeEventListener('keyup', this.handleKeyUp.bind(this));

    this.shortcuts.clear();
    this.combinationMap.clear();
    this.removeAllListeners();

    ShortcutService.instance = null;
  }
}
