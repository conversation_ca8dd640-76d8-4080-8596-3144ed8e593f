/**
 * 自动故障恢复服务
 * 实现自动故障检测、恢复策略执行和恢复状态监控
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';
import { HealthCheckService, HealthStatus, ServiceHealth } from './HealthCheckService';

/**
 * 恢复策略类型
 */
export enum RecoveryStrategyType {
  RESTART_SERVICE = 'restart_service',
  RECONNECT = 'reconnect',
  FAILOVER = 'failover',
  CIRCUIT_BREAKER = 'circuit_breaker',
  RETRY_WITH_BACKOFF = 'retry_with_backoff',
  CUSTOM = 'custom'
}

/**
 * 恢复策略配置
 */
export interface RecoveryStrategy {
  /** 策略ID */
  id: string;
  /** 策略名称 */
  name: string;
  /** 策略类型 */
  type: RecoveryStrategyType;
  /** 目标服务 */
  targetService: string;
  /** 触发条件 */
  trigger: {
    consecutiveFailures: number;
    timeWindow: number;
    healthStatus: HealthStatus[];
  };
  /** 恢复动作 */
  actions: Array<{
    type: string;
    config: any;
    execute: () => Promise<boolean>;
    rollback?: () => Promise<void>;
  }>;
  /** 最大重试次数 */
  maxRetries: number;
  /** 重试间隔（毫秒） */
  retryInterval: number;
  /** 冷却时间（毫秒） */
  cooldownPeriod: number;
  /** 是否启用 */
  enabled: boolean;
  /** 优先级 */
  priority: number;
}

/**
 * 恢复执行记录
 */
export interface RecoveryExecution {
  /** 执行ID */
  id: string;
  /** 策略ID */
  strategyId: string;
  /** 目标服务 */
  targetService: string;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime?: number;
  /** 执行状态 */
  status: 'running' | 'success' | 'failed' | 'cancelled';
  /** 重试次数 */
  retryCount: number;
  /** 执行步骤 */
  steps: Array<{
    action: string;
    startTime: number;
    endTime?: number;
    success: boolean;
    error?: string;
  }>;
  /** 错误信息 */
  error?: string;
  /** 恢复结果 */
  result?: any;
}

/**
 * 恢复统计信息
 */
export interface RecoveryStats {
  /** 服务名称 */
  serviceName: string;
  /** 总恢复尝试次数 */
  totalAttempts: number;
  /** 成功恢复次数 */
  successfulRecoveries: number;
  /** 失败恢复次数 */
  failedRecoveries: number;
  /** 恢复成功率 */
  successRate: number;
  /** 平均恢复时间 */
  averageRecoveryTime: number;
  /** 最后恢复时间 */
  lastRecoveryTime: number;
  /** 最常用的恢复策略 */
  mostUsedStrategy: string;
}

/**
 * 自动故障恢复服务
 */
@Injectable()
export class AutoRecoveryService extends EventEmitter implements OnModuleInit {
  private readonly logger = new Logger(AutoRecoveryService.name);

  /** 恢复策略映射 */
  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map();
  /** 恢复执行记录 */
  private recoveryExecutions: Map<string, RecoveryExecution> = new Map();
  /** 恢复统计 */
  private recoveryStats: Map<string, RecoveryStats> = new Map();
  /** 冷却期记录 */
  private cooldownPeriods: Map<string, number> = new Map();

  /** 是否启用自动恢复 */
  private autoRecoveryEnabled: boolean;
  /** 全局最大并发恢复数 */
  private maxConcurrentRecoveries: number;
  /** 当前运行的恢复数 */
  private runningRecoveries: number = 0;

  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly configService: ConfigService
  ) {
    super();

    this.autoRecoveryEnabled = this.configService.get<boolean>('AUTO_RECOVERY_ENABLED', true);
    this.maxConcurrentRecoveries = this.configService.get<number>('MAX_CONCURRENT_RECOVERIES', 3);
  }

  /**
   * 模块初始化
   */
  public async onModuleInit(): Promise<void> {
    if (this.autoRecoveryEnabled) {
      this.setupEventListeners();
      await this.initializeDefaultStrategies();
      this.logger.log('自动故障恢复服务已启动');
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听服务状态变化
    this.healthCheckService.on('serviceStatusChanged', (event) => {
      this.handleServiceStatusChange(event);
    });

    // 监听关键服务故障
    this.healthCheckService.on('criticalServiceDown', (event) => {
      this.handleCriticalServiceDown(event);
    });
  }

  /**
   * 初始化默认恢复策略
   */
  private async initializeDefaultStrategies(): Promise<void> {
    // 数据库重连策略
    this.registerRecoveryStrategy({
      id: 'database_reconnect',
      name: '数据库重连',
      type: RecoveryStrategyType.RECONNECT,
      targetService: 'database',
      trigger: {
        consecutiveFailures: 2,
        timeWindow: 60000,
        healthStatus: [HealthStatus.UNHEALTHY]
      },
      actions: [
        {
          type: 'reconnect_database',
          config: { timeout: 10000 },
          execute: async () => {
            this.logger.log('尝试重新连接数据库...');
            // 这里应该实现实际的数据库重连逻辑
            await new Promise(resolve => setTimeout(resolve, 2000));
            return Math.random() > 0.3; // 70%成功率
          }
        }
      ],
      maxRetries: 3,
      retryInterval: 30000,
      cooldownPeriod: 300000, // 5分钟
      enabled: true,
      priority: 10
    });

    // Redis缓存重启策略
    this.registerRecoveryStrategy({
      id: 'redis_restart',
      name: 'Redis缓存重启',
      type: RecoveryStrategyType.RESTART_SERVICE,
      targetService: 'redis_cache',
      trigger: {
        consecutiveFailures: 3,
        timeWindow: 120000,
        healthStatus: [HealthStatus.UNHEALTHY]
      },
      actions: [
        {
          type: 'restart_redis',
          config: { graceful: true },
          execute: async () => {
            this.logger.log('尝试重启Redis服务...');
            await new Promise(resolve => setTimeout(resolve, 3000));
            return Math.random() > 0.2; // 80%成功率
          }
        }
      ],
      maxRetries: 2,
      retryInterval: 60000,
      cooldownPeriod: 600000, // 10分钟
      enabled: true,
      priority: 8
    });

    // 消息队列故障转移策略
    this.registerRecoveryStrategy({
      id: 'queue_failover',
      name: '消息队列故障转移',
      type: RecoveryStrategyType.FAILOVER,
      targetService: 'message_queue',
      trigger: {
        consecutiveFailures: 2,
        timeWindow: 180000,
        healthStatus: [HealthStatus.UNHEALTHY]
      },
      actions: [
        {
          type: 'failover_to_backup',
          config: { backupQueue: 'backup_queue' },
          execute: async () => {
            this.logger.log('切换到备用消息队列...');
            await new Promise(resolve => setTimeout(resolve, 1000));
            return Math.random() > 0.1; // 90%成功率
          }
        }
      ],
      maxRetries: 1,
      retryInterval: 120000,
      cooldownPeriod: 900000, // 15分钟
      enabled: true,
      priority: 6
    });
  }

  /**
   * 注册恢复策略
   */
  public registerRecoveryStrategy(strategy: RecoveryStrategy): void {
    this.recoveryStrategies.set(strategy.id, strategy);
    
    // 初始化统计信息
    if (!this.recoveryStats.has(strategy.targetService)) {
      this.recoveryStats.set(strategy.targetService, {
        serviceName: strategy.targetService,
        totalAttempts: 0,
        successfulRecoveries: 0,
        failedRecoveries: 0,
        successRate: 0,
        averageRecoveryTime: 0,
        lastRecoveryTime: 0,
        mostUsedStrategy: ''
      });
    }

    this.logger.log(`注册恢复策略: ${strategy.name}`);
  }

  /**
   * 处理服务状态变化
   */
  private async handleServiceStatusChange(event: any): Promise<void> {
    const { serviceName, currentStatus } = event;
    
    if (currentStatus === HealthStatus.UNHEALTHY) {
      await this.triggerRecovery(serviceName);
    }
  }

  /**
   * 处理关键服务故障
   */
  private async handleCriticalServiceDown(event: any): Promise<void> {
    const { serviceName } = event;
    this.logger.error(`关键服务 ${serviceName} 故障，立即触发恢复`);
    await this.triggerRecovery(serviceName, true);
  }

  /**
   * 触发恢复
   */
  public async triggerRecovery(serviceName: string, urgent: boolean = false): Promise<void> {
    // 检查是否在冷却期
    if (!urgent && this.isInCooldown(serviceName)) {
      this.logger.debug(`服务 ${serviceName} 在冷却期内，跳过恢复`);
      return;
    }

    // 检查并发限制
    if (this.runningRecoveries >= this.maxConcurrentRecoveries) {
      this.logger.warn('达到最大并发恢复数限制，恢复请求排队');
      // 这里可以实现队列机制
      return;
    }

    // 获取适用的恢复策略
    const strategies = this.getApplicableStrategies(serviceName);
    if (strategies.length === 0) {
      this.logger.warn(`服务 ${serviceName} 没有可用的恢复策略`);
      return;
    }

    // 按优先级排序
    strategies.sort((a, b) => b.priority - a.priority);

    // 执行恢复策略
    for (const strategy of strategies) {
      if (await this.executeRecoveryStrategy(strategy)) {
        break; // 成功恢复，停止尝试其他策略
      }
    }
  }

  /**
   * 获取适用的恢复策略
   */
  private getApplicableStrategies(serviceName: string): RecoveryStrategy[] {
    const serviceHealth = this.healthCheckService.getServiceHealth(serviceName)[0];
    if (!serviceHealth) return [];

    return Array.from(this.recoveryStrategies.values())
      .filter(strategy => 
        strategy.enabled &&
        strategy.targetService === serviceName &&
        this.checkTriggerConditions(strategy, serviceHealth)
      );
  }

  /**
   * 检查触发条件
   */
  private checkTriggerConditions(strategy: RecoveryStrategy, serviceHealth: ServiceHealth): boolean {
    const { trigger } = strategy;
    
    // 检查连续失败次数
    if (serviceHealth.consecutiveFailures < trigger.consecutiveFailures) {
      return false;
    }

    // 检查健康状态
    if (!trigger.healthStatus.includes(serviceHealth.status)) {
      return false;
    }

    return true;
  }

  /**
   * 执行恢复策略
   */
  private async executeRecoveryStrategy(strategy: RecoveryStrategy): Promise<boolean> {
    const executionId = this.generateExecutionId();
    const execution: RecoveryExecution = {
      id: executionId,
      strategyId: strategy.id,
      targetService: strategy.targetService,
      startTime: Date.now(),
      status: 'running',
      retryCount: 0,
      steps: []
    };

    this.recoveryExecutions.set(executionId, execution);
    this.runningRecoveries++;

    this.logger.log(`开始执行恢复策略: ${strategy.name} (${executionId})`);
    this.emit('recoveryStarted', { strategy, execution });

    try {
      let success = false;
      let retryCount = 0;

      while (!success && retryCount <= strategy.maxRetries) {
        execution.retryCount = retryCount;

        // 执行所有恢复动作
        success = await this.executeRecoveryActions(strategy, execution);

        if (!success && retryCount < strategy.maxRetries) {
          this.logger.log(`恢复失败，${strategy.retryInterval}ms后重试 (${retryCount + 1}/${strategy.maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, strategy.retryInterval));
          retryCount++;
        }
      }

      // 更新执行状态
      execution.endTime = Date.now();
      execution.status = success ? 'success' : 'failed';

      // 更新统计信息
      this.updateRecoveryStats(strategy.targetService, success, execution.endTime - execution.startTime);

      // 设置冷却期
      if (success) {
        this.setCooldown(strategy.targetService, strategy.cooldownPeriod);
      }

      this.logger.log(`恢复策略执行${success ? '成功' : '失败'}: ${strategy.name}`);
      this.emit('recoveryCompleted', { strategy, execution, success });

      return success;

    } catch (error) {
      execution.endTime = Date.now();
      execution.status = 'failed';
      execution.error = error.message;

      this.logger.error(`恢复策略执行异常: ${strategy.name}`, error);
      this.emit('recoveryError', { strategy, execution, error });

      return false;

    } finally {
      this.runningRecoveries--;
    }
  }

  /**
   * 执行恢复动作
   */
  private async executeRecoveryActions(
    strategy: RecoveryStrategy,
    execution: RecoveryExecution
  ): Promise<boolean> {
    for (const action of strategy.actions) {
      const step = {
        action: action.type,
        startTime: Date.now(),
        success: false
      };

      try {
        const result = await action.execute();
        step.success = result;
        step.endTime = Date.now();
        execution.steps.push(step);

        if (!result) {
          this.logger.warn(`恢复动作失败: ${action.type}`);
          return false;
        }

        this.logger.debug(`恢复动作成功: ${action.type}`);
      } catch (error) {
        step.success = false;
        step.endTime = Date.now();
        step.error = error.message;
        execution.steps.push(step);

        this.logger.error(`恢复动作异常: ${action.type}`, error);

        // 如果有回滚函数，执行回滚
        if (action.rollback) {
          try {
            await action.rollback();
            this.logger.log(`回滚动作执行: ${action.type}`);
          } catch (rollbackError) {
            this.logger.error(`回滚动作失败: ${action.type}`, rollbackError);
          }
        }

        return false;
      }
    }

    return true;
  }

  /**
   * 检查是否在冷却期
   */
  private isInCooldown(serviceName: string): boolean {
    const cooldownEnd = this.cooldownPeriods.get(serviceName);
    return cooldownEnd ? Date.now() < cooldownEnd : false;
  }

  /**
   * 设置冷却期
   */
  private setCooldown(serviceName: string, cooldownPeriod: number): void {
    this.cooldownPeriods.set(serviceName, Date.now() + cooldownPeriod);
  }

  /**
   * 更新恢复统计
   */
  private updateRecoveryStats(serviceName: string, success: boolean, duration: number): void {
    let stats = this.recoveryStats.get(serviceName);
    if (!stats) {
      stats = {
        serviceName,
        totalAttempts: 0,
        successfulRecoveries: 0,
        failedRecoveries: 0,
        successRate: 0,
        averageRecoveryTime: 0,
        lastRecoveryTime: 0,
        mostUsedStrategy: ''
      };
      this.recoveryStats.set(serviceName, stats);
    }

    stats.totalAttempts++;
    if (success) {
      stats.successfulRecoveries++;
    } else {
      stats.failedRecoveries++;
    }

    stats.successRate = stats.successfulRecoveries / stats.totalAttempts;
    stats.averageRecoveryTime = (stats.averageRecoveryTime + duration) / 2;
    stats.lastRecoveryTime = Date.now();
  }

  /**
   * 生成执行ID
   */
  private generateExecutionId(): string {
    return `recovery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取恢复统计
   */
  public getRecoveryStats(serviceName?: string): RecoveryStats[] {
    if (serviceName) {
      const stats = this.recoveryStats.get(serviceName);
      return stats ? [stats] : [];
    }
    return Array.from(this.recoveryStats.values());
  }

  /**
   * 获取恢复执行记录
   */
  public getRecoveryExecutions(limit?: number): RecoveryExecution[] {
    const executions = Array.from(this.recoveryExecutions.values())
      .sort((a, b) => b.startTime - a.startTime);
    return limit ? executions.slice(0, limit) : executions;
  }

  /**
   * 启用/禁用恢复策略
   */
  public setRecoveryStrategyEnabled(strategyId: string, enabled: boolean): void {
    const strategy = this.recoveryStrategies.get(strategyId);
    if (strategy) {
      strategy.enabled = enabled;
      this.logger.log(`恢复策略 ${strategy.name} ${enabled ? '启用' : '禁用'}`);
    }
  }

  /**
   * 清理过期记录
   */
  public cleanupExpiredRecords(): void {
    const now = Date.now();
    const expiredThreshold = now - 7 * 24 * 3600000; // 7天前

    // 清理过期的执行记录
    for (const [id, execution] of this.recoveryExecutions.entries()) {
      if (execution.startTime < expiredThreshold) {
        this.recoveryExecutions.delete(id);
      }
    }

    // 清理过期的冷却期
    for (const [serviceName, cooldownEnd] of this.cooldownPeriods.entries()) {
      if (cooldownEnd < now) {
        this.cooldownPeriods.delete(serviceName);
      }
    }

    this.logger.debug('清理过期恢复记录完成');
  }

  /**
   * 销毁服务
   */
  public destroy(): void {
    this.removeAllListeners();
    this.logger.log('自动故障恢复服务已销毁');
  }
}
