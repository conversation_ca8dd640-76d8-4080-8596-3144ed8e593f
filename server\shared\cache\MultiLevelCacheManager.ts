/**
 * 多级缓存管理器
 * 实现L1(内存)、L2(Redis)、L3(分布式)多级缓存策略
 */
import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis, Cluster } from 'ioredis';
import { EventEmitter } from 'events';

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  /** 缓存值 */
  value: T;
  /** 过期时间戳 */
  expireAt: number;
  /** 创建时间戳 */
  createdAt: number;
  /** 最后访问时间 */
  lastAccessed: number;
  /** 访问次数 */
  accessCount: number;
  /** 数据大小（字节） */
  size: number;
  /** 缓存级别 */
  level: CacheLevel;
  /** 标签 */
  tags: string[];
}

/**
 * 缓存级别枚举
 */
export enum CacheLevel {
  L1_MEMORY = 'L1_MEMORY',
  L2_REDIS = 'L2_REDIS',
  L3_DISTRIBUTED = 'L3_DISTRIBUTED'
}

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  LRU = 'LRU', // 最近最少使用
  LFU = 'LFU', // 最少使用频率
  FIFO = 'FIFO', // 先进先出
  TTL = 'TTL' // 基于过期时间
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /** L1缓存配置 */
  l1: {
    maxSize: number; // 最大条目数
    maxMemory: number; // 最大内存使用（字节）
    ttl: number; // 默认TTL（秒）
    strategy: CacheStrategy;
  };
  /** L2缓存配置 */
  l2: {
    ttl: number;
    maxMemory: string; // Redis maxmemory
    evictionPolicy: string; // Redis eviction policy
  };
  /** L3缓存配置 */
  l3: {
    ttl: number;
    replicationFactor: number;
  };
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  /** L1统计 */
  l1: {
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
    memoryUsage: number;
  };
  /** L2统计 */
  l2: {
    hits: number;
    misses: number;
    hitRate: number;
    memoryUsage: number;
  };
  /** L3统计 */
  l3: {
    hits: number;
    misses: number;
    hitRate: number;
  };
  /** 总体统计 */
  overall: {
    totalHits: number;
    totalMisses: number;
    overallHitRate: number;
  };
}

/**
 * 多级缓存管理器
 */
@Injectable()
export class MultiLevelCacheManager extends EventEmitter implements OnModuleDestroy {
  private readonly logger = new Logger(MultiLevelCacheManager.name);

  /** L1内存缓存 */
  private l1Cache: Map<string, CacheItem> = new Map();
  /** L2 Redis缓存 */
  private l2Redis: Redis | Cluster;
  /** L3分布式缓存节点 */
  private l3Nodes: Redis[] = [];

  /** 缓存配置 */
  private config: CacheConfig;
  /** 缓存统计 */
  private stats: CacheStats;

  /** 清理定时器 */
  private cleanupTimer?: NodeJS.Timeout;
  /** 统计定时器 */
  private statsTimer?: NodeJS.Timeout;

  /** 当前内存使用量 */
  private currentMemoryUsage = 0;

  constructor(private readonly configService: ConfigService) {
    super();
    this.initializeConfig();
    this.initializeStats();
    this.initializeRedis();
    this.startCleanupTimer();
    this.startStatsTimer();
  }

  /**
   * 初始化配置
   */
  private initializeConfig(): void {
    this.config = {
      l1: {
        maxSize: this.configService.get<number>('CACHE_L1_MAX_SIZE', 10000),
        maxMemory: this.configService.get<number>('CACHE_L1_MAX_MEMORY', 100 * 1024 * 1024), // 100MB
        ttl: this.configService.get<number>('CACHE_L1_TTL', 300), // 5分钟
        strategy: this.configService.get<CacheStrategy>('CACHE_L1_STRATEGY', CacheStrategy.LRU)
      },
      l2: {
        ttl: this.configService.get<number>('CACHE_L2_TTL', 3600), // 1小时
        maxMemory: this.configService.get<string>('CACHE_L2_MAX_MEMORY', '1gb'),
        evictionPolicy: this.configService.get<string>('CACHE_L2_EVICTION_POLICY', 'allkeys-lru')
      },
      l3: {
        ttl: this.configService.get<number>('CACHE_L3_TTL', 86400), // 24小时
        replicationFactor: this.configService.get<number>('CACHE_L3_REPLICATION_FACTOR', 2)
      }
    };
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    this.stats = {
      l1: { hits: 0, misses: 0, hitRate: 0, size: 0, memoryUsage: 0 },
      l2: { hits: 0, misses: 0, hitRate: 0, memoryUsage: 0 },
      l3: { hits: 0, misses: 0, hitRate: 0 },
      overall: { totalHits: 0, totalMisses: 0, overallHitRate: 0 }
    };
  }

  /**
   * 初始化Redis连接
   */
  private async initializeRedis(): Promise<void> {
    try {
      // L2 Redis配置
      const redisConfig = {
        host: this.configService.get<string>('REDIS_HOST', 'localhost'),
        port: this.configService.get<number>('REDIS_PORT', 6379),
        password: this.configService.get<string>('REDIS_PASSWORD'),
        db: this.configService.get<number>('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      };

      // 检查是否使用Redis集群
      const clusterNodes = this.configService.get<string>('REDIS_CLUSTER_NODES');
      if (clusterNodes) {
        const nodes = clusterNodes.split(',').map(node => {
          const [host, port] = node.split(':');
          return { host, port: parseInt(port) };
        });
        this.l2Redis = new Cluster(nodes, {
          redisOptions: redisConfig
        });
      } else {
        this.l2Redis = new Redis(redisConfig);
      }

      // L3分布式缓存节点
      const l3Nodes = this.configService.get<string>('CACHE_L3_NODES', '');
      if (l3Nodes) {
        const nodeList = l3Nodes.split(',');
        for (const nodeConfig of nodeList) {
          const [host, port] = nodeConfig.split(':');
          const node = new Redis({
            host,
            port: parseInt(port),
            password: this.configService.get<string>('REDIS_PASSWORD'),
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3,
            lazyConnect: true
          });
          this.l3Nodes.push(node);
        }
      }

      this.logger.log('多级缓存系统初始化完成');
    } catch (error) {
      this.logger.error('Redis初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存值
   */
  public async get<T>(key: string): Promise<T | null> {
    // L1缓存查找
    const l1Result = this.getFromL1<T>(key);
    if (l1Result !== null) {
      this.stats.l1.hits++;
      this.updateOverallStats();
      return l1Result;
    }
    this.stats.l1.misses++;

    // L2缓存查找
    const l2Result = await this.getFromL2<T>(key);
    if (l2Result !== null) {
      this.stats.l2.hits++;
      // 回写到L1缓存
      this.setToL1(key, l2Result, this.config.l1.ttl);
      this.updateOverallStats();
      return l2Result;
    }
    this.stats.l2.misses++;

    // L3缓存查找
    const l3Result = await this.getFromL3<T>(key);
    if (l3Result !== null) {
      this.stats.l3.hits++;
      // 回写到L2和L1缓存
      await this.setToL2(key, l3Result, this.config.l2.ttl);
      this.setToL1(key, l3Result, this.config.l1.ttl);
      this.updateOverallStats();
      return l3Result;
    }
    this.stats.l3.misses++;

    this.updateOverallStats();
    return null;
  }

  /**
   * 设置缓存值
   */
  public async set<T>(
    key: string,
    value: T,
    ttl?: number,
    options?: {
      level?: CacheLevel[];
      tags?: string[];
      skipL1?: boolean;
      skipL2?: boolean;
      skipL3?: boolean;
    }
  ): Promise<void> {
    const levels = options?.level || [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS, CacheLevel.L3_DISTRIBUTED];
    const tags = options?.tags || [];

    // 设置到各级缓存
    if (levels.includes(CacheLevel.L1_MEMORY) && !options?.skipL1) {
      this.setToL1(key, value, ttl || this.config.l1.ttl, tags);
    }

    if (levels.includes(CacheLevel.L2_REDIS) && !options?.skipL2) {
      await this.setToL2(key, value, ttl || this.config.l2.ttl, tags);
    }

    if (levels.includes(CacheLevel.L3_DISTRIBUTED) && !options?.skipL3) {
      await this.setToL3(key, value, ttl || this.config.l3.ttl, tags);
    }

    this.emit('cacheSet', { key, levels, tags });
  }

  /**
   * 从L1缓存获取
   */
  private getFromL1<T>(key: string): T | null {
    const item = this.l1Cache.get(key);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() > item.expireAt) {
      this.l1Cache.delete(key);
      this.currentMemoryUsage -= item.size;
      return null;
    }

    // 更新访问信息
    item.lastAccessed = Date.now();
    item.accessCount++;

    return item.value as T;
  }

  /**
   * 设置到L1缓存
   */
  private setToL1<T>(key: string, value: T, ttl: number, tags: string[] = []): void {
    const size = this.calculateSize(value);
    const now = Date.now();

    const item: CacheItem<T> = {
      value,
      expireAt: now + ttl * 1000,
      createdAt: now,
      lastAccessed: now,
      accessCount: 1,
      size,
      level: CacheLevel.L1_MEMORY,
      tags
    };

    // 检查内存限制
    if (this.currentMemoryUsage + size > this.config.l1.maxMemory) {
      this.evictFromL1(size);
    }

    // 检查条目数限制
    if (this.l1Cache.size >= this.config.l1.maxSize) {
      this.evictFromL1(0);
    }

    this.l1Cache.set(key, item);
    this.currentMemoryUsage += size;
  }

  /**
   * 从L2缓存获取
   */
  private async getFromL2<T>(key: string): Promise<T | null> {
    try {
      const data = await this.l2Redis.get(key);
      if (!data) return null;

      return JSON.parse(data) as T;
    } catch (error) {
      this.logger.error('L2缓存读取失败:', error);
      return null;
    }
  }

  /**
   * 设置到L2缓存
   */
  private async setToL2<T>(key: string, value: T, ttl: number, tags: string[] = []): Promise<void> {
    try {
      const data = JSON.stringify(value);
      await this.l2Redis.setex(key, ttl, data);

      // 设置标签索引
      if (tags.length > 0) {
        for (const tag of tags) {
          await this.l2Redis.sadd(`tag:${tag}`, key);
          await this.l2Redis.expire(`tag:${tag}`, ttl);
        }
      }
    } catch (error) {
      this.logger.error('L2缓存写入失败:', error);
    }
  }

  /**
   * 从L3缓存获取
   */
  private async getFromL3<T>(key: string): Promise<T | null> {
    if (this.l3Nodes.length === 0) return null;

    // 使用一致性哈希选择节点
    const nodeIndex = this.getConsistentHashNode(key);
    const node = this.l3Nodes[nodeIndex];

    try {
      const data = await node.get(key);
      if (!data) return null;

      return JSON.parse(data) as T;
    } catch (error) {
      this.logger.error('L3缓存读取失败:', error);
      return null;
    }
  }

  /**
   * 设置到L3缓存
   */
  private async setToL3<T>(key: string, value: T, ttl: number, tags: string[] = []): Promise<void> {
    if (this.l3Nodes.length === 0) return;

    const data = JSON.stringify(value);
    const replicationFactor = Math.min(this.config.l3.replicationFactor, this.l3Nodes.length);

    // 复制到多个节点
    const promises: Promise<void>[] = [];
    for (let i = 0; i < replicationFactor; i++) {
      const nodeIndex = (this.getConsistentHashNode(key) + i) % this.l3Nodes.length;
      const node = this.l3Nodes[nodeIndex];

      promises.push(
        node.setex(key, ttl, data).catch(error => {
          this.logger.error(`L3缓存节点 ${nodeIndex} 写入失败:`, error);
        })
      );

      // 设置标签索引
      if (tags.length > 0) {
        for (const tag of tags) {
          promises.push(
            node.sadd(`tag:${tag}`, key).then(() =>
              node.expire(`tag:${tag}`, ttl)
            ).catch(error => {
              this.logger.error(`L3缓存标签设置失败:`, error);
            })
          );
        }
      }
    }

    await Promise.allSettled(promises);
  }

  /**
   * 删除缓存
   */
  public async delete(key: string): Promise<void> {
    // 从L1删除
    const l1Item = this.l1Cache.get(key);
    if (l1Item) {
      this.l1Cache.delete(key);
      this.currentMemoryUsage -= l1Item.size;
    }

    // 从L2删除
    try {
      await this.l2Redis.del(key);
    } catch (error) {
      this.logger.error('L2缓存删除失败:', error);
    }

    // 从L3删除
    const deletePromises = this.l3Nodes.map(node =>
      node.del(key).catch(error => {
        this.logger.error('L3缓存删除失败:', error);
      })
    );
    await Promise.allSettled(deletePromises);

    this.emit('cacheDeleted', { key });
  }

  /**
   * 按标签删除缓存
   */
  public async deleteByTag(tag: string): Promise<void> {
    // L1缓存按标签删除
    for (const [key, item] of this.l1Cache.entries()) {
      if (item.tags.includes(tag)) {
        this.l1Cache.delete(key);
        this.currentMemoryUsage -= item.size;
      }
    }

    // L2缓存按标签删除
    try {
      const keys = await this.l2Redis.smembers(`tag:${tag}`);
      if (keys.length > 0) {
        await this.l2Redis.del(...keys);
        await this.l2Redis.del(`tag:${tag}`);
      }
    } catch (error) {
      this.logger.error('L2缓存标签删除失败:', error);
    }

    // L3缓存按标签删除
    for (const node of this.l3Nodes) {
      try {
        const keys = await node.smembers(`tag:${tag}`);
        if (keys.length > 0) {
          await node.del(...keys);
          await node.del(`tag:${tag}`);
        }
      } catch (error) {
        this.logger.error('L3缓存标签删除失败:', error);
      }
    }

    this.emit('cacheDeletedByTag', { tag });
  }

  /**
   * L1缓存淘汰策略
   */
  private evictFromL1(requiredSize: number): void {
    const itemsToEvict: [string, CacheItem][] = [];

    switch (this.config.l1.strategy) {
      case CacheStrategy.LRU:
        // 按最后访问时间排序
        itemsToEvict.push(...Array.from(this.l1Cache.entries())
          .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed));
        break;

      case CacheStrategy.LFU:
        // 按访问频率排序
        itemsToEvict.push(...Array.from(this.l1Cache.entries())
          .sort((a, b) => a[1].accessCount - b[1].accessCount));
        break;

      case CacheStrategy.FIFO:
        // 按创建时间排序
        itemsToEvict.push(...Array.from(this.l1Cache.entries())
          .sort((a, b) => a[1].createdAt - b[1].createdAt));
        break;

      case CacheStrategy.TTL:
        // 按过期时间排序
        itemsToEvict.push(...Array.from(this.l1Cache.entries())
          .sort((a, b) => a[1].expireAt - b[1].expireAt));
        break;
    }

    let freedMemory = 0;
    let evictedCount = 0;

    for (const [key, item] of itemsToEvict) {
      this.l1Cache.delete(key);
      this.currentMemoryUsage -= item.size;
      freedMemory += item.size;
      evictedCount++;

      // 检查是否满足要求
      if (freedMemory >= requiredSize && this.l1Cache.size < this.config.l1.maxSize) {
        break;
      }
    }

    this.logger.debug(`L1缓存淘汰了 ${evictedCount} 个项目，释放 ${freedMemory} 字节内存`);
  }

  /**
   * 计算数据大小
   */
  private calculateSize(value: any): number {
    try {
      return JSON.stringify(value).length * 2; // 粗略估算（UTF-16）
    } catch {
      return 1024; // 默认1KB
    }
  }

  /**
   * 一致性哈希节点选择
   */
  private getConsistentHashNode(key: string): number {
    if (this.l3Nodes.length === 0) return 0;

    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash) % this.l3Nodes.length;
  }

  /**
   * 更新总体统计
   */
  private updateOverallStats(): void {
    this.stats.overall.totalHits = this.stats.l1.hits + this.stats.l2.hits + this.stats.l3.hits;
    this.stats.overall.totalMisses = this.stats.l1.misses + this.stats.l2.misses + this.stats.l3.misses;

    const total = this.stats.overall.totalHits + this.stats.overall.totalMisses;
    this.stats.overall.overallHitRate = total > 0 ? this.stats.overall.totalHits / total : 0;

    // 更新各级命中率
    this.stats.l1.hitRate = (this.stats.l1.hits + this.stats.l1.misses) > 0
      ? this.stats.l1.hits / (this.stats.l1.hits + this.stats.l1.misses) : 0;
    this.stats.l2.hitRate = (this.stats.l2.hits + this.stats.l2.misses) > 0
      ? this.stats.l2.hits / (this.stats.l2.hits + this.stats.l2.misses) : 0;
    this.stats.l3.hitRate = (this.stats.l3.hits + this.stats.l3.misses) > 0
      ? this.stats.l3.hits / (this.stats.l3.hits + this.stats.l3.misses) : 0;

    // 更新大小和内存使用
    this.stats.l1.size = this.l1Cache.size;
    this.stats.l1.memoryUsage = this.currentMemoryUsage;
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, 60000); // 每分钟清理一次
  }

  /**
   * 启动统计定时器
   */
  private startStatsTimer(): void {
    this.statsTimer = setInterval(() => {
      this.updateStats();
    }, 30000); // 每30秒更新统计
  }

  /**
   * 执行清理
   */
  private performCleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    // 清理L1过期项
    for (const [key, item] of this.l1Cache.entries()) {
      if (now > item.expireAt) {
        this.l1Cache.delete(key);
        this.currentMemoryUsage -= item.size;
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`清理了 ${cleanedCount} 个过期的L1缓存项`);
    }

    this.emit('cacheCleanup', { cleanedCount, level: 'L1' });
  }

  /**
   * 更新统计信息
   */
  private async updateStats(): Promise<void> {
    try {
      // 获取L2内存使用情况
      const l2Info = await this.l2Redis.memory('usage');
      if (typeof l2Info === 'number') {
        this.stats.l2.memoryUsage = l2Info;
      }
    } catch (error) {
      this.logger.error('获取L2统计失败:', error);
    }

    this.updateOverallStats();
    this.emit('statsUpdated', this.stats);
  }

  /**
   * 获取缓存统计
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 获取缓存配置
   */
  public getConfig(): CacheConfig {
    return { ...this.config };
  }

  /**
   * 清空所有缓存
   */
  public async clear(): Promise<void> {
    // 清空L1
    this.l1Cache.clear();
    this.currentMemoryUsage = 0;

    // 清空L2
    try {
      await this.l2Redis.flushdb();
    } catch (error) {
      this.logger.error('L2缓存清空失败:', error);
    }

    // 清空L3
    const clearPromises = this.l3Nodes.map(node =>
      node.flushdb().catch(error => {
        this.logger.error('L3缓存清空失败:', error);
      })
    );
    await Promise.allSettled(clearPromises);

    // 重置统计
    this.initializeStats();

    this.emit('cacheCleared');
  }

  /**
   * 预热缓存
   */
  public async warmup(data: Array<{ key: string; value: any; ttl?: number; tags?: string[] }>): Promise<void> {
    const promises = data.map(item =>
      this.set(item.key, item.value, item.ttl, { tags: item.tags })
    );

    await Promise.allSettled(promises);
    this.logger.log(`缓存预热完成，加载了 ${data.length} 个项目`);
    this.emit('cacheWarmedUp', { count: data.length });
  }

  /**
   * 模块销毁时清理资源
   */
  public async onModuleDestroy(): Promise<void> {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    if (this.statsTimer) {
      clearInterval(this.statsTimer);
    }

    // 关闭Redis连接
    try {
      await this.l2Redis.quit();

      const quitPromises = this.l3Nodes.map(node => node.quit());
      await Promise.allSettled(quitPromises);
    } catch (error) {
      this.logger.error('关闭Redis连接失败:', error);
    }

    this.removeAllListeners();
    this.logger.log('多级缓存管理器已销毁');
  }
}