/**
 * 资产缩略图组件
 * 支持缓存和懒加载的高性能缩略图显示
 */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Spin, Image } from 'antd';
import { 
  FileImageOutlined, FileTextOutlined, VideoCameraOutlined, 
  AudioOutlined, FileUnknownOutlined, FolderOutlined 
} from '@ant-design/icons';
import { Asset, AssetType } from '../../store/asset/assetSlice';
import './AssetThumbnail.less';

/**
 * 组件属性接口
 */
interface AssetThumbnailProps {
  /** 资产对象 */
  asset: Asset;
  /** 缩略图大小 */
  size?: number;
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 是否启用懒加载 */
  enableLazyLoad?: boolean;
  /** 点击回调 */
  onClick?: () => void;
  /** 双击回调 */
  onDoubleClick?: () => void;
  /** 加载完成回调 */
  onLoad?: () => void;
  /** 加载错误回调 */
  onError?: (error: Error) => void;
}

/**
 * 资产类型默认图标
 */
const DEFAULT_ICONS = {
  [AssetType.MODEL]: <FileUnknownOutlined />,
  [AssetType.TEXTURE]: <FileImageOutlined />,
  [AssetType.MATERIAL]: <FileTextOutlined />,
  [AssetType.AUDIO]: <AudioOutlined />,
  [AssetType.SCRIPT]: <FileTextOutlined />,
  [AssetType.PREFAB]: <FileUnknownOutlined />,
  [AssetType.SCENE]: <FolderOutlined />,
  [AssetType.ANIMATION]: <VideoCameraOutlined />,
  [AssetType.PARTICLE]: <FileUnknownOutlined />,
  [AssetType.OTHER]: <FileUnknownOutlined />
};

/**
 * 缓存管理器
 */
class ThumbnailCache {
  private static instance: ThumbnailCache;
  private cache = new Map<string, { blob: Blob; timestamp: number }>();
  private maxSize = 100; // 最大缓存数量
  private maxAge = 10 * 60 * 1000; // 10分钟

  public static getInstance(): ThumbnailCache {
    if (!ThumbnailCache.instance) {
      ThumbnailCache.instance = new ThumbnailCache();
    }
    return ThumbnailCache.instance;
  }

  public get(url: string): Blob | null {
    const item = this.cache.get(url);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() - item.timestamp > this.maxAge) {
      this.cache.delete(url);
      return null;
    }

    return item.blob;
  }

  public set(url: string, blob: Blob): void {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(url, {
      blob,
      timestamp: Date.now()
    });
  }

  public clear(): void {
    this.cache.clear();
  }

  public cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.maxAge) {
        this.cache.delete(key);
      }
    }
  }
}

/**
 * 资产缩略图组件
 */
export const AssetThumbnail: React.FC<AssetThumbnailProps> = ({
  asset,
  size = 100,
  enableCache = true,
  enableLazyLoad = true,
  onClick,
  onDoubleClick,
  onLoad,
  onError
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [hasError, setHasError] = useState(false);
  const [isVisible, setIsVisible] = useState(!enableLazyLoad);
  
  const imgRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const cache = ThumbnailCache.getInstance();

  /**
   * 加载缩略图
   */
  const loadThumbnail = useCallback(async () => {
    if (!asset.thumbnail || isLoading) return;

    setIsLoading(true);
    setHasError(false);

    try {
      // 检查缓存
      if (enableCache) {
        const cachedBlob = cache.get(asset.thumbnail);
        if (cachedBlob) {
          const url = URL.createObjectURL(cachedBlob);
          setThumbnailUrl(url);
          setIsLoading(false);
          onLoad?.();
          return;
        }
      }

      // 加载图片
      const response = await fetch(asset.thumbnail);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const blob = await response.blob();

      // 缓存blob
      if (enableCache) {
        cache.set(asset.thumbnail, blob);
      }

      // 创建URL
      const url = URL.createObjectURL(blob);
      setThumbnailUrl(url);
      onLoad?.();
    } catch (error) {
      console.error('Failed to load thumbnail:', error);
      setHasError(true);
      onError?.(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [asset.thumbnail, isLoading, enableCache, cache, onLoad, onError]);

  /**
   * 设置懒加载观察器
   */
  useEffect(() => {
    if (!enableLazyLoad || !imgRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsVisible(true);
          observerRef.current?.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    observerRef.current.observe(imgRef.current);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [enableLazyLoad]);

  /**
   * 当可见时加载缩略图
   */
  useEffect(() => {
    if (isVisible && asset.thumbnail) {
      loadThumbnail();
    }
  }, [isVisible, asset.thumbnail, loadThumbnail]);

  /**
   * 清理URL对象
   */
  useEffect(() => {
    return () => {
      if (thumbnailUrl) {
        URL.revokeObjectURL(thumbnailUrl);
      }
    };
  }, [thumbnailUrl]);

  /**
   * 处理点击事件
   */
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onClick?.();
  }, [onClick]);

  /**
   * 处理双击事件
   */
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onDoubleClick?.();
  }, [onDoubleClick]);

  /**
   * 渲染缩略图内容
   */
  const renderThumbnailContent = () => {
    if (isLoading) {
      return (
        <div className="thumbnail-loading">
          <Spin size="small" />
        </div>
      );
    }

    if (hasError || !asset.thumbnail) {
      return (
        <div className="thumbnail-fallback">
          <span className="fallback-icon" style={{ fontSize: size * 0.4 }}>
            {DEFAULT_ICONS[asset.type]}
          </span>
        </div>
      );
    }

    if (thumbnailUrl) {
      return (
        <Image
          src={thumbnailUrl}
          alt={asset.name}
          preview={false}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }}
          onError={() => setHasError(true)}
        />
      );
    }

    return (
      <div className="thumbnail-placeholder">
        <span className="placeholder-icon" style={{ fontSize: size * 0.4 }}>
          {DEFAULT_ICONS[asset.type]}
        </span>
      </div>
    );
  };

  return (
    <div
      ref={imgRef}
      className={`asset-thumbnail ${hasError ? 'has-error' : ''} ${isLoading ? 'loading' : ''}`}
      style={{
        width: size,
        height: size,
        cursor: onClick || onDoubleClick ? 'pointer' : 'default'
      }}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      title={asset.name}
    >
      {renderThumbnailContent()}
      
      {/* 资产类型标识 */}
      <div className="asset-type-badge">
        <span className="type-icon">
          {DEFAULT_ICONS[asset.type]}
        </span>
      </div>
    </div>
  );
};

export default AssetThumbnail;
