/**
 * 缓存失效策略服务
 * 实现智能缓存失效、依赖关系管理和一致性保证
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MultiLevelCacheManager } from './MultiLevelCacheManager';
import { EventEmitter } from 'events';

/**
 * 失效策略枚举
 */
export enum InvalidationStrategy {
  IMMEDIATE = 'immediate', // 立即失效
  DELAYED = 'delayed', // 延迟失效
  LAZY = 'lazy', // 懒惰失效
  WRITE_THROUGH = 'write_through', // 写穿透
  WRITE_BEHIND = 'write_behind' // 写回
}

/**
 * 依赖关系类型
 */
export enum DependencyType {
  PARENT_CHILD = 'parent_child', // 父子关系
  SIBLING = 'sibling', // 兄弟关系
  REFERENCE = 'reference', // 引用关系
  COMPUTED = 'computed' // 计算关系
}

/**
 * 缓存依赖关系
 */
export interface CacheDependency {
  /** 依赖ID */
  id: string;
  /** 源缓存键 */
  sourceKey: string;
  /** 目标缓存键 */
  targetKey: string;
  /** 依赖类型 */
  type: DependencyType;
  /** 失效策略 */
  strategy: InvalidationStrategy;
  /** 延迟时间（毫秒） */
  delay?: number;
  /** 是否启用 */
  enabled: boolean;
  /** 创建时间 */
  createdAt: number;
}

/**
 * 失效任务
 */
export interface InvalidationTask {
  /** 任务ID */
  id: string;
  /** 缓存键 */
  key: string;
  /** 失效策略 */
  strategy: InvalidationStrategy;
  /** 执行时间 */
  executeAt: number;
  /** 重试次数 */
  retryCount: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 创建时间 */
  createdAt: number;
  /** 相关依赖 */
  dependencies: string[];
}

/**
 * 失效事件
 */
export interface InvalidationEvent {
  /** 事件ID */
  id: string;
  /** 事件类型 */
  type: 'data_change' | 'time_based' | 'manual' | 'dependency';
  /** 触发的缓存键 */
  keys: string[];
  /** 事件时间 */
  timestamp: number;
  /** 事件源 */
  source: string;
  /** 事件数据 */
  data?: any;
}

/**
 * 失效统计
 */
export interface InvalidationStats {
  /** 总失效次数 */
  totalInvalidations: number;
  /** 成功失效次数 */
  successfulInvalidations: number;
  /** 失败失效次数 */
  failedInvalidations: number;
  /** 依赖失效次数 */
  dependencyInvalidations: number;
  /** 平均失效时间 */
  averageInvalidationTime: number;
  /** 最后失效时间 */
  lastInvalidationTime: number;
}

/**
 * 缓存失效策略服务
 */
@Injectable()
export class CacheInvalidationService extends EventEmitter {
  private readonly logger = new Logger(CacheInvalidationService.name);

  /** 依赖关系映射 */
  private dependencies: Map<string, CacheDependency[]> = new Map();
  /** 失效任务队列 */
  private invalidationTasks: Map<string, InvalidationTask> = new Map();
  /** 失效统计 */
  private stats: InvalidationStats;

  /** 配置参数 */
  private readonly enableDependencyTracking: boolean;
  private readonly maxRetries: number;
  private readonly defaultDelay: number;
  private readonly batchSize: number;

  /** 定时器 */
  private processingTimer?: NodeJS.Timeout;

  constructor(
    private readonly cacheManager: MultiLevelCacheManager,
    private readonly configService: ConfigService
  ) {
    super();

    this.enableDependencyTracking = this.configService.get<boolean>('CACHE_DEPENDENCY_TRACKING', true);
    this.maxRetries = this.configService.get<number>('CACHE_INVALIDATION_MAX_RETRIES', 3);
    this.defaultDelay = this.configService.get<number>('CACHE_INVALIDATION_DEFAULT_DELAY', 1000);
    this.batchSize = this.configService.get<number>('CACHE_INVALIDATION_BATCH_SIZE', 50);

    this.initializeStats();
    this.startProcessing();
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    this.stats = {
      totalInvalidations: 0,
      successfulInvalidations: 0,
      failedInvalidations: 0,
      dependencyInvalidations: 0,
      averageInvalidationTime: 0,
      lastInvalidationTime: 0
    };
  }

  /**
   * 添加缓存依赖关系
   */
  public addDependency(dependency: Omit<CacheDependency, 'id' | 'createdAt'>): string {
    if (!this.enableDependencyTracking) {
      return '';
    }

    const dependencyId = this.generateDependencyId();
    const fullDependency: CacheDependency = {
      ...dependency,
      id: dependencyId,
      createdAt: Date.now()
    };

    // 添加到源键的依赖列表
    if (!this.dependencies.has(dependency.sourceKey)) {
      this.dependencies.set(dependency.sourceKey, []);
    }
    this.dependencies.get(dependency.sourceKey)!.push(fullDependency);

    this.logger.debug(`添加缓存依赖: ${dependency.sourceKey} -> ${dependency.targetKey}`);
    this.emit('dependencyAdded', fullDependency);

    return dependencyId;
  }

  /**
   * 移除缓存依赖关系
   */
  public removeDependency(dependencyId: string): boolean {
    for (const [sourceKey, deps] of this.dependencies.entries()) {
      const index = deps.findIndex(dep => dep.id === dependencyId);
      if (index !== -1) {
        const removed = deps.splice(index, 1)[0];
        if (deps.length === 0) {
          this.dependencies.delete(sourceKey);
        }
        this.logger.debug(`移除缓存依赖: ${removed.sourceKey} -> ${removed.targetKey}`);
        this.emit('dependencyRemoved', removed);
        return true;
      }
    }
    return false;
  }

  /**
   * 失效缓存
   */
  public async invalidate(
    keys: string | string[],
    strategy: InvalidationStrategy = InvalidationStrategy.IMMEDIATE,
    options?: {
      delay?: number;
      cascade?: boolean;
      source?: string;
      data?: any;
    }
  ): Promise<void> {
    const keyArray = Array.isArray(keys) ? keys : [keys];
    const delay = options?.delay || this.defaultDelay;
    const cascade = options?.cascade !== false;
    const source = options?.source || 'manual';

    // 创建失效事件
    const event: InvalidationEvent = {
      id: this.generateEventId(),
      type: source === 'manual' ? 'manual' : 'data_change',
      keys: keyArray,
      timestamp: Date.now(),
      source,
      data: options?.data
    };

    this.emit('invalidationEvent', event);

    // 处理每个键
    for (const key of keyArray) {
      await this.processInvalidation(key, strategy, delay);

      // 级联失效依赖项
      if (cascade && this.enableDependencyTracking) {
        await this.cascadeInvalidation(key);
      }
    }
  }

  /**
   * 处理单个键的失效
   */
  private async processInvalidation(
    key: string,
    strategy: InvalidationStrategy,
    delay: number
  ): Promise<void> {
    const startTime = Date.now();

    try {
      switch (strategy) {
        case InvalidationStrategy.IMMEDIATE:
          await this.cacheManager.delete(key);
          break;

        case InvalidationStrategy.DELAYED:
          this.scheduleInvalidation(key, delay);
          return; // 延迟执行，不更新统计

        case InvalidationStrategy.LAZY:
          // 懒惰失效：标记为过期但不立即删除
          await this.markAsExpired(key);
          break;

        case InvalidationStrategy.WRITE_THROUGH:
          // 写穿透：立即删除并通知数据源更新
          await this.cacheManager.delete(key);
          this.emit('writeThrough', { key });
          break;

        case InvalidationStrategy.WRITE_BEHIND:
          // 写回：延迟删除并批量更新数据源
          this.scheduleWriteBehind(key, delay);
          return; // 延迟执行，不更新统计

        default:
          await this.cacheManager.delete(key);
      }

      // 更新统计
      const executionTime = Date.now() - startTime;
      this.updateStats(true, executionTime);

      this.logger.debug(`缓存失效完成: ${key}, 策略: ${strategy}, 耗时: ${executionTime}ms`);

    } catch (error) {
      this.logger.error(`缓存失效失败: ${key}`, error);
      this.updateStats(false, Date.now() - startTime);
      throw error;
    }
  }

  /**
   * 级联失效依赖项
   */
  private async cascadeInvalidation(sourceKey: string): Promise<void> {
    const dependencies = this.dependencies.get(sourceKey);
    if (!dependencies || dependencies.length === 0) {
      return;
    }

    const cascadeTasks: Promise<void>[] = [];

    for (const dependency of dependencies) {
      if (!dependency.enabled) continue;

      cascadeTasks.push(
        this.processInvalidation(
          dependency.targetKey,
          dependency.strategy,
          dependency.delay || this.defaultDelay
        ).catch(error => {
          this.logger.error(`依赖失效失败: ${dependency.targetKey}`, error);
        })
      );
    }

    await Promise.allSettled(cascadeTasks);
    this.stats.dependencyInvalidations += dependencies.filter(d => d.enabled).length;
  }

  /**
   * 调度延迟失效
   */
  private scheduleInvalidation(key: string, delay: number): void {
    const taskId = this.generateTaskId();
    const task: InvalidationTask = {
      id: taskId,
      key,
      strategy: InvalidationStrategy.DELAYED,
      executeAt: Date.now() + delay,
      retryCount: 0,
      maxRetries: this.maxRetries,
      createdAt: Date.now(),
      dependencies: []
    };

    this.invalidationTasks.set(taskId, task);
    this.logger.debug(`调度延迟失效: ${key}, 延迟: ${delay}ms`);
  }

  /**
   * 调度写回任务
   */
  private scheduleWriteBehind(key: string, delay: number): void {
    const taskId = this.generateTaskId();
    const task: InvalidationTask = {
      id: taskId,
      key,
      strategy: InvalidationStrategy.WRITE_BEHIND,
      executeAt: Date.now() + delay,
      retryCount: 0,
      maxRetries: this.maxRetries,
      createdAt: Date.now(),
      dependencies: []
    };

    this.invalidationTasks.set(taskId, task);
    this.logger.debug(`调度写回任务: ${key}, 延迟: ${delay}ms`);
  }

  /**
   * 标记为过期
   */
  private async markAsExpired(key: string): Promise<void> {
    // 在实际实现中，这里应该设置一个过期标记
    // 简化实现：直接删除
    await this.cacheManager.delete(key);
  }

  /**
   * 开始处理任务队列
   */
  private startProcessing(): void {
    this.processingTimer = setInterval(() => {
      this.processTaskQueue();
    }, 1000); // 每秒处理一次
  }

  /**
   * 处理任务队列
   */
  private async processTaskQueue(): Promise<void> {
    const now = Date.now();
    const readyTasks = Array.from(this.invalidationTasks.values())
      .filter(task => task.executeAt <= now)
      .sort((a, b) => a.executeAt - b.executeAt)
      .slice(0, this.batchSize);

    if (readyTasks.length === 0) return;

    const processingPromises = readyTasks.map(async (task) => {
      try {
        if (task.strategy === InvalidationStrategy.WRITE_BEHIND) {
          await this.processWriteBehindTask(task);
        } else {
          await this.cacheManager.delete(task.key);
        }

        this.invalidationTasks.delete(task.id);
        this.updateStats(true, Date.now() - task.createdAt);

      } catch (error) {
        this.logger.error(`任务执行失败: ${task.key}`, error);
        
        if (task.retryCount < task.maxRetries) {
          task.retryCount++;
          task.executeAt = now + this.defaultDelay * Math.pow(2, task.retryCount); // 指数退避
        } else {
          this.invalidationTasks.delete(task.id);
          this.updateStats(false, Date.now() - task.createdAt);
        }
      }
    });

    await Promise.allSettled(processingPromises);
  }

  /**
   * 处理写回任务
   */
  private async processWriteBehindTask(task: InvalidationTask): Promise<void> {
    // 删除缓存
    await this.cacheManager.delete(task.key);
    
    // 通知数据源更新
    this.emit('writeBehind', { key: task.key, task });
  }

  /**
   * 更新统计信息
   */
  private updateStats(success: boolean, executionTime: number): void {
    this.stats.totalInvalidations++;
    this.stats.lastInvalidationTime = Date.now();

    if (success) {
      this.stats.successfulInvalidations++;
      
      // 更新平均执行时间
      if (this.stats.successfulInvalidations === 1) {
        this.stats.averageInvalidationTime = executionTime;
      } else {
        this.stats.averageInvalidationTime = 
          (this.stats.averageInvalidationTime * (this.stats.successfulInvalidations - 1) + executionTime) / 
          this.stats.successfulInvalidations;
      }
    } else {
      this.stats.failedInvalidations++;
    }
  }

  /**
   * 生成依赖ID
   */
  private generateDependencyId(): string {
    return `dep_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取依赖关系
   */
  public getDependencies(sourceKey?: string): CacheDependency[] {
    if (sourceKey) {
      return this.dependencies.get(sourceKey) || [];
    }
    
    const allDependencies: CacheDependency[] = [];
    for (const deps of this.dependencies.values()) {
      allDependencies.push(...deps);
    }
    return allDependencies;
  }

  /**
   * 获取待处理任务
   */
  public getPendingTasks(): InvalidationTask[] {
    return Array.from(this.invalidationTasks.values())
      .sort((a, b) => a.executeAt - b.executeAt);
  }

  /**
   * 获取失效统计
   */
  public getStats(): InvalidationStats {
    return { ...this.stats };
  }

  /**
   * 清理过期任务
   */
  public cleanupExpiredTasks(): void {
    const now = Date.now();
    const expiredThreshold = now - 24 * 3600000; // 24小时前

    for (const [taskId, task] of this.invalidationTasks.entries()) {
      if (task.createdAt < expiredThreshold) {
        this.invalidationTasks.delete(taskId);
      }
    }
  }

  /**
   * 销毁服务
   */
  public destroy(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
    }

    this.removeAllListeners();
    this.logger.log('缓存失效策略服务已销毁');
  }
}
