/**
 * 高级地形编辑器组件
 * 提供完整的地形编辑功能，包括高度图编辑、纹理混合和植被分布
 */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Slider,
  Button,
  ButtonGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Brush as BrushIcon,
  Landscape as LandscapeIcon,
  Nature as NatureIcon,
  Texture as TextureIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  Save as SaveIcon,
  FolderOpen as LoadIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { useEngine } from '../../hooks/useEngine';

/**
 * 笔刷类型
 */
enum BrushType {
  RAISE = 'raise',
  LOWER = 'lower',
  SMOOTH = 'smooth',
  FLATTEN = 'flatten',
  NOISE = 'noise',
  EROSION = 'erosion',
  PAINT = 'paint'
}

/**
 * 植被类型
 */
enum VegetationType {
  GRASS = 'grass',
  TREES = 'trees',
  BUSHES = 'bushes',
  FLOWERS = 'flowers',
  ROCKS = 'rocks'
}

/**
 * 笔刷设置
 */
interface BrushSettings {
  type: BrushType;
  size: number;
  strength: number;
  falloff: number;
  targetHeight: number;
  textureIndex: number;
  noiseParams: {
    seed: number;
    scale: number;
    octaves: number;
  };
}

/**
 * 植被配置
 */
interface VegetationConfig {
  type: VegetationType;
  density: number;
  minHeight: number;
  maxHeight: number;
  minSlope: number;
  maxSlope: number;
  seed: number;
  scaleRange: { min: number; max: number };
  randomRotation: boolean;
}

/**
 * 高级地形编辑器组件属性
 */
interface AdvancedTerrainEditorProps {
  onTerrainChange?: (terrain: any) => void;
  onBrushOperation?: (operation: any) => void;
}

/**
 * 高级地形编辑器组件
 */
export const AdvancedTerrainEditor: React.FC<AdvancedTerrainEditorProps> = ({
  onTerrainChange,
  onBrushOperation
}) => {
  const { engine } = useEngine();
  const [activeTab, setActiveTab] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedTerrain, setSelectedTerrain] = useState<any>(null);
  
  // 笔刷设置
  const [brushSettings, setBrushSettings] = useState<BrushSettings>({
    type: BrushType.RAISE,
    size: 5,
    strength: 0.5,
    falloff: 2,
    targetHeight: 0.5,
    textureIndex: 0,
    noiseParams: {
      seed: 12345,
      scale: 0.1,
      octaves: 4
    }
  });

  // 植被配置
  const [vegetationConfig, setVegetationConfig] = useState<VegetationConfig>({
    type: VegetationType.GRASS,
    density: 0.1,
    minHeight: 0,
    maxHeight: 100,
    minSlope: 0,
    maxSlope: 1,
    seed: 54321,
    scaleRange: { min: 0.8, max: 1.2 },
    randomRotation: true
  });

  // 纹理层
  const [textureLayers, setTextureLayers] = useState<any[]>([]);
  
  // 操作历史
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  const terrainEditorSystemRef = useRef<any>(null);

  /**
   * 初始化地形编辑器系统
   */
  useEffect(() => {
    if (engine) {
      const terrainEditorSystem = engine.getSystem('TerrainEditorSystem');
      if (terrainEditorSystem) {
        terrainEditorSystemRef.current = terrainEditorSystem;
        
        // 监听事件
        terrainEditorSystem.on('terrainSelected', handleTerrainSelected);
        terrainEditorSystem.on('brushOperationCompleted', handleBrushOperationCompleted);
        terrainEditorSystem.on('operationUndone', updateHistoryState);
        terrainEditorSystem.on('operationRedone', updateHistoryState);
        
        return () => {
          terrainEditorSystem.off('terrainSelected', handleTerrainSelected);
          terrainEditorSystem.off('brushOperationCompleted', handleBrushOperationCompleted);
          terrainEditorSystem.off('operationUndone', updateHistoryState);
          terrainEditorSystem.off('operationRedone', updateHistoryState);
        };
      }
    }
  }, [engine]);

  /**
   * 处理地形选择
   */
  const handleTerrainSelected = useCallback((terrain: any) => {
    setSelectedTerrain(terrain);
    onTerrainChange?.(terrain);
  }, [onTerrainChange]);

  /**
   * 处理笔刷操作完成
   */
  const handleBrushOperationCompleted = useCallback((operation: any) => {
    onBrushOperation?.(operation);
    updateHistoryState();
  }, [onBrushOperation]);

  /**
   * 更新历史状态
   */
  const updateHistoryState = useCallback(() => {
    if (terrainEditorSystemRef.current) {
      const stats = terrainEditorSystemRef.current.getPerformanceStats();
      setCanUndo(stats.historyIndex >= 0);
      setCanRedo(stats.historyIndex < stats.operationHistoryLength - 1);
    }
  }, []);

  /**
   * 处理笔刷设置变化
   */
  const handleBrushSettingChange = useCallback((key: keyof BrushSettings, value: any) => {
    setBrushSettings(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  /**
   * 处理植被配置变化
   */
  const handleVegetationConfigChange = useCallback((key: keyof VegetationConfig, value: any) => {
    setVegetationConfig(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  /**
   * 执行笔刷操作
   */
  const performBrushOperation = useCallback((position: { x: number; y: number }) => {
    if (!terrainEditorSystemRef.current || !selectedTerrain) return;

    const operation = {
      type: brushSettings.type,
      position: { x: position.x, y: position.y },
      size: brushSettings.size,
      strength: brushSettings.strength,
      falloff: brushSettings.falloff,
      targetHeight: brushSettings.targetHeight,
      textureIndex: brushSettings.textureIndex,
      noiseParams: brushSettings.noiseParams
    };

    terrainEditorSystemRef.current.performBrushOperation(operation);
  }, [brushSettings, selectedTerrain]);

  /**
   * 生成植被
   */
  const generateVegetation = useCallback(() => {
    if (!terrainEditorSystemRef.current) return;
    terrainEditorSystemRef.current.generateVegetation(vegetationConfig);
  }, [vegetationConfig]);

  /**
   * 清除植被
   */
  const clearVegetation = useCallback(() => {
    if (!terrainEditorSystemRef.current) return;
    terrainEditorSystemRef.current.clearVegetation();
  }, []);

  /**
   * 撤销操作
   */
  const undo = useCallback(() => {
    if (!terrainEditorSystemRef.current) return;
    terrainEditorSystemRef.current.undo();
  }, []);

  /**
   * 重做操作
   */
  const redo = useCallback(() => {
    if (!terrainEditorSystemRef.current) return;
    terrainEditorSystemRef.current.redo();
  }, []);

  /**
   * 渲染笔刷工具面板
   */
  const renderBrushPanel = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        <BrushIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        笔刷工具
      </Typography>
      
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>笔刷类型</InputLabel>
            <Select
              value={brushSettings.type}
              onChange={(e) => handleBrushSettingChange('type', e.target.value)}
            >
              <MenuItem value={BrushType.RAISE}>抬升</MenuItem>
              <MenuItem value={BrushType.LOWER}>降低</MenuItem>
              <MenuItem value={BrushType.SMOOTH}>平滑</MenuItem>
              <MenuItem value={BrushType.FLATTEN}>平整</MenuItem>
              <MenuItem value={BrushType.NOISE}>噪声</MenuItem>
              <MenuItem value={BrushType.EROSION}>侵蚀</MenuItem>
              <MenuItem value={BrushType.PAINT}>绘制</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
          <Typography gutterBottom>笔刷大小: {brushSettings.size}</Typography>
          <Slider
            value={brushSettings.size}
            onChange={(_, value) => handleBrushSettingChange('size', value)}
            min={1}
            max={50}
            step={1}
          />
        </Grid>
        
        <Grid item xs={12}>
          <Typography gutterBottom>笔刷强度: {brushSettings.strength}</Typography>
          <Slider
            value={brushSettings.strength}
            onChange={(_, value) => handleBrushSettingChange('strength', value)}
            min={0.1}
            max={2}
            step={0.1}
          />
        </Grid>
        
        <Grid item xs={12}>
          <Typography gutterBottom>衰减: {brushSettings.falloff}</Typography>
          <Slider
            value={brushSettings.falloff}
            onChange={(_, value) => handleBrushSettingChange('falloff', value)}
            min={0.5}
            max={5}
            step={0.1}
          />
        </Grid>
        
        {brushSettings.type === BrushType.FLATTEN && (
          <Grid item xs={12}>
            <Typography gutterBottom>目标高度: {brushSettings.targetHeight}</Typography>
            <Slider
              value={brushSettings.targetHeight}
              onChange={(_, value) => handleBrushSettingChange('targetHeight', value)}
              min={0}
              max={1}
              step={0.01}
            />
          </Grid>
        )}
        
        {brushSettings.type === BrushType.NOISE && (
          <>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="噪声种子"
                type="number"
                value={brushSettings.noiseParams.seed}
                onChange={(e) => handleBrushSettingChange('noiseParams', {
                  ...brushSettings.noiseParams,
                  seed: parseInt(e.target.value)
                })}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography gutterBottom>噪声缩放: {brushSettings.noiseParams.scale}</Typography>
              <Slider
                value={brushSettings.noiseParams.scale}
                onChange={(_, value) => handleBrushSettingChange('noiseParams', {
                  ...brushSettings.noiseParams,
                  scale: value
                })}
                min={0.01}
                max={1}
                step={0.01}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography gutterBottom>噪声层数: {brushSettings.noiseParams.octaves}</Typography>
              <Slider
                value={brushSettings.noiseParams.octaves}
                onChange={(_, value) => handleBrushSettingChange('noiseParams', {
                  ...brushSettings.noiseParams,
                  octaves: value
                })}
                min={1}
                max={8}
                step={1}
              />
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );

  /**
   * 渲染纹理面板
   */
  const renderTexturePanel = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        <TextureIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        纹理混合
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Button
            variant="outlined"
            fullWidth
            onClick={() => {
              // 添加纹理层的逻辑
              console.log('添加纹理层');
            }}
          >
            添加纹理层
          </Button>
        </Grid>

        {textureLayers.map((layer, index) => (
          <Grid item xs={12} key={index}>
            <Card variant="outlined">
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="subtitle2">纹理层 {index + 1}</Typography>
                  <IconButton
                    size="small"
                    onClick={() => {
                      const newLayers = textureLayers.filter((_, i) => i !== index);
                      setTextureLayers(newLayers);
                    }}
                  >
                    <ClearIcon />
                  </IconButton>
                </Box>

                <Typography gutterBottom>权重: {layer.weight || 1}</Typography>
                <Slider
                  value={layer.weight || 1}
                  onChange={(_, value) => {
                    const newLayers = [...textureLayers];
                    newLayers[index] = { ...layer, weight: value };
                    setTextureLayers(newLayers);
                  }}
                  min={0}
                  max={1}
                  step={0.01}
                />

                <Typography gutterBottom>缩放: {layer.scale || 1}</Typography>
                <Slider
                  value={layer.scale || 1}
                  onChange={(_, value) => {
                    const newLayers = [...textureLayers];
                    newLayers[index] = { ...layer, scale: value };
                    setTextureLayers(newLayers);
                  }}
                  min={0.1}
                  max={10}
                  step={0.1}
                />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  /**
   * 渲染植被面板
   */
  const renderVegetationPanel = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        <NatureIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        植被分布
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>植被类型</InputLabel>
            <Select
              value={vegetationConfig.type}
              onChange={(e) => handleVegetationConfigChange('type', e.target.value)}
            >
              <MenuItem value={VegetationType.GRASS}>草地</MenuItem>
              <MenuItem value={VegetationType.TREES}>树木</MenuItem>
              <MenuItem value={VegetationType.BUSHES}>灌木</MenuItem>
              <MenuItem value={VegetationType.FLOWERS}>花朵</MenuItem>
              <MenuItem value={VegetationType.ROCKS}>岩石</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <Typography gutterBottom>密度: {vegetationConfig.density}</Typography>
          <Slider
            value={vegetationConfig.density}
            onChange={(_, value) => handleVegetationConfigChange('density', value)}
            min={0.01}
            max={1}
            step={0.01}
          />
        </Grid>

        <Grid item xs={6}>
          <Typography gutterBottom>最小高度: {vegetationConfig.minHeight}</Typography>
          <Slider
            value={vegetationConfig.minHeight}
            onChange={(_, value) => handleVegetationConfigChange('minHeight', value)}
            min={0}
            max={100}
            step={1}
          />
        </Grid>

        <Grid item xs={6}>
          <Typography gutterBottom>最大高度: {vegetationConfig.maxHeight}</Typography>
          <Slider
            value={vegetationConfig.maxHeight}
            onChange={(_, value) => handleVegetationConfigChange('maxHeight', value)}
            min={0}
            max={100}
            step={1}
          />
        </Grid>

        <Grid item xs={6}>
          <Typography gutterBottom>最小坡度: {vegetationConfig.minSlope}</Typography>
          <Slider
            value={vegetationConfig.minSlope}
            onChange={(_, value) => handleVegetationConfigChange('minSlope', value)}
            min={0}
            max={1}
            step={0.01}
          />
        </Grid>

        <Grid item xs={6}>
          <Typography gutterBottom>最大坡度: {vegetationConfig.maxSlope}</Typography>
          <Slider
            value={vegetationConfig.maxSlope}
            onChange={(_, value) => handleVegetationConfigChange('maxSlope', value)}
            min={0}
            max={1}
            step={0.01}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label="随机种子"
            type="number"
            value={vegetationConfig.seed}
            onChange={(e) => handleVegetationConfigChange('seed', parseInt(e.target.value))}
          />
        </Grid>

        <Grid item xs={6}>
          <Typography gutterBottom>最小缩放: {vegetationConfig.scaleRange.min}</Typography>
          <Slider
            value={vegetationConfig.scaleRange.min}
            onChange={(_, value) => handleVegetationConfigChange('scaleRange', {
              ...vegetationConfig.scaleRange,
              min: value
            })}
            min={0.1}
            max={2}
            step={0.1}
          />
        </Grid>

        <Grid item xs={6}>
          <Typography gutterBottom>最大缩放: {vegetationConfig.scaleRange.max}</Typography>
          <Slider
            value={vegetationConfig.scaleRange.max}
            onChange={(_, value) => handleVegetationConfigChange('scaleRange', {
              ...vegetationConfig.scaleRange,
              max: value
            })}
            min={0.1}
            max={2}
            step={0.1}
          />
        </Grid>

        <Grid item xs={12}>
          <FormControlLabel
            control={
              <Switch
                checked={vegetationConfig.randomRotation}
                onChange={(e) => handleVegetationConfigChange('randomRotation', e.target.checked)}
              />
            }
            label="随机旋转"
          />
        </Grid>

        <Grid item xs={12}>
          <ButtonGroup fullWidth>
            <Button
              variant="contained"
              onClick={generateVegetation}
              disabled={!selectedTerrain}
            >
              生成植被
            </Button>
            <Button
              variant="outlined"
              onClick={clearVegetation}
              disabled={!selectedTerrain}
            >
              清除植被
            </Button>
          </ButtonGroup>
        </Grid>
      </Grid>
    </Box>
  );

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5">
            <LandscapeIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            高级地形编辑器
          </Typography>

          <Box>
            <Tooltip title="撤销">
              <span>
                <IconButton onClick={undo} disabled={!canUndo}>
                  <UndoIcon />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title="重做">
              <span>
                <IconButton onClick={redo} disabled={!canRedo}>
                  <RedoIcon />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title="保存">
              <IconButton onClick={() => console.log('保存地形')}>
                <SaveIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="加载">
              <IconButton onClick={() => console.log('加载地形')}>
                <LoadIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {selectedTerrain ? (
          <Box>
            <Chip
              label={`已选择地形: ${selectedTerrain.id}`}
              color="primary"
              sx={{ mb: 2 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={isEditing}
                  onChange={(e) => setIsEditing(e.target.checked)}
                />
              }
              label="启用编辑模式"
              sx={{ mb: 2 }}
            />

            <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
              <Tab label="笔刷工具" />
              <Tab label="纹理混合" />
              <Tab label="植被分布" />
            </Tabs>

            <Box mt={2}>
              {activeTab === 0 && renderBrushPanel()}
              {activeTab === 1 && renderTexturePanel()}
              {activeTab === 2 && renderVegetationPanel()}
            </Box>
          </Box>
        ) : (
          <Box textAlign="center" py={4}>
            <Typography variant="body1" color="textSecondary">
              请先选择一个地形对象进行编辑
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};
