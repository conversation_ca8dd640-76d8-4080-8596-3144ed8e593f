/**
 * 地形编辑器系统
 * 实现高度图编辑、纹理混合功能和植被分布系统
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { World } from '../core/World';
import type { Scene } from '../scene/Scene';
import { TerrainSystem } from './TerrainSystem';
import { TerrainComponent } from './components/TerrainComponent';
import { Debug } from '../utils/Debug';
import { EventEmitter } from '../utils/EventEmitter';
import { PerformanceMonitor } from '../utils/PerformanceMonitor';

/**
 * 笔刷类型
 */
export enum BrushType {
  RAISE = 'raise',
  LOWER = 'lower',
  SMOOTH = 'smooth',
  FLATTEN = 'flatten',
  NOISE = 'noise',
  EROSION = 'erosion',
  PAINT = 'paint'
}

/**
 * 植被类型
 */
export enum VegetationType {
  GRASS = 'grass',
  TREES = 'trees',
  BUSHES = 'bushes',
  FLOWERS = 'flowers',
  ROCKS = 'rocks'
}

/**
 * 笔刷操作
 */
export interface BrushOperation {
  /** 笔刷类型 */
  type: BrushType;
  /** 位置 */
  position: THREE.Vector2;
  /** 大小 */
  size: number;
  /** 强度 */
  strength: number;
  /** 衰减 */
  falloff: number;
  /** 目标高度（用于flatten） */
  targetHeight?: number;
  /** 纹理索引（用于paint） */
  textureIndex?: number;
  /** 噪声参数（用于noise） */
  noiseParams?: {
    seed: number;
    scale: number;
    octaves: number;
  };
}

/**
 * 植被分布配置
 */
export interface VegetationConfig {
  /** 植被类型 */
  type: VegetationType;
  /** 密度 */
  density: number;
  /** 最小高度 */
  minHeight: number;
  /** 最大高度 */
  maxHeight: number;
  /** 最小坡度 */
  minSlope: number;
  /** 最大坡度 */
  maxSlope: number;
  /** 随机种子 */
  seed: number;
  /** 模型路径列表 */
  models: string[];
  /** 缩放范围 */
  scaleRange: { min: number; max: number };
  /** 旋转随机 */
  randomRotation: boolean;
}

/**
 * 纹理混合层
 */
export interface TextureBlendLayer {
  /** 纹理 */
  texture: THREE.Texture;
  /** 法线贴图 */
  normalMap?: THREE.Texture;
  /** 纹理缩放 */
  scale: number;
  /** 权重 */
  weight: number;
  /** 混合模式 */
  blendMode: string;
}

/**
 * 地形编辑器系统
 */
export class TerrainEditorSystem extends System {
  /** 地形系统 */
  private terrainSystem: TerrainSystem | null = null;
  /** 活跃场景 */
  private activeScene: Scene | null = null;
  /** 当前选中的地形实体 */
  private selectedTerrainEntity: any = null;
  /** 笔刷预览网格 */
  private brushPreviewMesh: THREE.Mesh | null = null;
  /** 植被实例映射 */
  private vegetationInstances: Map<string, THREE.InstancedMesh[]> = new Map();
  /** 纹理混合层 */
  private textureBlendLayers: TextureBlendLayer[] = [];
  /** 操作历史 */
  private operationHistory: BrushOperation[] = [];
  /** 历史索引 */
  private historyIndex: number = -1;
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   */
  constructor() {
    super();
  }

  /**
   * 初始化系统
   */
  public async initialize(world: World): Promise<void> {
    await super.initialize(world);
    
    // 获取地形系统
    this.terrainSystem = world.getSystem('TerrainSystem') as TerrainSystem;
    if (!this.terrainSystem) {
      throw new Error('地形编辑器系统需要地形系统');
    }

    // 初始化笔刷预览
    this.initializeBrushPreview();
    
    Debug.log('TerrainEditorSystem', '地形编辑器系统初始化完成');
  }

  /**
   * 初始化笔刷预览
   */
  private initializeBrushPreview(): void {
    const geometry = new THREE.RingGeometry(0.5, 1, 32);
    const material = new THREE.MeshBasicMaterial({
      color: 0x00ff00,
      transparent: true,
      opacity: 0.5,
      side: THREE.DoubleSide
    });
    
    this.brushPreviewMesh = new THREE.Mesh(geometry, material);
    this.brushPreviewMesh.rotation.x = -Math.PI / 2;
    this.brushPreviewMesh.visible = false;
  }

  /**
   * 设置活跃场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;
    
    // 添加笔刷预览到场景
    if (this.brushPreviewMesh) {
      scene.getThreeScene().add(this.brushPreviewMesh);
    }
  }

  /**
   * 选择地形实体
   */
  public selectTerrainEntity(entity: any): void {
    this.selectedTerrainEntity = entity;
    this.eventEmitter.emit('terrainSelected', entity);
  }

  /**
   * 执行笔刷操作
   */
  public async performBrushOperation(operation: BrushOperation): Promise<void> {
    if (!this.selectedTerrainEntity || !this.terrainSystem) {
      return;
    }

    const startTime = performance.now();

    try {
      // 获取地形组件
      const terrainComponent = this.selectedTerrainEntity.getComponent('TerrainComponent') as TerrainComponent;
      if (!terrainComponent) {
        throw new Error('选中的实体没有地形组件');
      }

      // 执行具体的笔刷操作
      switch (operation.type) {
        case BrushType.RAISE:
          this.performRaiseBrush(terrainComponent, operation);
          break;
        case BrushType.LOWER:
          this.performLowerBrush(terrainComponent, operation);
          break;
        case BrushType.SMOOTH:
          this.performSmoothBrush(terrainComponent, operation);
          break;
        case BrushType.FLATTEN:
          this.performFlattenBrush(terrainComponent, operation);
          break;
        case BrushType.NOISE:
          this.performNoiseBrush(terrainComponent, operation);
          break;
        case BrushType.EROSION:
          this.performErosionBrush(terrainComponent, operation);
          break;
        case BrushType.PAINT:
          this.performPaintBrush(terrainComponent, operation);
          break;
      }

      // 标记地形需要更新
      terrainComponent.needsUpdate = true;
      terrainComponent.needsPhysicsUpdate = true;

      // 添加到操作历史
      this.addToHistory(operation);

      // 发出操作完成事件
      this.eventEmitter.emit('brushOperationCompleted', operation);

    } catch (error) {
      Debug.error('TerrainEditorSystem', '笔刷操作失败:', error);
    }

    // 性能监控
    const endTime = performance.now();
    this.performanceMonitor.recordMetric('brush_operation_time', endTime - startTime);
  }

  /**
   * 执行抬升笔刷
   */
  private performRaiseBrush(terrainComponent: TerrainComponent, operation: BrushOperation): void {
    const { position, size, strength, falloff } = operation;
    const { heightData, resolution, width, height } = terrainComponent;

    const centerX = Math.floor((position.x / width + 0.5) * resolution);
    const centerZ = Math.floor((position.y / height + 0.5) * resolution);
    const radius = Math.floor(size * resolution / width);

    for (let z = Math.max(0, centerZ - radius); z <= Math.min(resolution - 1, centerZ + radius); z++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(resolution - 1, centerX + radius); x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (z - centerZ) ** 2);
        if (distance <= radius) {
          const falloffFactor = Math.pow(1 - distance / radius, falloff);
          const index = z * resolution + x;
          heightData[index] = Math.min(1, heightData[index] + strength * falloffFactor * 0.01);
        }
      }
    }
  }

  /**
   * 执行降低笔刷
   */
  private performLowerBrush(terrainComponent: TerrainComponent, operation: BrushOperation): void {
    const { position, size, strength, falloff } = operation;
    const { heightData, resolution, width, height } = terrainComponent;

    const centerX = Math.floor((position.x / width + 0.5) * resolution);
    const centerZ = Math.floor((position.y / height + 0.5) * resolution);
    const radius = Math.floor(size * resolution / width);

    for (let z = Math.max(0, centerZ - radius); z <= Math.min(resolution - 1, centerZ + radius); z++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(resolution - 1, centerX + radius); x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (z - centerZ) ** 2);
        if (distance <= radius) {
          const falloffFactor = Math.pow(1 - distance / radius, falloff);
          const index = z * resolution + x;
          heightData[index] = Math.max(0, heightData[index] - strength * falloffFactor * 0.01);
        }
      }
    }
  }

  /**
   * 执行平滑笔刷
   */
  private performSmoothBrush(terrainComponent: TerrainComponent, operation: BrushOperation): void {
    const { position, size, strength, falloff } = operation;
    const { heightData, resolution, width, height } = terrainComponent;

    const centerX = Math.floor((position.x / width + 0.5) * resolution);
    const centerZ = Math.floor((position.y / height + 0.5) * resolution);
    const radius = Math.floor(size * resolution / width);

    // 创建临时数组存储平滑后的高度
    const smoothedHeights = new Float32Array(heightData.length);
    smoothedHeights.set(heightData);

    for (let z = Math.max(0, centerZ - radius); z <= Math.min(resolution - 1, centerZ + radius); z++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(resolution - 1, centerX + radius); x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (z - centerZ) ** 2);
        if (distance <= radius) {
          const falloffFactor = Math.pow(1 - distance / radius, falloff);
          const index = z * resolution + x;
          
          // 计算周围点的平均高度
          let sum = 0;
          let count = 0;
          for (let dz = -1; dz <= 1; dz++) {
            for (let dx = -1; dx <= 1; dx++) {
              const nx = x + dx;
              const nz = z + dz;
              if (nx >= 0 && nx < resolution && nz >= 0 && nz < resolution) {
                sum += heightData[nz * resolution + nx];
                count++;
              }
            }
          }
          
          const averageHeight = sum / count;
          smoothedHeights[index] = THREE.MathUtils.lerp(
            heightData[index],
            averageHeight,
            strength * falloffFactor
          );
        }
      }
    }

    // 应用平滑后的高度
    for (let i = 0; i < heightData.length; i++) {
      heightData[i] = smoothedHeights[i];
    }
  }

  /**
   * 执行平整笔刷
   */
  private performFlattenBrush(terrainComponent: TerrainComponent, operation: BrushOperation): void {
    const { position, size, strength, falloff, targetHeight = 0.5 } = operation;
    const { heightData, resolution, width, height } = terrainComponent;

    const centerX = Math.floor((position.x / width + 0.5) * resolution);
    const centerZ = Math.floor((position.y / height + 0.5) * resolution);
    const radius = Math.floor(size * resolution / width);

    for (let z = Math.max(0, centerZ - radius); z <= Math.min(resolution - 1, centerZ + radius); z++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(resolution - 1, centerX + radius); x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (z - centerZ) ** 2);
        if (distance <= radius) {
          const falloffFactor = Math.pow(1 - distance / radius, falloff);
          const index = z * resolution + x;
          heightData[index] = THREE.MathUtils.lerp(
            heightData[index],
            targetHeight,
            strength * falloffFactor
          );
        }
      }
    }
  }

  /**
   * 执行噪声笔刷
   */
  private performNoiseBrush(terrainComponent: TerrainComponent, operation: BrushOperation): void {
    const { position, size, strength, falloff, noiseParams } = operation;
    const { heightData, resolution, width, height } = terrainComponent;

    if (!noiseParams) return;

    const centerX = Math.floor((position.x / width + 0.5) * resolution);
    const centerZ = Math.floor((position.y / height + 0.5) * resolution);
    const radius = Math.floor(size * resolution / width);

    for (let z = Math.max(0, centerZ - radius); z <= Math.min(resolution - 1, centerZ + radius); z++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(resolution - 1, centerX + radius); x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (z - centerZ) ** 2);
        if (distance <= radius) {
          const falloffFactor = Math.pow(1 - distance / radius, falloff);
          const index = z * resolution + x;

          // 生成噪声值
          const noiseValue = this.generateNoise(
            x * noiseParams.scale,
            z * noiseParams.scale,
            noiseParams.seed,
            noiseParams.octaves
          );

          heightData[index] += noiseValue * strength * falloffFactor * 0.01;
          heightData[index] = Math.max(0, Math.min(1, heightData[index]));
        }
      }
    }
  }

  /**
   * 执行侵蚀笔刷
   */
  private performErosionBrush(terrainComponent: TerrainComponent, operation: BrushOperation): void {
    const { position, size, strength, falloff } = operation;
    const { heightData, resolution, width, height } = terrainComponent;

    const centerX = Math.floor((position.x / width + 0.5) * resolution);
    const centerZ = Math.floor((position.y / height + 0.5) * resolution);
    const radius = Math.floor(size * resolution / width);

    // 简化的热侵蚀算法
    for (let z = Math.max(1, centerZ - radius); z <= Math.min(resolution - 2, centerZ + radius); z++) {
      for (let x = Math.max(1, centerX - radius); x <= Math.min(resolution - 2, centerX + radius); x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (z - centerZ) ** 2);
        if (distance <= radius) {
          const falloffFactor = Math.pow(1 - distance / radius, falloff);
          const index = z * resolution + x;

          // 计算坡度
          const heightL = heightData[z * resolution + (x - 1)];
          const heightR = heightData[z * resolution + (x + 1)];
          const heightU = heightData[(z - 1) * resolution + x];
          const heightD = heightData[(z + 1) * resolution + x];

          const slope = Math.abs(heightL - heightR) + Math.abs(heightU - heightD);

          // 根据坡度进行侵蚀
          if (slope > 0.1) {
            heightData[index] -= slope * strength * falloffFactor * 0.005;
            heightData[index] = Math.max(0, heightData[index]);
          }
        }
      }
    }
  }

  /**
   * 执行绘制笔刷（纹理混合）
   */
  private performPaintBrush(terrainComponent: TerrainComponent, operation: BrushOperation): void {
    const { position, size, strength, falloff, textureIndex = 0 } = operation;

    // 这里需要实现纹理混合权重的修改
    // 由于当前TerrainComponent没有纹理混合数据，这里只是示例
    Debug.log('TerrainEditorSystem', `绘制纹理 ${textureIndex} 在位置 ${position.x}, ${position.y}`);
  }

  /**
   * 生成噪声
   */
  private generateNoise(x: number, y: number, seed: number, octaves: number): number {
    // 简化的Perlin噪声实现
    let value = 0;
    let amplitude = 1;
    let frequency = 1;

    for (let i = 0; i < octaves; i++) {
      value += this.noise(x * frequency + seed, y * frequency + seed) * amplitude;
      amplitude *= 0.5;
      frequency *= 2;
    }

    return value;
  }

  /**
   * 基础噪声函数
   */
  private noise(x: number, y: number): number {
    const n = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453;
    return (n - Math.floor(n)) * 2 - 1;
  }

  /**
   * 添加到操作历史
   */
  private addToHistory(operation: BrushOperation): void {
    // 移除当前索引之后的历史
    this.operationHistory = this.operationHistory.slice(0, this.historyIndex + 1);

    // 添加新操作
    this.operationHistory.push(operation);
    this.historyIndex++;

    // 限制历史长度
    const maxHistoryLength = 100;
    if (this.operationHistory.length > maxHistoryLength) {
      this.operationHistory.shift();
      this.historyIndex--;
    }
  }

  /**
   * 撤销操作
   */
  public undo(): boolean {
    if (this.historyIndex >= 0) {
      // 这里需要实现撤销逻辑
      // 由于地形修改是不可逆的，需要保存操作前的状态
      this.historyIndex--;
      this.eventEmitter.emit('operationUndone');
      return true;
    }
    return false;
  }

  /**
   * 重做操作
   */
  public redo(): boolean {
    if (this.historyIndex < this.operationHistory.length - 1) {
      this.historyIndex++;
      const operation = this.operationHistory[this.historyIndex];
      this.performBrushOperation(operation);
      this.eventEmitter.emit('operationRedone');
      return true;
    }
    return false;
  }

  /**
   * 更新笔刷预览
   */
  public updateBrushPreview(position: THREE.Vector3, size: number, visible: boolean): void {
    if (!this.brushPreviewMesh) return;

    this.brushPreviewMesh.position.copy(position);
    this.brushPreviewMesh.scale.setScalar(size);
    this.brushPreviewMesh.visible = visible;
  }

  /**
   * 添加纹理混合层
   */
  public addTextureBlendLayer(layer: TextureBlendLayer): void {
    this.textureBlendLayers.push(layer);
    this.eventEmitter.emit('textureLayerAdded', layer);
  }

  /**
   * 移除纹理混合层
   */
  public removeTextureBlendLayer(index: number): void {
    if (index >= 0 && index < this.textureBlendLayers.length) {
      const layer = this.textureBlendLayers.splice(index, 1)[0];
      this.eventEmitter.emit('textureLayerRemoved', layer);
    }
  }

  /**
   * 生成植被
   */
  public generateVegetation(config: VegetationConfig): void {
    if (!this.selectedTerrainEntity || !this.activeScene) return;

    const terrainComponent = this.selectedTerrainEntity.getComponent('TerrainComponent') as TerrainComponent;
    if (!terrainComponent) return;

    const instances = this.createVegetationInstances(terrainComponent, config);

    // 存储植被实例
    const key = `${config.type}_${Date.now()}`;
    this.vegetationInstances.set(key, instances);

    // 添加到场景
    instances.forEach(instance => {
      this.activeScene!.getThreeScene().add(instance);
    });

    this.eventEmitter.emit('vegetationGenerated', { config, instances });
  }

  /**
   * 创建植被实例
   */
  private createVegetationInstances(terrainComponent: TerrainComponent, config: VegetationConfig): THREE.InstancedMesh[] {
    const instances: THREE.InstancedMesh[] = [];
    const { heightData, resolution, width, height, maxHeight } = terrainComponent;

    // 计算植被位置
    const positions: THREE.Vector3[] = [];
    const random = new THREE.MathUtils.seededRandom(config.seed);

    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        if (random() < config.density) {
          const worldX = (x / resolution - 0.5) * width;
          const worldZ = (z / resolution - 0.5) * height;
          const heightValue = heightData[z * resolution + x];
          const worldY = heightValue * maxHeight;

          // 检查高度和坡度条件
          if (worldY >= config.minHeight && worldY <= config.maxHeight) {
            // 计算坡度
            const slope = this.calculateSlope(terrainComponent, x, z);
            if (slope >= config.minSlope && slope <= config.maxSlope) {
              positions.push(new THREE.Vector3(worldX, worldY, worldZ));
            }
          }
        }
      }
    }

    // 创建实例化网格
    if (positions.length > 0) {
      // 这里应该加载实际的植被模型
      // 目前使用简单的几何体作为占位符
      const geometry = this.createVegetationGeometry(config.type);
      const material = this.createVegetationMaterial(config.type);

      const instancedMesh = new THREE.InstancedMesh(geometry, material, positions.length);

      // 设置实例变换
      const matrix = new THREE.Matrix4();
      for (let i = 0; i < positions.length; i++) {
        const position = positions[i];
        const scale = THREE.MathUtils.lerp(config.scaleRange.min, config.scaleRange.max, random());
        const rotation = config.randomRotation ? random() * Math.PI * 2 : 0;

        matrix.makeRotationY(rotation);
        matrix.scale(new THREE.Vector3(scale, scale, scale));
        matrix.setPosition(position);

        instancedMesh.setMatrixAt(i, matrix);
      }

      instancedMesh.instanceMatrix.needsUpdate = true;
      instances.push(instancedMesh);
    }

    return instances;
  }

  /**
   * 计算坡度
   */
  private calculateSlope(terrainComponent: TerrainComponent, x: number, z: number): number {
    const { heightData, resolution } = terrainComponent;

    if (x <= 0 || x >= resolution - 1 || z <= 0 || z >= resolution - 1) {
      return 0;
    }

    const heightL = heightData[z * resolution + (x - 1)];
    const heightR = heightData[z * resolution + (x + 1)];
    const heightU = heightData[(z - 1) * resolution + x];
    const heightD = heightData[(z + 1) * resolution + x];

    const dx = heightR - heightL;
    const dz = heightD - heightU;

    return Math.sqrt(dx * dx + dz * dz);
  }

  /**
   * 创建植被几何体
   */
  private createVegetationGeometry(type: VegetationType): THREE.BufferGeometry {
    switch (type) {
      case VegetationType.GRASS:
        return new THREE.PlaneGeometry(0.5, 1);
      case VegetationType.TREES:
        return new THREE.ConeGeometry(0.5, 2, 8);
      case VegetationType.BUSHES:
        return new THREE.SphereGeometry(0.3, 8, 6);
      case VegetationType.FLOWERS:
        return new THREE.PlaneGeometry(0.2, 0.3);
      case VegetationType.ROCKS:
        return new THREE.DodecahedronGeometry(0.4);
      default:
        return new THREE.BoxGeometry(0.5, 0.5, 0.5);
    }
  }

  /**
   * 创建植被材质
   */
  private createVegetationMaterial(type: VegetationType): THREE.Material {
    switch (type) {
      case VegetationType.GRASS:
        return new THREE.MeshLambertMaterial({ color: 0x4a7c59 });
      case VegetationType.TREES:
        return new THREE.MeshLambertMaterial({ color: 0x2d5016 });
      case VegetationType.BUSHES:
        return new THREE.MeshLambertMaterial({ color: 0x3a5f3a });
      case VegetationType.FLOWERS:
        return new THREE.MeshLambertMaterial({ color: 0xff6b9d });
      case VegetationType.ROCKS:
        return new THREE.MeshLambertMaterial({ color: 0x8b7355 });
      default:
        return new THREE.MeshLambertMaterial({ color: 0x888888 });
    }
  }

  /**
   * 清除植被
   */
  public clearVegetation(key?: string): void {
    if (key) {
      const instances = this.vegetationInstances.get(key);
      if (instances) {
        instances.forEach(instance => {
          if (this.activeScene) {
            this.activeScene.getThreeScene().remove(instance);
          }
          instance.dispose();
        });
        this.vegetationInstances.delete(key);
      }
    } else {
      // 清除所有植被
      this.vegetationInstances.forEach((instances, key) => {
        instances.forEach(instance => {
          if (this.activeScene) {
            this.activeScene.getThreeScene().remove(instance);
          }
          instance.dispose();
        });
      });
      this.vegetationInstances.clear();
    }

    this.eventEmitter.emit('vegetationCleared', key);
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): any {
    return {
      selectedTerrain: this.selectedTerrainEntity ? this.selectedTerrainEntity.id : null,
      operationHistoryLength: this.operationHistory.length,
      historyIndex: this.historyIndex,
      textureLayerCount: this.textureBlendLayers.length,
      vegetationInstanceCount: Array.from(this.vegetationInstances.values()).reduce(
        (total, instances) => total + instances.length, 0
      )
    };
  }

  /**
   * 监听事件
   */
  public on(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   */
  public off(event: string, callback: Function): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清除植被
    this.clearVegetation();

    // 清理笔刷预览
    if (this.brushPreviewMesh) {
      if (this.activeScene) {
        this.activeScene.getThreeScene().remove(this.brushPreviewMesh);
      }
      this.brushPreviewMesh.geometry.dispose();
      (this.brushPreviewMesh.material as THREE.Material).dispose();
      this.brushPreviewMesh = null;
    }

    // 清理纹理层
    this.textureBlendLayers.length = 0;

    // 清理历史
    this.operationHistory.length = 0;

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    Debug.log('TerrainEditorSystem', '地形编辑器系统已销毁');
  }
}
