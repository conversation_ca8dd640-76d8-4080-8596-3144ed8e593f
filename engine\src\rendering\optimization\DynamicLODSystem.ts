/**
 * 自适应LOD系统
 * 根据性能、视距、屏幕大小和复杂度动态调整LOD级别
 * 实现平滑过渡和智能切换策略
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { LODComponent, LODLevel } from './LODComponent';
import { EnhancedLODSystem, EnhancedLODSystemOptions } from './EnhancedLODSystem';
import { PerformanceMonitor, PerformanceMetricType } from '../../utils/PerformanceMonitor';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 自适应LOD系统事件类型
 */
export enum AdaptiveLODSystemEventType {
  /** LOD级别调整 */
  LOD_LEVEL_ADJUSTED = 'lod_level_adjusted',
  /** 性能目标调整 */
  PERFORMANCE_TARGET_ADJUSTED = 'performance_target_adjusted',
  /** 质量级别调整 */
  QUALITY_LEVEL_ADJUSTED = 'quality_level_adjusted',
  /** 自适应策略变更 */
  ADAPTIVE_STRATEGY_CHANGED = 'adaptive_strategy_changed',
  /** 平滑过渡开始 */
  SMOOTH_TRANSITION_START = 'smooth_transition_start',
  /** 平滑过渡完成 */
  SMOOTH_TRANSITION_COMPLETE = 'smooth_transition_complete',
  /** 性能阈值触发 */
  PERFORMANCE_THRESHOLD_TRIGGERED = 'performance_threshold_triggered'
}

/**
 * 自适应策略类型
 */
export enum AdaptiveStrategy {
  /** 性能优先 */
  PERFORMANCE_FIRST = 'performance_first',
  /** 质量优先 */
  QUALITY_FIRST = 'quality_first',
  /** 平衡模式 */
  BALANCED = 'balanced',
  /** 自动模式 */
  AUTO = 'auto'
}

/**
 * LOD过渡状态
 */
export enum LODTransitionState {
  /** 空闲 */
  IDLE = 'idle',
  /** 过渡中 */
  TRANSITIONING = 'transitioning',
  /** 完成 */
  COMPLETED = 'completed'
}

/**
 * 自适应LOD系统配置接口
 */
export interface AdaptiveLODSystemOptions extends EnhancedLODSystemOptions {
  /** 是否启用自适应LOD */
  useAdaptiveLOD?: boolean;
  /** 目标帧率 */
  targetFPS?: number;
  /** 最小帧率 */
  minFPS?: number;
  /** 最大帧率 */
  maxFPS?: number;
  /** 调整灵敏度 */
  adjustmentSensitivity?: number;
  /** 调整间隔（毫秒） */
  adjustmentInterval?: number;
  /** 是否使用平滑过渡 */
  useSmoothTransition?: boolean;
  /** 过渡时间（毫秒） */
  transitionTime?: number;
  /** 质量级别 (0-1) */
  qualityLevel?: number;
  /** 是否使用自动质量调整 */
  useAutoQualityAdjustment?: boolean;
  /** 是否使用距离偏移 */
  useDistanceOffset?: boolean;
  /** 距离偏移系数 */
  distanceOffsetFactor?: number;
  /** 自适应策略 */
  adaptiveStrategy?: AdaptiveStrategy;
  /** 是否启用屏幕大小检测 */
  useScreenSizeDetection?: boolean;
  /** 是否启用复杂度分析 */
  useComplexityAnalysis?: boolean;
  /** 性能阈值 */
  performanceThresholds?: {
    low: number;
    medium: number;
    high: number;
  };
  /** 是否启用预测性LOD */
  usePredictiveLOD?: boolean;
  /** 预测时间窗口（毫秒） */
  predictionWindow?: number;
  /** 是否启用批量更新 */
  useBatchUpdate?: boolean;
  /** 批量更新大小 */
  batchSize?: number;
}

/**
 * 自适应LOD系统
 * 根据性能、视距、屏幕大小和复杂度动态调整LOD级别
 * 实现平滑过渡和智能切换策略
 */
export class AdaptiveLODSystem extends EnhancedLODSystem {

  /** 是否启用自适应LOD */
  private useAdaptiveLOD: boolean;
  /** 目标帧率 */
  private targetFPS: number;
  /** 最小帧率 */
  private minFPS: number;
  /** 最大帧率 */
  private maxFPS: number;
  /** 调整灵敏度 */
  private adjustmentSensitivity: number;
  /** 调整间隔（毫秒） */
  private adjustmentInterval: number;
  /** 上次调整时间 */
  private lastAdjustmentTime: number;
  /** 是否使用平滑过渡 */
  private useSmoothTransition: boolean;
  /** 过渡时间（毫秒） */
  private transitionTime: number;
  /** 质量级别 (0-1) */
  private qualityLevel: number;
  /** 是否使用自动质量调整 */
  private useAutoQualityAdjustment: boolean;
  /** 是否使用距离偏移 */
  private useDistanceOffset: boolean;
  /** 距离偏移系数 */
  private distanceOffsetFactor: number;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor;
  /** 当前距离偏移 */
  private currentDistanceOffset: number;
  /** 自适应LOD事件发射器 */
  private adaptiveLODEventEmitter: EventEmitter;
  /** 自适应策略 */
  private adaptiveStrategy: AdaptiveStrategy;
  /** 是否启用屏幕大小检测 */
  private useScreenSizeDetection: boolean;
  /** 是否启用复杂度分析 */
  private useComplexityAnalysis: boolean;
  /** 性能阈值 */
  private performanceThresholds: { low: number; medium: number; high: number };
  /** 是否启用预测性LOD */
  private usePredictiveLOD: boolean;
  /** 预测时间窗口（毫秒） */
  private predictionWindow: number;
  /** 是否启用批量更新 */
  private useBatchUpdate: boolean;
  /** 批量更新大小 */
  private batchSize: number;
  /** LOD过渡状态映射 */
  private transitionStates: Map<Entity, LODTransitionState>;
  /** 过渡开始时间映射 */
  private transitionStartTimes: Map<Entity, number>;
  /** 性能历史记录 */
  private performanceHistory: number[];
  /** 复杂度缓存 */
  private complexityCache: Map<Entity, number>;
  /** 屏幕大小缓存 */
  private screenSizeCache: Map<Entity, number>;
  /** 批量更新队列 */
  private batchUpdateQueue: Entity[];

  /**
   * 创建自适应LOD系统
   * @param options 系统选项
   */
  constructor(options: AdaptiveLODSystemOptions = {}) {
    super(options);

    // 设置自适应LOD选项
    this.useAdaptiveLOD = options.useAdaptiveLOD !== undefined ? options.useAdaptiveLOD : true;
    this.targetFPS = options.targetFPS || 60;
    this.minFPS = options.minFPS || 30;
    this.maxFPS = options.maxFPS || 120;
    this.adjustmentSensitivity = options.adjustmentSensitivity || 0.1;
    this.adjustmentInterval = options.adjustmentInterval || 1000;
    this.lastAdjustmentTime = 0;
    this.useSmoothTransition = options.useSmoothTransition !== undefined ? options.useSmoothTransition : true;
    this.transitionTime = options.transitionTime || 500;
    this.qualityLevel = options.qualityLevel !== undefined ? options.qualityLevel : 0.5;
    this.useAutoQualityAdjustment = options.useAutoQualityAdjustment !== undefined ? options.useAutoQualityAdjustment : true;
    this.useDistanceOffset = options.useDistanceOffset !== undefined ? options.useDistanceOffset : true;
    this.distanceOffsetFactor = options.distanceOffsetFactor || 1.0;
    this.currentDistanceOffset = 0;

    // 新增自适应功能选项
    this.adaptiveStrategy = options.adaptiveStrategy || AdaptiveStrategy.BALANCED;
    this.useScreenSizeDetection = options.useScreenSizeDetection !== undefined ? options.useScreenSizeDetection : true;
    this.useComplexityAnalysis = options.useComplexityAnalysis !== undefined ? options.useComplexityAnalysis : true;
    this.performanceThresholds = options.performanceThresholds || { low: 30, medium: 45, high: 60 };
    this.usePredictiveLOD = options.usePredictiveLOD !== undefined ? options.usePredictiveLOD : true;
    this.predictionWindow = options.predictionWindow || 2000;
    this.useBatchUpdate = options.useBatchUpdate !== undefined ? options.useBatchUpdate : true;
    this.batchSize = options.batchSize || 50;

    // 初始化状态映射和缓存
    this.transitionStates = new Map();
    this.transitionStartTimes = new Map();
    this.performanceHistory = [];
    this.complexityCache = new Map();
    this.screenSizeCache = new Map();
    this.batchUpdateQueue = [];

    // 获取性能监控器实例
    this.performanceMonitor = PerformanceMonitor.getInstance();

    // 创建自适应LOD事件发射器
    this.adaptiveLODEventEmitter = new EventEmitter();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return 'AdaptiveLODSystem';
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled()) {
      return;
    }

    // 调用父类更新方法
    super.update(deltaTime);

    // 如果启用了自适应LOD，则更新自适应LOD
    if (this.useAdaptiveLOD) {
      this.updateAdaptiveLOD(deltaTime);
    }

    // 更新过渡状态
    if (this.useSmoothTransition) {
      this.updateTransitionStates(deltaTime);
    }

    // 处理批量更新
    if (this.useBatchUpdate && this.batchUpdateQueue.length > 0) {
      this.processBatchUpdate();
    }
  }

  /**
   * 更新自适应LOD
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAdaptiveLOD(deltaTime: number): void {
    const currentTime = performance.now();

    // 检查是否需要调整
    if (currentTime - this.lastAdjustmentTime < this.adjustmentInterval) {
      return;
    }

    // 获取当前性能指标
    const performanceMetrics = this.gatherPerformanceMetrics();

    // 更新性能历史记录
    this.updatePerformanceHistory(performanceMetrics.fps);

    // 根据自适应策略调整LOD
    this.applyAdaptiveStrategy(performanceMetrics);

    // 如果启用了预测性LOD，则进行性能预测
    if (this.usePredictiveLOD) {
      this.predictiveAdjustment();
    }

    // 更新上次调整时间
    this.lastAdjustmentTime = currentTime;
  }

  /**
   * 收集性能指标
   * @returns 性能指标
   */
  private gatherPerformanceMetrics(): {
    fps: number;
    frameTime: number;
    memoryUsage: number;
    drawCalls: number;
    triangles: number;
  } {
    const fpsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.FPS);
    const frameTimeMetric = this.performanceMonitor.getMetric(PerformanceMetricType.FRAME_TIME);
    const memoryMetric = this.performanceMonitor.getMetric(PerformanceMetricType.MEMORY_USAGE);
    const drawCallsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.DRAW_CALLS);
    const trianglesMetric = this.performanceMonitor.getMetric(PerformanceMetricType.TRIANGLES);

    return {
      fps: fpsMetric ? fpsMetric.value : 60,
      frameTime: frameTimeMetric ? frameTimeMetric.value : 16.67,
      memoryUsage: memoryMetric ? memoryMetric.value : 0,
      drawCalls: drawCallsMetric ? drawCallsMetric.value : 0,
      triangles: trianglesMetric ? trianglesMetric.value : 0
    };
  }

  /**
   * 应用自适应策略
   * @param metrics 性能指标
   */
  private applyAdaptiveStrategy(metrics: {
    fps: number;
    frameTime: number;
    memoryUsage: number;
    drawCalls: number;
    triangles: number;
  }): void {
    switch (this.adaptiveStrategy) {
      case AdaptiveStrategy.PERFORMANCE_FIRST:
        this.applyPerformanceFirstStrategy(metrics);
        break;
      case AdaptiveStrategy.QUALITY_FIRST:
        this.applyQualityFirstStrategy(metrics);
        break;
      case AdaptiveStrategy.BALANCED:
        this.applyBalancedStrategy(metrics);
        break;
      case AdaptiveStrategy.AUTO:
        this.applyAutoStrategy(metrics);
        break;
    }
  }

  /**
   * 应用性能优先策略
   * @param metrics 性能指标
   */
  private applyPerformanceFirstStrategy(metrics: any): void {
    if (metrics.fps < this.performanceThresholds.low) {
      // 激进降低质量
      this.adjustQualityLevel(metrics.fps, 0.2);
      this.adaptiveLODEventEmitter.emit(AdaptiveLODSystemEventType.PERFORMANCE_THRESHOLD_TRIGGERED, 'low', metrics.fps);
    } else if (metrics.fps < this.performanceThresholds.medium) {
      // 适度降低质量
      this.adjustQualityLevel(metrics.fps, 0.1);
    } else if (metrics.fps > this.performanceThresholds.high) {
      // 谨慎提升质量
      this.adjustQualityLevel(metrics.fps, 0.05);
    }
  }

  /**
   * 应用质量优先策略
   * @param metrics 性能指标
   */
  private applyQualityFirstStrategy(metrics: any): void {
    if (metrics.fps < this.performanceThresholds.low) {
      // 适度降低质量
      this.adjustQualityLevel(metrics.fps, 0.1);
    } else if (metrics.fps > this.performanceThresholds.medium) {
      // 积极提升质量
      this.adjustQualityLevel(metrics.fps, 0.15);
    }
  }

  /**
   * 应用平衡策略
   * @param metrics 性能指标
   */
  private applyBalancedStrategy(metrics: any): void {
    const targetFPS = this.targetFPS;
    const fpsDiff = metrics.fps - targetFPS;
    const adjustmentFactor = Math.abs(fpsDiff) / targetFPS;

    if (metrics.fps < this.performanceThresholds.low) {
      this.adjustQualityLevel(metrics.fps, 0.15 * adjustmentFactor);
    } else if (metrics.fps > this.performanceThresholds.high) {
      this.adjustQualityLevel(metrics.fps, 0.1 * adjustmentFactor);
    }
  }

  /**
   * 应用自动策略
   * @param metrics 性能指标
   */
  private applyAutoStrategy(metrics: any): void {
    // 根据性能历史动态选择策略
    const avgFPS = this.calculateAveragePerformance();

    if (avgFPS < this.performanceThresholds.medium) {
      this.applyPerformanceFirstStrategy(metrics);
    } else if (avgFPS > this.performanceThresholds.high) {
      this.applyQualityFirstStrategy(metrics);
    } else {
      this.applyBalancedStrategy(metrics);
    }
  }

  /**
   * 调整质量级别
   * @param currentFPS 当前FPS
   * @param sensitivity 调整敏感度
   */
  private adjustQualityLevel(currentFPS: number, sensitivity: number = this.adjustmentSensitivity): void {
    // 计算FPS差异
    const fpsDiff = currentFPS - this.targetFPS;

    // 计算调整量
    const adjustment = fpsDiff * sensitivity / (this.maxFPS - this.minFPS);

    // 调整质量级别
    const oldQualityLevel = this.qualityLevel;
    this.qualityLevel = Math.max(0, Math.min(1, this.qualityLevel + adjustment));

    // 如果质量级别发生变化，则发出事件
    if (Math.abs(this.qualityLevel - oldQualityLevel) > 0.01) {
      this.adaptiveLODEventEmitter.emit(AdaptiveLODSystemEventType.QUALITY_LEVEL_ADJUSTED, this.qualityLevel, oldQualityLevel);
      Debug.log('AdaptiveLODSystem', `质量级别调整: ${oldQualityLevel.toFixed(2)} -> ${this.qualityLevel.toFixed(2)}`);

      // 调整距离偏移
      if (this.useDistanceOffset) {
        this.adjustDistanceOffset();
      }
    }
  }

  /**
   * 更新性能历史记录
   * @param fps 当前FPS
   */
  private updatePerformanceHistory(fps: number): void {
    this.performanceHistory.push(fps);

    // 保持历史记录在合理范围内
    const maxHistorySize = Math.ceil(this.predictionWindow / this.adjustmentInterval);
    if (this.performanceHistory.length > maxHistorySize) {
      this.performanceHistory.shift();
    }
  }

  /**
   * 计算平均性能
   * @returns 平均FPS
   */
  private calculateAveragePerformance(): number {
    if (this.performanceHistory.length === 0) {
      return this.targetFPS;
    }

    const sum = this.performanceHistory.reduce((acc, fps) => acc + fps, 0);
    return sum / this.performanceHistory.length;
  }

  /**
   * 预测性调整
   */
  private predictiveAdjustment(): void {
    if (this.performanceHistory.length < 3) {
      return;
    }

    // 计算性能趋势
    const recentPerformance = this.performanceHistory.slice(-3);
    const trend = this.calculatePerformanceTrend(recentPerformance);

    // 根据趋势预测性调整
    if (trend < -0.1) {
      // 性能下降趋势，预防性降低质量
      this.qualityLevel = Math.max(0, this.qualityLevel - 0.05);
      Debug.log('AdaptiveLODSystem', '检测到性能下降趋势，预防性降低质量');
    } else if (trend > 0.1 && this.qualityLevel < 0.9) {
      // 性能提升趋势，可以适当提升质量
      this.qualityLevel = Math.min(1, this.qualityLevel + 0.03);
      Debug.log('AdaptiveLODSystem', '检测到性能提升趋势，适当提升质量');
    }
  }

  /**
   * 计算性能趋势
   * @param performanceData 性能数据
   * @returns 趋势值
   */
  private calculatePerformanceTrend(performanceData: number[]): number {
    if (performanceData.length < 2) {
      return 0;
    }

    let trend = 0;
    for (let i = 1; i < performanceData.length; i++) {
      trend += (performanceData[i] - performanceData[i - 1]) / performanceData[i - 1];
    }

    return trend / (performanceData.length - 1);
  }

  /**
   * 调整距离偏移
   */
  private adjustDistanceOffset(): void {
    // 根据质量级别计算距离偏移
    // 质量级别越低，距离偏移越大，LOD切换越早
    const oldDistanceOffset = this.currentDistanceOffset;
    this.currentDistanceOffset = (1 - this.qualityLevel) * this.distanceOffsetFactor;

    // 如果距离偏移发生变化，则更新所有LOD组件
    if (Math.abs(this.currentDistanceOffset - oldDistanceOffset) > 0.01) {
      if (this.useBatchUpdate) {
        this.queueBatchUpdate();
      } else {
        this.updateLODDistances();
      }
    }
  }

  /**
   * 更新过渡状态
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateTransitionStates(deltaTime: number): void {
    const currentTime = performance.now();

    for (const [entity, state] of this.transitionStates.entries()) {
      if (state === LODTransitionState.TRANSITIONING) {
        const startTime = this.transitionStartTimes.get(entity);
        if (startTime && currentTime - startTime >= this.transitionTime) {
          // 过渡完成
          this.transitionStates.set(entity, LODTransitionState.COMPLETED);
          this.adaptiveLODEventEmitter.emit(AdaptiveLODSystemEventType.SMOOTH_TRANSITION_COMPLETE, entity);
        }
      }
    }
  }

  /**
   * 开始平滑过渡
   * @param entity 实体
   */
  private startSmoothTransition(entity: Entity): void {
    this.transitionStates.set(entity, LODTransitionState.TRANSITIONING);
    this.transitionStartTimes.set(entity, performance.now());
    this.adaptiveLODEventEmitter.emit(AdaptiveLODSystemEventType.SMOOTH_TRANSITION_START, entity);
  }

  /**
   * 队列批量更新
   */
  private queueBatchUpdate(): void {
    // 将所有LOD组件的实体添加到批量更新队列
    for (const [entity] of this.lodComponents.entries()) {
      if (!this.batchUpdateQueue.includes(entity)) {
        this.batchUpdateQueue.push(entity);
      }
    }
  }

  /**
   * 处理批量更新
   */
  private processBatchUpdate(): void {
    const batchSize = Math.min(this.batchSize, this.batchUpdateQueue.length);
    const batch = this.batchUpdateQueue.splice(0, batchSize);

    for (const entity of batch) {
      this.updateEntityLODDistances(entity);
    }
  }

  /**
   * 更新实体LOD距离
   * @param entity 实体
   */
  private updateEntityLODDistances(entity: Entity): void {
    const lodComponent = this.lodComponents.get(entity);
    if (!lodComponent) {
      return;
    }

    // 获取所有级别
    const levels = lodComponent.getLevels();

    // 更新每个级别的距离
    for (const level of levels) {
      // 获取原始距离
      const originalDistance = level.originalDistance !== undefined ? level.originalDistance : level.distance;

      // 如果没有保存原始距离，则保存
      if (level.originalDistance === undefined) {
        level.originalDistance = originalDistance;
      }

      // 计算新距离
      const newDistance = originalDistance * (1 - this.currentDistanceOffset);

      // 设置新距离
      level.distance = newDistance;
    }

    // 如果启用平滑过渡，开始过渡
    if (this.useSmoothTransition) {
      this.startSmoothTransition(entity);
    }
  }

  /**
   * 更新LOD距离
   */
  private updateLODDistances(): void {
    // 遍历所有LOD组件
    for (const [entity] of Array.from(this.lodComponents.entries())) {
      this.updateEntityLODDistances(entity);
    }
  }

  /**
   * 设置目标帧率
   * @param fps 目标帧率
   */
  public setTargetFPS(fps: number): void {
    const oldTargetFPS = this.targetFPS;
    this.targetFPS = Math.max(this.minFPS, Math.min(this.maxFPS, fps));

    // 发出事件
    this.adaptiveLODEventEmitter.emit(AdaptiveLODSystemEventType.PERFORMANCE_TARGET_ADJUSTED, this.targetFPS, oldTargetFPS);
    Debug.log('AdaptiveLODSystem', `目标帧率调整: ${oldTargetFPS} -> ${this.targetFPS}`);
  }

  /**
   * 获取目标帧率
   * @returns 目标帧率
   */
  public getTargetFPS(): number {
    return this.targetFPS;
  }

  /**
   * 设置质量级别
   * @param level 质量级别 (0-1)
   */
  public setQualityLevel(level: number): void {
    const oldQualityLevel = this.qualityLevel;
    this.qualityLevel = Math.max(0, Math.min(1, level));

    // 发出事件
    this.adaptiveLODEventEmitter.emit(AdaptiveLODSystemEventType.QUALITY_LEVEL_ADJUSTED, this.qualityLevel, oldQualityLevel);
    Debug.log('AdaptiveLODSystem', `质量级别设置: ${oldQualityLevel.toFixed(2)} -> ${this.qualityLevel.toFixed(2)}`);

    // 调整距离偏移
    if (this.useDistanceOffset) {
      this.adjustDistanceOffset();
    }
  }

  /**
   * 获取质量级别
   * @returns 质量级别 (0-1)
   */
  public getQualityLevel(): number {
    return this.qualityLevel;
  }

  /**
   * 设置自适应策略
   * @param strategy 自适应策略
   */
  public setAdaptiveStrategy(strategy: AdaptiveStrategy): void {
    const oldStrategy = this.adaptiveStrategy;
    this.adaptiveStrategy = strategy;

    // 发出事件
    this.adaptiveLODEventEmitter.emit(AdaptiveLODSystemEventType.ADAPTIVE_STRATEGY_CHANGED, strategy, oldStrategy);
    Debug.log('AdaptiveLODSystem', `自适应策略变更: ${oldStrategy} -> ${strategy}`);
  }

  /**
   * 获取自适应策略
   * @returns 自适应策略
   */
  public getAdaptiveStrategy(): AdaptiveStrategy {
    return this.adaptiveStrategy;
  }

  /**
   * 启用/禁用自适应LOD
   * @param enabled 是否启用
   */
  public setAdaptiveLODEnabled(enabled: boolean): void {
    this.useAdaptiveLOD = enabled;
  }

  /**
   * 获取自适应LOD是否启用
   * @returns 是否启用
   */
  public isAdaptiveLODEnabled(): boolean {
    return this.useAdaptiveLOD;
  }

  /**
   * 获取性能统计信息
   * @returns 性能统计
   */
  public getPerformanceStats(): {
    averageFPS: number;
    currentQuality: number;
    transitioningEntities: number;
    batchQueueSize: number;
    performanceTrend: number;
  } {
    return {
      averageFPS: this.calculateAveragePerformance(),
      currentQuality: this.qualityLevel,
      transitioningEntities: Array.from(this.transitionStates.values()).filter(state => state === LODTransitionState.TRANSITIONING).length,
      batchQueueSize: this.batchUpdateQueue.length,
      performanceTrend: this.performanceHistory.length >= 3 ? this.calculatePerformanceTrend(this.performanceHistory.slice(-3)) : 0
    };
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: (...args: any[]) => void): void {
    this.adaptiveLODEventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener: (...args: any[]) => void): void {
    this.adaptiveLODEventEmitter.off(type, listener);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    super.dispose();

    // 清理状态映射和缓存
    this.transitionStates.clear();
    this.transitionStartTimes.clear();
    this.performanceHistory.length = 0;
    this.complexityCache.clear();
    this.screenSizeCache.clear();
    this.batchUpdateQueue.length = 0;

    // 移除所有事件监听器
    this.adaptiveLODEventEmitter.removeAllListeners();
  }
}
