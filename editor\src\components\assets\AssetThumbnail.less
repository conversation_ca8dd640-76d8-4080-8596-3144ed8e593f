/**
 * 资产缩略图样式
 */
.asset-thumbnail {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.loading {
    .thumbnail-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #fafafa;
    }
  }

  &.has-error {
    border-color: #ff7875;
    background: #fff2f0;

    .thumbnail-fallback {
      color: #ff4d4f;
    }
  }

  .thumbnail-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #fafafa;
  }

  .thumbnail-fallback,
  .thumbnail-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #f0f0f0;
    color: #999;

    .fallback-icon,
    .placeholder-icon {
      opacity: 0.6;
      transition: opacity 0.3s;
    }
  }

  &:hover {
    .fallback-icon,
    .placeholder-icon {
      opacity: 0.8;
    }
  }

  .ant-image {
    width: 100%;
    height: 100%;

    .ant-image-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
  }

  &:hover .ant-image-img {
    transform: scale(1.05);
  }

  .asset-type-badge {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    padding: 2px 4px;
    opacity: 0;
    transition: opacity 0.3s ease;

    .type-icon {
      color: #fff;
      font-size: 12px;
    }
  }

  &:hover .asset-type-badge {
    opacity: 1;
  }

  // 不同尺寸的适配
  &.size-small {
    .asset-type-badge {
      top: 2px;
      right: 2px;
      padding: 1px 2px;

      .type-icon {
        font-size: 10px;
      }
    }
  }

  &.size-large {
    .asset-type-badge {
      top: 8px;
      right: 8px;
      padding: 4px 6px;

      .type-icon {
        font-size: 14px;
      }
    }
  }

  // 选中状态
  &.selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

    .asset-type-badge {
      background: rgba(24, 144, 255, 0.8);
      opacity: 1;
    }
  }

  // 深色主题
  &.dark-theme {
    background: #2a2a2a;
    border-color: #434343;

    &:hover {
      border-color: #40a9ff;
    }

    &.has-error {
      border-color: #ff7875;
      background: #2a1f1f;
    }

    .thumbnail-loading {
      background: #1f1f1f;
    }

    .thumbnail-fallback,
    .thumbnail-placeholder {
      background: #1f1f1f;
      color: #666;
    }

    .asset-type-badge {
      background: rgba(255, 255, 255, 0.1);

      .type-icon {
        color: #fff;
      }
    }

    &.selected {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);

      .asset-type-badge {
        background: rgba(24, 144, 255, 0.8);
      }
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    border: 2px solid #000;

    &:hover {
      border-color: #0066cc;
    }

    &.selected {
      border-color: #0066cc;
      box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
    }

    .asset-type-badge {
      background: #000;
      border: 1px solid #fff;

      .type-icon {
        color: #fff;
      }
    }
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    transition: none;

    .ant-image-img {
      transition: none;
    }

    &:hover .ant-image-img {
      transform: none;
    }

    .fallback-icon,
    .placeholder-icon,
    .asset-type-badge {
      transition: none;
    }
  }

  // 无障碍支持
  &:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }

  &[aria-selected="true"] {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}
