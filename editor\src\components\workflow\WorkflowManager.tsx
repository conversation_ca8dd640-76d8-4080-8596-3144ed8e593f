/**
 * 工作流管理组件
 * 提供操作历史、工作流模式和智能建议的管理界面
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card, Tabs, Table, Timeline, Button, Space, Tag, Tooltip, 
  Statistic, Row, Col, Alert, List, Avatar, Switch, Modal,
  Progress, Empty, Divider
} from 'antd';
import {
  HistoryOutlined, BulbOutlined, BarChartOutlined, SettingOutlined,
  UndoOutlined, RedoOutlined, CheckOutlined, CloseOutlined,
  ExportOutlined, ImportOutlined, ClearOutlined, PlayCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { 
  WorkflowService, 
  OperationRecord, 
  WorkflowPattern, 
  SmartSuggestion, 
  WorkflowStats,
  OperationType 
} from '../../services/WorkflowService';
import './WorkflowManager.less';

const { TabPane } = Tabs;

/**
 * 组件属性接口
 */
interface WorkflowManagerProps {
  /** 是否可见 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
}

/**
 * 工作流管理组件
 */
export const WorkflowManager: React.FC<WorkflowManagerProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const workflowService = useMemo(() => WorkflowService.getInstance(), []);

  // 状态
  const [operationHistory, setOperationHistory] = useState<OperationRecord[]>([]);
  const [workflowPatterns, setWorkflowPatterns] = useState<WorkflowPattern[]>([]);
  const [smartSuggestions, setSmartSuggestions] = useState<SmartSuggestion[]>([]);
  const [workflowStats, setWorkflowStats] = useState<WorkflowStats | null>(null);
  const [activeTab, setActiveTab] = useState('history');

  /**
   * 加载数据
   */
  const loadData = useCallback(() => {
    setOperationHistory(workflowService.getOperationHistory(100));
    setWorkflowPatterns(workflowService.getWorkflowPatterns());
    setSmartSuggestions(workflowService.getSmartSuggestions());
    setWorkflowStats(workflowService.getWorkflowStats());
  }, [workflowService]);

  /**
   * 处理撤销
   */
  const handleUndo = useCallback(() => {
    const success = workflowService.undo();
    if (success) {
      loadData();
    }
  }, [workflowService, loadData]);

  /**
   * 处理重做
   */
  const handleRedo = useCallback(() => {
    const success = workflowService.redo();
    if (success) {
      loadData();
    }
  }, [workflowService, loadData]);

  /**
   * 处理接受建议
   */
  const handleAcceptSuggestion = useCallback((suggestionId: string) => {
    workflowService.acceptSuggestion(suggestionId);
    loadData();
  }, [workflowService, loadData]);

  /**
   * 处理拒绝建议
   */
  const handleRejectSuggestion = useCallback((suggestionId: string) => {
    workflowService.rejectSuggestion(suggestionId);
    loadData();
  }, [workflowService, loadData]);

  /**
   * 清除历史记录
   */
  const handleClearHistory = useCallback(() => {
    Modal.confirm({
      title: t('workflow.clearHistoryConfirm'),
      content: t('workflow.clearHistoryDesc'),
      onOk: () => {
        workflowService.clearHistory();
        loadData();
      }
    });
  }, [workflowService, loadData, t]);

  /**
   * 导出工作流数据
   */
  const handleExportData = useCallback(() => {
    const data = workflowService.exportWorkflowData();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'workflow-data.json';
    a.click();
    URL.revokeObjectURL(url);
  }, [workflowService]);

  /**
   * 导入工作流数据
   */
  const handleImportData = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target?.result as string);
            workflowService.importWorkflowData(data);
            loadData();
          } catch (error) {
            console.error('Import failed:', error);
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [workflowService, loadData]);

  /**
   * 渲染操作历史
   */
  const renderOperationHistory = () => {
    const columns = [
      {
        title: t('workflow.operation'),
        dataIndex: 'description',
        key: 'description',
        render: (text: string, record: OperationRecord) => (
          <div>
            <div>{text}</div>
            <div style={{ fontSize: 12, color: '#999' }}>
              {record.target}
            </div>
          </div>
        )
      },
      {
        title: t('workflow.type'),
        dataIndex: 'type',
        key: 'type',
        width: 100,
        render: (type: OperationType) => (
          <Tag color="blue">{type}</Tag>
        )
      },
      {
        title: t('workflow.time'),
        dataIndex: 'timestamp',
        key: 'timestamp',
        width: 150,
        render: (timestamp: number) => (
          new Date(timestamp).toLocaleString()
        )
      },
      {
        title: t('workflow.duration'),
        dataIndex: 'duration',
        key: 'duration',
        width: 100,
        render: (duration: number) => (
          `${duration}ms`
        )
      },
      {
        title: t('workflow.result'),
        dataIndex: 'result',
        key: 'result',
        width: 80,
        render: (result: string) => (
          <Tag color={result === 'success' ? 'green' : result === 'error' ? 'red' : 'orange'}>
            {result}
          </Tag>
        )
      }
    ];

    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button icon={<UndoOutlined />} onClick={handleUndo}>
              {t('workflow.undo')}
            </Button>
            <Button icon={<RedoOutlined />} onClick={handleRedo}>
              {t('workflow.redo')}
            </Button>
            <Divider type="vertical" />
            <Button icon={<ClearOutlined />} onClick={handleClearHistory}>
              {t('workflow.clearHistory')}
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExportData}>
              {t('workflow.export')}
            </Button>
            <Button icon={<ImportOutlined />} onClick={handleImportData}>
              {t('workflow.import')}
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={operationHistory}
          rowKey="id"
          size="small"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true
          }}
        />
      </div>
    );
  };

  /**
   * 渲染工作流模式
   */
  const renderWorkflowPatterns = () => {
    if (workflowPatterns.length === 0) {
      return (
        <Empty
          description={t('workflow.noPatternsDetected')}
          style={{ margin: '50px 0' }}
        />
      );
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={workflowPatterns}
        renderItem={(pattern) => (
          <List.Item
            actions={[
              <Button
                type="primary"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => {
                  // 执行工作流模式
                }}
              >
                {t('workflow.execute')}
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar icon={<HistoryOutlined />} />}
              title={pattern.name}
              description={
                <div>
                  <div>{pattern.description}</div>
                  <div style={{ marginTop: 8 }}>
                    <Space>
                      {pattern.operations.map((op, index) => (
                        <Tag key={index} size="small">{op}</Tag>
                      ))}
                    </Space>
                  </div>
                  <div style={{ marginTop: 8, fontSize: 12, color: '#999' }}>
                    使用频率: {pattern.frequency} 次 | 
                    最后使用: {new Date(pattern.lastUsed).toLocaleString()}
                  </div>
                </div>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  /**
   * 渲染智能建议
   */
  const renderSmartSuggestions = () => {
    if (smartSuggestions.length === 0) {
      return (
        <Empty
          description={t('workflow.noSuggestions')}
          style={{ margin: '50px 0' }}
        />
      );
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={smartSuggestions}
        renderItem={(suggestion) => (
          <List.Item
            actions={[
              <Button
                type="primary"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleAcceptSuggestion(suggestion.id)}
              >
                {t('workflow.accept')}
              </Button>,
              <Button
                size="small"
                icon={<CloseOutlined />}
                onClick={() => handleRejectSuggestion(suggestion.id)}
              >
                {t('workflow.reject')}
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar icon={<BulbOutlined />} />}
              title={
                <div>
                  {suggestion.title}
                  <Tag color="orange" style={{ marginLeft: 8 }}>
                    {suggestion.type}
                  </Tag>
                </div>
              }
              description={suggestion.description}
            />
          </List.Item>
        )}
      />
    );
  };

  /**
   * 渲染统计信息
   */
  const renderStats = () => {
    if (!workflowStats) return null;

    return (
      <div>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title={t('workflow.totalOperations')}
              value={workflowStats.totalOperations}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('workflow.successRate')}
              value={workflowStats.totalOperations > 0 ? 
                (workflowStats.successfulOperations / workflowStats.totalOperations * 100).toFixed(1) : 0}
              suffix="%"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('workflow.avgOperationTime')}
              value={workflowStats.averageOperationTime.toFixed(0)}
              suffix="ms"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('workflow.suggestionAcceptanceRate')}
              value={(workflowStats.suggestionAcceptanceRate * 100).toFixed(1)}
              suffix="%"
            />
          </Col>
        </Row>

        <Divider />

        <h4>{t('workflow.mostUsedOperations')}</h4>
        <List
          size="small"
          dataSource={workflowStats.mostUsedOperations}
          renderItem={(item) => (
            <List.Item>
              <Space>
                <Tag color="blue">{item.type}</Tag>
                <span>{item.count} 次</span>
                <Progress
                  percent={(item.count / workflowStats.totalOperations * 100)}
                  size="small"
                  style={{ width: 100 }}
                />
              </Space>
            </List.Item>
          )}
        />
      </div>
    );
  };

  // 初始化数据
  useEffect(() => {
    if (visible) {
      loadData();
    }
  }, [visible, loadData]);

  return (
    <Modal
      title={t('workflow.title')}
      visible={visible}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="workflow-manager"
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('workflow.history')} key="history">
          {renderOperationHistory()}
        </TabPane>
        
        <TabPane tab={t('workflow.patterns')} key="patterns">
          {renderWorkflowPatterns()}
        </TabPane>
        
        <TabPane tab={t('workflow.suggestions')} key="suggestions">
          {renderSmartSuggestions()}
        </TabPane>
        
        <TabPane tab={t('workflow.stats')} key="stats">
          {renderStats()}
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default WorkflowManager;
