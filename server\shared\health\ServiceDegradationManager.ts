/**
 * 服务降级管理器
 * 实现服务降级策略、熔断器模式和服务限流
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';

/**
 * 降级级别枚举
 */
export enum DegradationLevel {
  NONE = 0,
  LIGHT = 1,
  MODERATE = 2,
  HEAVY = 3,
  CRITICAL = 4
}

/**
 * 熔断器状态枚举
 */
export enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open'
}

/**
 * 降级策略配置
 */
export interface DegradationStrategy {
  /** 策略ID */
  id: string;
  /** 策略名称 */
  name: string;
  /** 目标服务 */
  targetService: string;
  /** 降级级别 */
  level: DegradationLevel;
  /** 触发条件 */
  triggers: {
    errorRate: number;
    responseTime: number;
    concurrentRequests: number;
    queueLength: number;
  };
  /** 降级动作 */
  actions: Array<{
    type: 'disable_feature' | 'use_cache' | 'return_default' | 'redirect' | 'limit_rate' | 'custom';
    config: any;
    execute: () => Promise<void>;
    rollback: () => Promise<void>;
  }>;
  /** 恢复条件 */
  recovery: {
    errorRate: number;
    responseTime: number;
    stabilityPeriod: number;
  };
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 熔断器配置
 */
export interface CircuitBreakerConfig {
  /** 熔断器名称 */
  name: string;
  /** 目标服务 */
  targetService: string;
  /** 失败阈值 */
  failureThreshold: number;
  /** 成功阈值（半开状态） */
  successThreshold: number;
  /** 超时时间（毫秒） */
  timeout: number;
  /** 重置时间（毫秒） */
  resetTimeout: number;
  /** 监控窗口大小 */
  monitoringWindow: number;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 熔断器状态
 */
export interface CircuitBreakerStatus {
  /** 熔断器名称 */
  name: string;
  /** 当前状态 */
  state: CircuitBreakerState;
  /** 失败计数 */
  failureCount: number;
  /** 成功计数 */
  successCount: number;
  /** 最后失败时间 */
  lastFailureTime: number;
  /** 下次重试时间 */
  nextRetryTime: number;
  /** 总请求数 */
  totalRequests: number;
  /** 错误率 */
  errorRate: number;
}

/**
 * 限流配置
 */
export interface RateLimitConfig {
  /** 限流器名称 */
  name: string;
  /** 目标服务 */
  targetService: string;
  /** 每秒请求数限制 */
  requestsPerSecond: number;
  /** 突发请求数限制 */
  burstLimit: number;
  /** 窗口大小（毫秒） */
  windowSize: number;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 服务状态
 */
export interface ServiceStatus {
  /** 服务名称 */
  serviceName: string;
  /** 当前降级级别 */
  degradationLevel: DegradationLevel;
  /** 是否可用 */
  available: boolean;
  /** 当前错误率 */
  errorRate: number;
  /** 平均响应时间 */
  averageResponseTime: number;
  /** 并发请求数 */
  concurrentRequests: number;
  /** 队列长度 */
  queueLength: number;
  /** 熔断器状态 */
  circuitBreakerState: CircuitBreakerState;
  /** 最后更新时间 */
  lastUpdated: number;
}

/**
 * 服务降级管理器
 */
@Injectable()
export class ServiceDegradationManager extends EventEmitter {
  private readonly logger = new Logger(ServiceDegradationManager.name);

  /** 降级策略映射 */
  private degradationStrategies: Map<string, DegradationStrategy> = new Map();
  /** 熔断器配置映射 */
  private circuitBreakers: Map<string, CircuitBreakerConfig> = new Map();
  /** 熔断器状态映射 */
  private circuitBreakerStates: Map<string, CircuitBreakerStatus> = new Map();
  /** 限流配置映射 */
  private rateLimiters: Map<string, RateLimitConfig> = new Map();
  /** 服务状态映射 */
  private serviceStatuses: Map<string, ServiceStatus> = new Map();

  /** 请求计数器（用于限流） */
  private requestCounters: Map<string, Array<{ timestamp: number; count: number }>> = new Map();

  constructor(private readonly configService: ConfigService) {
    super();
    this.initializeDefaultConfigurations();
  }

  /**
   * 初始化默认配置
   */
  private initializeDefaultConfigurations(): void {
    // 数据库降级策略
    this.registerDegradationStrategy({
      id: 'database_degradation',
      name: '数据库降级',
      targetService: 'database',
      level: DegradationLevel.MODERATE,
      triggers: {
        errorRate: 0.1, // 10%
        responseTime: 5000, // 5秒
        concurrentRequests: 100,
        queueLength: 50
      },
      actions: [
        {
          type: 'use_cache',
          config: { cacheKey: 'database_fallback' },
          execute: async () => {
            this.logger.log('启用数据库缓存降级');
          },
          rollback: async () => {
            this.logger.log('恢复数据库正常访问');
          }
        }
      ],
      recovery: {
        errorRate: 0.05, // 5%
        responseTime: 2000, // 2秒
        stabilityPeriod: 300000 // 5分钟
      },
      enabled: true
    });

    // API熔断器
    this.registerCircuitBreaker({
      name: 'api_circuit_breaker',
      targetService: 'external_api',
      failureThreshold: 5,
      successThreshold: 3,
      timeout: 10000,
      resetTimeout: 60000,
      monitoringWindow: 100,
      enabled: true
    });

    // 限流配置
    this.registerRateLimiter({
      name: 'api_rate_limiter',
      targetService: 'api_gateway',
      requestsPerSecond: 100,
      burstLimit: 200,
      windowSize: 1000,
      enabled: true
    });
  }

  /**
   * 注册降级策略
   */
  public registerDegradationStrategy(strategy: DegradationStrategy): void {
    this.degradationStrategies.set(strategy.id, strategy);
    this.logger.log(`注册降级策略: ${strategy.name}`);
  }

  /**
   * 注册熔断器
   */
  public registerCircuitBreaker(config: CircuitBreakerConfig): void {
    this.circuitBreakers.set(config.name, config);
    
    // 初始化熔断器状态
    this.circuitBreakerStates.set(config.name, {
      name: config.name,
      state: CircuitBreakerState.CLOSED,
      failureCount: 0,
      successCount: 0,
      lastFailureTime: 0,
      nextRetryTime: 0,
      totalRequests: 0,
      errorRate: 0
    });

    this.logger.log(`注册熔断器: ${config.name}`);
  }

  /**
   * 注册限流器
   */
  public registerRateLimiter(config: RateLimitConfig): void {
    this.rateLimiters.set(config.name, config);
    this.requestCounters.set(config.name, []);
    this.logger.log(`注册限流器: ${config.name}`);
  }

  /**
   * 检查服务是否可用
   */
  public async checkServiceAvailability(serviceName: string): Promise<boolean> {
    const status = this.serviceStatuses.get(serviceName);
    if (!status) return true;

    // 检查熔断器状态
    const circuitBreaker = this.getCircuitBreakerForService(serviceName);
    if (circuitBreaker && !this.isCircuitBreakerClosed(circuitBreaker.name)) {
      return false;
    }

    // 检查限流
    if (!this.checkRateLimit(serviceName)) {
      return false;
    }

    // 检查降级状态
    return status.available;
  }

  /**
   * 记录请求结果
   */
  public recordRequestResult(
    serviceName: string,
    success: boolean,
    responseTime: number
  ): void {
    // 更新服务状态
    this.updateServiceStatus(serviceName, success, responseTime);

    // 更新熔断器状态
    const circuitBreaker = this.getCircuitBreakerForService(serviceName);
    if (circuitBreaker) {
      this.updateCircuitBreakerState(circuitBreaker.name, success);
    }

    // 检查是否需要降级
    this.checkDegradationTriggers(serviceName);
  }

  /**
   * 更新服务状态
   */
  private updateServiceStatus(serviceName: string, success: boolean, responseTime: number): void {
    let status = this.serviceStatuses.get(serviceName);
    
    if (!status) {
      status = {
        serviceName,
        degradationLevel: DegradationLevel.NONE,
        available: true,
        errorRate: 0,
        averageResponseTime: 0,
        concurrentRequests: 0,
        queueLength: 0,
        circuitBreakerState: CircuitBreakerState.CLOSED,
        lastUpdated: Date.now()
      };
      this.serviceStatuses.set(serviceName, status);
    }

    // 更新响应时间
    if (status.averageResponseTime === 0) {
      status.averageResponseTime = responseTime;
    } else {
      status.averageResponseTime = (status.averageResponseTime + responseTime) / 2;
    }

    // 更新错误率（简化计算）
    if (!success) {
      status.errorRate = Math.min(status.errorRate + 0.01, 1);
    } else {
      status.errorRate = Math.max(status.errorRate - 0.005, 0);
    }

    status.lastUpdated = Date.now();
  }

  /**
   * 更新熔断器状态
   */
  private updateCircuitBreakerState(circuitBreakerName: string, success: boolean): void {
    const config = this.circuitBreakers.get(circuitBreakerName);
    const state = this.circuitBreakerStates.get(circuitBreakerName);
    
    if (!config || !state) return;

    state.totalRequests++;

    if (success) {
      state.successCount++;
      
      if (state.state === CircuitBreakerState.HALF_OPEN) {
        if (state.successCount >= config.successThreshold) {
          // 恢复到关闭状态
          state.state = CircuitBreakerState.CLOSED;
          state.failureCount = 0;
          state.successCount = 0;
          this.logger.log(`熔断器 ${circuitBreakerName} 恢复到关闭状态`);
          this.emit('circuitBreakerClosed', { name: circuitBreakerName });
        }
      }
    } else {
      state.failureCount++;
      state.lastFailureTime = Date.now();
      
      if (state.state === CircuitBreakerState.CLOSED) {
        if (state.failureCount >= config.failureThreshold) {
          // 切换到开启状态
          state.state = CircuitBreakerState.OPEN;
          state.nextRetryTime = Date.now() + config.resetTimeout;
          this.logger.warn(`熔断器 ${circuitBreakerName} 开启`);
          this.emit('circuitBreakerOpened', { name: circuitBreakerName });
        }
      } else if (state.state === CircuitBreakerState.HALF_OPEN) {
        // 重新开启
        state.state = CircuitBreakerState.OPEN;
        state.nextRetryTime = Date.now() + config.resetTimeout;
        state.successCount = 0;
      }
    }

    // 计算错误率
    state.errorRate = state.totalRequests > 0 ? 
      (state.totalRequests - state.successCount) / state.totalRequests : 0;

    // 检查是否可以切换到半开状态
    if (state.state === CircuitBreakerState.OPEN && Date.now() >= state.nextRetryTime) {
      state.state = CircuitBreakerState.HALF_OPEN;
      state.successCount = 0;
      this.logger.log(`熔断器 ${circuitBreakerName} 切换到半开状态`);
      this.emit('circuitBreakerHalfOpened', { name: circuitBreakerName });
    }
  }

  /**
   * 检查降级触发条件
   */
  private checkDegradationTriggers(serviceName: string): void {
    const status = this.serviceStatuses.get(serviceName);
    if (!status) return;

    const strategies = Array.from(this.degradationStrategies.values())
      .filter(strategy => strategy.targetService === serviceName && strategy.enabled);

    for (const strategy of strategies) {
      const shouldDegrade = this.shouldTriggerDegradation(strategy, status);
      const shouldRecover = this.shouldTriggerRecovery(strategy, status);

      if (shouldDegrade && status.degradationLevel < strategy.level) {
        this.executeDegradation(strategy);
      } else if (shouldRecover && status.degradationLevel >= strategy.level) {
        this.executeRecovery(strategy);
      }
    }
  }

  /**
   * 判断是否应该触发降级
   */
  private shouldTriggerDegradation(strategy: DegradationStrategy, status: ServiceStatus): boolean {
    const { triggers } = strategy;
    
    return status.errorRate >= triggers.errorRate ||
           status.averageResponseTime >= triggers.responseTime ||
           status.concurrentRequests >= triggers.concurrentRequests ||
           status.queueLength >= triggers.queueLength;
  }

  /**
   * 判断是否应该触发恢复
   */
  private shouldTriggerRecovery(strategy: DegradationStrategy, status: ServiceStatus): boolean {
    const { recovery } = strategy;
    
    return status.errorRate <= recovery.errorRate &&
           status.averageResponseTime <= recovery.responseTime;
  }

  /**
   * 执行降级
   */
  private async executeDegradation(strategy: DegradationStrategy): Promise<void> {
    this.logger.warn(`执行服务降级: ${strategy.name}`);

    try {
      for (const action of strategy.actions) {
        await action.execute();
      }

      const status = this.serviceStatuses.get(strategy.targetService);
      if (status) {
        status.degradationLevel = strategy.level;
        status.available = strategy.level < DegradationLevel.CRITICAL;
      }

      this.emit('serviceDegraded', {
        serviceName: strategy.targetService,
        strategy,
        level: strategy.level
      });
    } catch (error) {
      this.logger.error(`降级执行失败: ${strategy.name}`, error);
    }
  }

  /**
   * 执行恢复
   */
  private async executeRecovery(strategy: DegradationStrategy): Promise<void> {
    this.logger.log(`执行服务恢复: ${strategy.name}`);

    try {
      for (const action of strategy.actions) {
        await action.rollback();
      }

      const status = this.serviceStatuses.get(strategy.targetService);
      if (status) {
        status.degradationLevel = DegradationLevel.NONE;
        status.available = true;
      }

      this.emit('serviceRecovered', {
        serviceName: strategy.targetService,
        strategy
      });
    } catch (error) {
      this.logger.error(`恢复执行失败: ${strategy.name}`, error);
    }
  }

  /**
   * 检查限流
   */
  private checkRateLimit(serviceName: string): boolean {
    const rateLimiter = Array.from(this.rateLimiters.values())
      .find(limiter => limiter.targetService === serviceName && limiter.enabled);
    
    if (!rateLimiter) return true;

    const now = Date.now();
    const windowStart = now - rateLimiter.windowSize;
    const counters = this.requestCounters.get(rateLimiter.name) || [];

    // 清理过期计数
    const validCounters = counters.filter(counter => counter.timestamp > windowStart);
    this.requestCounters.set(rateLimiter.name, validCounters);

    // 计算当前窗口内的请求数
    const currentRequests = validCounters.reduce((sum, counter) => sum + counter.count, 0);

    // 检查是否超过限制
    if (currentRequests >= rateLimiter.requestsPerSecond) {
      this.emit('rateLimitExceeded', {
        serviceName,
        rateLimiter,
        currentRequests
      });
      return false;
    }

    // 记录当前请求
    validCounters.push({ timestamp: now, count: 1 });
    return true;
  }

  /**
   * 获取服务的熔断器
   */
  private getCircuitBreakerForService(serviceName: string): CircuitBreakerConfig | undefined {
    return Array.from(this.circuitBreakers.values())
      .find(cb => cb.targetService === serviceName && cb.enabled);
  }

  /**
   * 检查熔断器是否关闭
   */
  private isCircuitBreakerClosed(circuitBreakerName: string): boolean {
    const state = this.circuitBreakerStates.get(circuitBreakerName);
    return state ? state.state === CircuitBreakerState.CLOSED : true;
  }

  /**
   * 获取服务状态
   */
  public getServiceStatus(serviceName?: string): ServiceStatus[] {
    if (serviceName) {
      const status = this.serviceStatuses.get(serviceName);
      return status ? [status] : [];
    }
    return Array.from(this.serviceStatuses.values());
  }

  /**
   * 获取熔断器状态
   */
  public getCircuitBreakerStatus(name?: string): CircuitBreakerStatus[] {
    if (name) {
      const status = this.circuitBreakerStates.get(name);
      return status ? [status] : [];
    }
    return Array.from(this.circuitBreakerStates.values());
  }

  /**
   * 手动触发降级
   */
  public async manualDegrade(serviceName: string, level: DegradationLevel): Promise<void> {
    const strategies = Array.from(this.degradationStrategies.values())
      .filter(strategy => strategy.targetService === serviceName && strategy.level === level);

    for (const strategy of strategies) {
      await this.executeDegradation(strategy);
    }
  }

  /**
   * 手动恢复服务
   */
  public async manualRecover(serviceName: string): Promise<void> {
    const strategies = Array.from(this.degradationStrategies.values())
      .filter(strategy => strategy.targetService === serviceName);

    for (const strategy of strategies) {
      await this.executeRecovery(strategy);
    }
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.removeAllListeners();
    this.logger.log('服务降级管理器已销毁');
  }
}
