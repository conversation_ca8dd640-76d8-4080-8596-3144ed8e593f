/**
 * 服务发现管理器
 * 实现服务注册、发现和动态配置更新
 */
import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * 服务注册信息
 */
export interface ServiceRegistration {
  /** 服务ID */
  id: string;
  /** 服务名称 */
  name: string;
  /** 版本 */
  version: string;
  /** 主机地址 */
  host: string;
  /** 端口 */
  port: number;
  /** 协议 */
  protocol: 'http' | 'https' | 'tcp' | 'udp';
  /** 健康检查端点 */
  healthCheckPath?: string;
  /** 标签 */
  tags: string[];
  /** 元数据 */
  metadata: Record<string, any>;
  /** 注册时间 */
  registeredAt: number;
  /** 最后心跳时间 */
  lastHeartbeat: number;
  /** TTL（生存时间，秒） */
  ttl: number;
}

/**
 * 服务查询条件
 */
export interface ServiceQuery {
  /** 服务名称 */
  name?: string;
  /** 版本 */
  version?: string;
  /** 标签 */
  tags?: string[];
  /** 是否只返回健康的服务 */
  healthyOnly?: boolean;
  /** 元数据过滤 */
  metadata?: Record<string, any>;
}

/**
 * 服务发现事件
 */
export interface ServiceDiscoveryEvent {
  /** 事件类型 */
  type: 'registered' | 'deregistered' | 'updated' | 'health_changed';
  /** 服务信息 */
  service: ServiceRegistration;
  /** 事件时间 */
  timestamp: number;
}

/**
 * 服务发现统计
 */
export interface ServiceDiscoveryStats {
  /** 总服务数 */
  totalServices: number;
  /** 健康服务数 */
  healthyServices: number;
  /** 不健康服务数 */
  unhealthyServices: number;
  /** 注册事件数 */
  registrationEvents: number;
  /** 注销事件数 */
  deregistrationEvents: number;
  /** 心跳事件数 */
  heartbeatEvents: number;
  /** 最后更新时间 */
  lastUpdated: number;
}

/**
 * 服务发现管理器
 */
@Injectable()
export class ServiceDiscoveryManager extends EventEmitter implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceDiscoveryManager.name);

  /** 服务注册表 */
  private serviceRegistry: Map<string, ServiceRegistration> = new Map();
  /** 服务监听器 */
  private serviceWatchers: Map<string, Set<(services: ServiceRegistration[]) => void>> = new Map();
  /** 统计信息 */
  private stats: ServiceDiscoveryStats;

  /** 心跳检查间隔 */
  private readonly heartbeatInterval: number;
  /** 服务TTL默认值 */
  private readonly defaultTTL: number;
  /** 是否启用自动清理 */
  private readonly autoCleanup: boolean;

  constructor(private readonly configService: ConfigService) {
    super();

    this.heartbeatInterval = this.configService.get<number>('SERVICE_DISCOVERY_HEARTBEAT_INTERVAL', 30000);
    this.defaultTTL = this.configService.get<number>('SERVICE_DISCOVERY_DEFAULT_TTL', 60);
    this.autoCleanup = this.configService.get<boolean>('SERVICE_DISCOVERY_AUTO_CLEANUP', true);

    this.initializeStats();
  }

  /**
   * 模块初始化
   */
  public async onModuleInit(): Promise<void> {
    this.logger.log('服务发现管理器已启动');
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    this.stats = {
      totalServices: 0,
      healthyServices: 0,
      unhealthyServices: 0,
      registrationEvents: 0,
      deregistrationEvents: 0,
      heartbeatEvents: 0,
      lastUpdated: Date.now()
    };
  }

  /**
   * 注册服务
   */
  public registerService(registration: Omit<ServiceRegistration, 'id' | 'registeredAt' | 'lastHeartbeat'>): string {
    const serviceId = this.generateServiceId(registration.name, registration.host, registration.port);
    
    const fullRegistration: ServiceRegistration = {
      ...registration,
      id: serviceId,
      registeredAt: Date.now(),
      lastHeartbeat: Date.now(),
      ttl: registration.ttl || this.defaultTTL
    };

    this.serviceRegistry.set(serviceId, fullRegistration);
    
    this.stats.registrationEvents++;
    this.updateStats();

    this.logger.log(`服务注册: ${registration.name} (${serviceId})`);
    
    const event: ServiceDiscoveryEvent = {
      type: 'registered',
      service: fullRegistration,
      timestamp: Date.now()
    };
    
    this.emit('serviceRegistered', event);
    this.notifyWatchers(registration.name);

    return serviceId;
  }

  /**
   * 注销服务
   */
  public deregisterService(serviceId: string): boolean {
    const service = this.serviceRegistry.get(serviceId);
    if (!service) {
      return false;
    }

    this.serviceRegistry.delete(serviceId);
    
    this.stats.deregistrationEvents++;
    this.updateStats();

    this.logger.log(`服务注销: ${service.name} (${serviceId})`);
    
    const event: ServiceDiscoveryEvent = {
      type: 'deregistered',
      service,
      timestamp: Date.now()
    };
    
    this.emit('serviceDeregistered', event);
    this.notifyWatchers(service.name);

    return true;
  }

  /**
   * 更新服务心跳
   */
  public heartbeat(serviceId: string): boolean {
    const service = this.serviceRegistry.get(serviceId);
    if (!service) {
      return false;
    }

    service.lastHeartbeat = Date.now();
    this.stats.heartbeatEvents++;

    this.logger.debug(`服务心跳: ${service.name} (${serviceId})`);
    return true;
  }

  /**
   * 发现服务
   */
  public discoverServices(query: ServiceQuery = {}): ServiceRegistration[] {
    let services = Array.from(this.serviceRegistry.values());

    // 按服务名称过滤
    if (query.name) {
      services = services.filter(service => service.name === query.name);
    }

    // 按版本过滤
    if (query.version) {
      services = services.filter(service => service.version === query.version);
    }

    // 按标签过滤
    if (query.tags && query.tags.length > 0) {
      services = services.filter(service => 
        query.tags!.every(tag => service.tags.includes(tag))
      );
    }

    // 按元数据过滤
    if (query.metadata) {
      services = services.filter(service => {
        return Object.entries(query.metadata!).every(([key, value]) => 
          service.metadata[key] === value
        );
      });
    }

    // 只返回健康的服务
    if (query.healthyOnly) {
      services = services.filter(service => this.isServiceHealthy(service));
    }

    return services;
  }

  /**
   * 获取服务详情
   */
  public getService(serviceId: string): ServiceRegistration | null {
    return this.serviceRegistry.get(serviceId) || null;
  }

  /**
   * 监听服务变化
   */
  public watchServices(
    serviceName: string,
    callback: (services: ServiceRegistration[]) => void
  ): () => void {
    if (!this.serviceWatchers.has(serviceName)) {
      this.serviceWatchers.set(serviceName, new Set());
    }

    const watchers = this.serviceWatchers.get(serviceName)!;
    watchers.add(callback);

    // 立即返回当前服务列表
    const currentServices = this.discoverServices({ name: serviceName });
    callback(currentServices);

    // 返回取消监听的函数
    return () => {
      watchers.delete(callback);
      if (watchers.size === 0) {
        this.serviceWatchers.delete(serviceName);
      }
    };
  }

  /**
   * 通知监听器
   */
  private notifyWatchers(serviceName: string): void {
    const watchers = this.serviceWatchers.get(serviceName);
    if (!watchers) return;

    const services = this.discoverServices({ name: serviceName });
    watchers.forEach(callback => {
      try {
        callback(services);
      } catch (error) {
        this.logger.error('服务监听器回调失败:', error);
      }
    });
  }

  /**
   * 检查服务是否健康
   */
  private isServiceHealthy(service: ServiceRegistration): boolean {
    const now = Date.now();
    const ttlMs = service.ttl * 1000;
    return (now - service.lastHeartbeat) < ttlMs;
  }

  /**
   * 生成服务ID
   */
  private generateServiceId(name: string, host: string, port: number): string {
    return `${name}_${host}_${port}_${Date.now()}`;
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    const services = Array.from(this.serviceRegistry.values());
    
    this.stats.totalServices = services.length;
    this.stats.healthyServices = services.filter(service => this.isServiceHealthy(service)).length;
    this.stats.unhealthyServices = this.stats.totalServices - this.stats.healthyServices;
    this.stats.lastUpdated = Date.now();
  }

  /**
   * 定时清理过期服务
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  private cleanupExpiredServices(): void {
    if (!this.autoCleanup) return;

    const now = Date.now();
    const expiredServices: string[] = [];

    for (const [serviceId, service] of this.serviceRegistry.entries()) {
      const ttlMs = service.ttl * 1000;
      if ((now - service.lastHeartbeat) > ttlMs) {
        expiredServices.push(serviceId);
      }
    }

    for (const serviceId of expiredServices) {
      const service = this.serviceRegistry.get(serviceId);
      if (service) {
        this.logger.warn(`清理过期服务: ${service.name} (${serviceId})`);
        this.deregisterService(serviceId);
      }
    }

    if (expiredServices.length > 0) {
      this.updateStats();
    }
  }

  /**
   * 定时更新统计信息
   */
  @Cron(CronExpression.EVERY_MINUTE)
  private updateStatsScheduled(): void {
    this.updateStats();
    this.emit('statsUpdated', this.stats);
  }

  /**
   * 获取服务列表（按服务名分组）
   */
  public getServicesByName(): Map<string, ServiceRegistration[]> {
    const servicesByName = new Map<string, ServiceRegistration[]>();
    
    for (const service of this.serviceRegistry.values()) {
      if (!servicesByName.has(service.name)) {
        servicesByName.set(service.name, []);
      }
      servicesByName.get(service.name)!.push(service);
    }

    return servicesByName;
  }

  /**
   * 获取服务统计信息
   */
  public getStats(): ServiceDiscoveryStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * 获取所有服务
   */
  public getAllServices(): ServiceRegistration[] {
    return Array.from(this.serviceRegistry.values());
  }

  /**
   * 批量注册服务
   */
  public batchRegisterServices(
    registrations: Array<Omit<ServiceRegistration, 'id' | 'registeredAt' | 'lastHeartbeat'>>
  ): string[] {
    const serviceIds: string[] = [];
    
    for (const registration of registrations) {
      const serviceId = this.registerService(registration);
      serviceIds.push(serviceId);
    }

    return serviceIds;
  }

  /**
   * 批量注销服务
   */
  public batchDeregisterServices(serviceIds: string[]): number {
    let deregisteredCount = 0;
    
    for (const serviceId of serviceIds) {
      if (this.deregisterService(serviceId)) {
        deregisteredCount++;
      }
    }

    return deregisteredCount;
  }

  /**
   * 更新服务信息
   */
  public updateService(
    serviceId: string,
    updates: Partial<Pick<ServiceRegistration, 'tags' | 'metadata' | 'healthCheckPath' | 'ttl'>>
  ): boolean {
    const service = this.serviceRegistry.get(serviceId);
    if (!service) {
      return false;
    }

    // 更新允许的字段
    if (updates.tags !== undefined) {
      service.tags = updates.tags;
    }
    if (updates.metadata !== undefined) {
      service.metadata = { ...service.metadata, ...updates.metadata };
    }
    if (updates.healthCheckPath !== undefined) {
      service.healthCheckPath = updates.healthCheckPath;
    }
    if (updates.ttl !== undefined) {
      service.ttl = updates.ttl;
    }

    this.logger.log(`服务更新: ${service.name} (${serviceId})`);
    
    const event: ServiceDiscoveryEvent = {
      type: 'updated',
      service,
      timestamp: Date.now()
    };
    
    this.emit('serviceUpdated', event);
    this.notifyWatchers(service.name);

    return true;
  }

  /**
   * 导出服务注册表
   */
  public exportRegistry(): ServiceRegistration[] {
    return Array.from(this.serviceRegistry.values());
  }

  /**
   * 导入服务注册表
   */
  public importRegistry(services: ServiceRegistration[]): void {
    this.serviceRegistry.clear();
    
    for (const service of services) {
      this.serviceRegistry.set(service.id, service);
    }

    this.updateStats();
    this.logger.log(`导入 ${services.length} 个服务注册信息`);
  }

  /**
   * 清空服务注册表
   */
  public clearRegistry(): void {
    const serviceCount = this.serviceRegistry.size;
    this.serviceRegistry.clear();
    this.updateStats();
    this.logger.log(`清空服务注册表，移除 ${serviceCount} 个服务`);
  }

  /**
   * 模块销毁时清理资源
   */
  public async onModuleDestroy(): Promise<void> {
    this.serviceWatchers.clear();
    this.removeAllListeners();
    this.logger.log('服务发现管理器已销毁');
  }
}
