/**
 * 队列性能监控服务
 * 实现队列性能监控、瓶颈分析和性能优化建议
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 队列名称 */
  queueName: string;
  /** 时间戳 */
  timestamp: number;
  /** 吞吐量（任务/秒） */
  throughput: number;
  /** 平均等待时间（毫秒） */
  averageWaitTime: number;
  /** 平均处理时间（毫秒） */
  averageProcessingTime: number;
  /** 队列长度 */
  queueLength: number;
  /** 活跃工作者数量 */
  activeWorkers: number;
  /** CPU使用率 */
  cpuUsage: number;
  /** 内存使用率 */
  memoryUsage: number;
  /** 错误率 */
  errorRate: number;
  /** 延迟百分位数 */
  latencyPercentiles: {
    p50: number;
    p90: number;
    p95: number;
    p99: number;
  };
}

/**
 * 性能警报
 */
export interface PerformanceAlert {
  /** 警报ID */
  id: string;
  /** 队列名称 */
  queueName: string;
  /** 警报类型 */
  type: 'high_latency' | 'low_throughput' | 'high_error_rate' | 'queue_backlog' | 'resource_exhaustion';
  /** 警报级别 */
  level: 'info' | 'warning' | 'error' | 'critical';
  /** 警报消息 */
  message: string;
  /** 当前值 */
  currentValue: number;
  /** 阈值 */
  threshold: number;
  /** 创建时间 */
  timestamp: number;
  /** 是否已确认 */
  acknowledged: boolean;
}

/**
 * 性能趋势
 */
export interface PerformanceTrend {
  /** 队列名称 */
  queueName: string;
  /** 指标名称 */
  metricName: string;
  /** 趋势方向 */
  direction: 'increasing' | 'decreasing' | 'stable';
  /** 变化率 */
  changeRate: number;
  /** 时间窗口（毫秒） */
  timeWindow: number;
  /** 数据点 */
  dataPoints: Array<{ timestamp: number; value: number }>;
}

/**
 * 优化建议
 */
export interface OptimizationSuggestion {
  /** 建议ID */
  id: string;
  /** 队列名称 */
  queueName: string;
  /** 建议类型 */
  type: 'scaling' | 'configuration' | 'architecture' | 'resource';
  /** 优先级 */
  priority: 'low' | 'medium' | 'high' | 'critical';
  /** 建议标题 */
  title: string;
  /** 建议描述 */
  description: string;
  /** 预期影响 */
  expectedImpact: string;
  /** 实施难度 */
  implementationDifficulty: 'easy' | 'medium' | 'hard';
  /** 创建时间 */
  timestamp: number;
}

/**
 * 队列性能监控器
 */
@Injectable()
export class QueuePerformanceMonitor extends EventEmitter {
  private readonly logger = new Logger(QueuePerformanceMonitor.name);

  /** 性能指标历史 */
  private metricsHistory: Map<string, PerformanceMetrics[]> = new Map();
  /** 性能警报 */
  private performanceAlerts: PerformanceAlert[] = [];
  /** 优化建议 */
  private optimizationSuggestions: OptimizationSuggestion[] = [];

  /** 监控配置 */
  private readonly monitoringConfig = {
    maxHistorySize: 1000,
    alertThresholds: {
      highLatency: 5000, // 5秒
      lowThroughput: 1, // 1任务/秒
      highErrorRate: 0.1, // 10%
      queueBacklog: 100, // 100个任务
      cpuUsage: 0.8, // 80%
      memoryUsage: 0.9 // 90%
    },
    trendAnalysisWindow: 3600000 // 1小时
  };

  constructor(private readonly configService: ConfigService) {
    super();
    this.loadConfiguration();
  }

  /**
   * 加载配置
   */
  private loadConfiguration(): void {
    this.monitoringConfig.maxHistorySize = this.configService.get<number>('QUEUE_METRICS_HISTORY_SIZE', 1000);
    this.monitoringConfig.alertThresholds.highLatency = this.configService.get<number>('QUEUE_HIGH_LATENCY_THRESHOLD', 5000);
    this.monitoringConfig.alertThresholds.lowThroughput = this.configService.get<number>('QUEUE_LOW_THROUGHPUT_THRESHOLD', 1);
    this.monitoringConfig.alertThresholds.highErrorRate = this.configService.get<number>('QUEUE_HIGH_ERROR_RATE_THRESHOLD', 0.1);
    this.monitoringConfig.alertThresholds.queueBacklog = this.configService.get<number>('QUEUE_BACKLOG_THRESHOLD', 100);
  }

  /**
   * 记录性能指标
   */
  public recordMetrics(metrics: PerformanceMetrics): void {
    const queueName = metrics.queueName;
    
    if (!this.metricsHistory.has(queueName)) {
      this.metricsHistory.set(queueName, []);
    }

    const history = this.metricsHistory.get(queueName)!;
    history.push(metrics);

    // 限制历史记录大小
    if (history.length > this.monitoringConfig.maxHistorySize) {
      history.shift();
    }

    // 检查性能警报
    this.checkPerformanceAlerts(metrics);

    // 发出事件
    this.emit('metricsRecorded', metrics);
  }

  /**
   * 检查性能警报
   */
  private checkPerformanceAlerts(metrics: PerformanceMetrics): void {
    const alerts: PerformanceAlert[] = [];

    // 检查高延迟
    if (metrics.averageProcessingTime > this.monitoringConfig.alertThresholds.highLatency) {
      alerts.push({
        id: this.generateAlertId(),
        queueName: metrics.queueName,
        type: 'high_latency',
        level: metrics.averageProcessingTime > this.monitoringConfig.alertThresholds.highLatency * 2 ? 'critical' : 'warning',
        message: `队列 ${metrics.queueName} 平均处理时间过高: ${metrics.averageProcessingTime}ms`,
        currentValue: metrics.averageProcessingTime,
        threshold: this.monitoringConfig.alertThresholds.highLatency,
        timestamp: Date.now(),
        acknowledged: false
      });
    }

    // 检查低吞吐量
    if (metrics.throughput < this.monitoringConfig.alertThresholds.lowThroughput) {
      alerts.push({
        id: this.generateAlertId(),
        queueName: metrics.queueName,
        type: 'low_throughput',
        level: 'warning',
        message: `队列 ${metrics.queueName} 吞吐量过低: ${metrics.throughput} 任务/秒`,
        currentValue: metrics.throughput,
        threshold: this.monitoringConfig.alertThresholds.lowThroughput,
        timestamp: Date.now(),
        acknowledged: false
      });
    }

    // 检查高错误率
    if (metrics.errorRate > this.monitoringConfig.alertThresholds.highErrorRate) {
      alerts.push({
        id: this.generateAlertId(),
        queueName: metrics.queueName,
        type: 'high_error_rate',
        level: metrics.errorRate > this.monitoringConfig.alertThresholds.highErrorRate * 2 ? 'critical' : 'error',
        message: `队列 ${metrics.queueName} 错误率过高: ${(metrics.errorRate * 100).toFixed(1)}%`,
        currentValue: metrics.errorRate,
        threshold: this.monitoringConfig.alertThresholds.highErrorRate,
        timestamp: Date.now(),
        acknowledged: false
      });
    }

    // 检查队列积压
    if (metrics.queueLength > this.monitoringConfig.alertThresholds.queueBacklog) {
      alerts.push({
        id: this.generateAlertId(),
        queueName: metrics.queueName,
        type: 'queue_backlog',
        level: metrics.queueLength > this.monitoringConfig.alertThresholds.queueBacklog * 2 ? 'critical' : 'warning',
        message: `队列 ${metrics.queueName} 积压任务过多: ${metrics.queueLength} 个任务`,
        currentValue: metrics.queueLength,
        threshold: this.monitoringConfig.alertThresholds.queueBacklog,
        timestamp: Date.now(),
        acknowledged: false
      });
    }

    // 检查资源使用
    if (metrics.cpuUsage > this.monitoringConfig.alertThresholds.cpuUsage || 
        metrics.memoryUsage > this.monitoringConfig.alertThresholds.memoryUsage) {
      alerts.push({
        id: this.generateAlertId(),
        queueName: metrics.queueName,
        type: 'resource_exhaustion',
        level: 'error',
        message: `队列 ${metrics.queueName} 资源使用率过高: CPU ${(metrics.cpuUsage * 100).toFixed(1)}%, 内存 ${(metrics.memoryUsage * 100).toFixed(1)}%`,
        currentValue: Math.max(metrics.cpuUsage, metrics.memoryUsage),
        threshold: Math.min(this.monitoringConfig.alertThresholds.cpuUsage, this.monitoringConfig.alertThresholds.memoryUsage),
        timestamp: Date.now(),
        acknowledged: false
      });
    }

    // 添加新警报
    this.performanceAlerts.push(...alerts);

    // 限制警报数量
    if (this.performanceAlerts.length > 1000) {
      this.performanceAlerts = this.performanceAlerts.slice(-500);
    }

    // 发出警报事件
    alerts.forEach(alert => {
      this.emit('performanceAlert', alert);
    });
  }

  /**
   * 分析性能趋势
   */
  public analyzePerformanceTrends(queueName: string): PerformanceTrend[] {
    const history = this.metricsHistory.get(queueName);
    if (!history || history.length < 10) {
      return [];
    }

    const trends: PerformanceTrend[] = [];
    const now = Date.now();
    const windowStart = now - this.monitoringConfig.trendAnalysisWindow;

    // 过滤时间窗口内的数据
    const recentMetrics = history.filter(m => m.timestamp >= windowStart);
    if (recentMetrics.length < 5) {
      return trends;
    }

    // 分析各项指标的趋势
    const metricsToAnalyze = [
      'throughput',
      'averageProcessingTime',
      'queueLength',
      'errorRate'
    ];

    for (const metricName of metricsToAnalyze) {
      const dataPoints = recentMetrics.map(m => ({
        timestamp: m.timestamp,
        value: m[metricName as keyof PerformanceMetrics] as number
      }));

      const trend = this.calculateTrend(dataPoints);
      if (trend) {
        trends.push({
          queueName,
          metricName,
          direction: trend.direction,
          changeRate: trend.changeRate,
          timeWindow: this.monitoringConfig.trendAnalysisWindow,
          dataPoints
        });
      }
    }

    return trends;
  }

  /**
   * 计算趋势
   */
  private calculateTrend(dataPoints: Array<{ timestamp: number; value: number }>): {
    direction: 'increasing' | 'decreasing' | 'stable';
    changeRate: number;
  } | null {
    if (dataPoints.length < 3) return null;

    // 使用线性回归计算趋势
    const n = dataPoints.length;
    const sumX = dataPoints.reduce((sum, point, index) => sum + index, 0);
    const sumY = dataPoints.reduce((sum, point) => sum + point.value, 0);
    const sumXY = dataPoints.reduce((sum, point, index) => sum + index * point.value, 0);
    const sumXX = dataPoints.reduce((sum, point, index) => sum + index * index, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const changeRate = Math.abs(slope);

    let direction: 'increasing' | 'decreasing' | 'stable';
    if (Math.abs(slope) < 0.01) {
      direction = 'stable';
    } else if (slope > 0) {
      direction = 'increasing';
    } else {
      direction = 'decreasing';
    }

    return { direction, changeRate };
  }

  /**
   * 生成优化建议
   */
  public generateOptimizationSuggestions(queueName: string): OptimizationSuggestion[] {
    const history = this.metricsHistory.get(queueName);
    if (!history || history.length === 0) {
      return [];
    }

    const suggestions: OptimizationSuggestion[] = [];
    const latestMetrics = history[history.length - 1];
    const trends = this.analyzePerformanceTrends(queueName);

    // 基于当前指标生成建议
    if (latestMetrics.averageProcessingTime > this.monitoringConfig.alertThresholds.highLatency) {
      suggestions.push({
        id: this.generateSuggestionId(),
        queueName,
        type: 'scaling',
        priority: 'high',
        title: '增加工作者数量',
        description: `当前平均处理时间为 ${latestMetrics.averageProcessingTime}ms，建议增加工作者数量以提高并发处理能力`,
        expectedImpact: '可将处理时间降低 30-50%',
        implementationDifficulty: 'easy',
        timestamp: Date.now()
      });
    }

    if (latestMetrics.queueLength > this.monitoringConfig.alertThresholds.queueBacklog) {
      suggestions.push({
        id: this.generateSuggestionId(),
        queueName,
        type: 'configuration',
        priority: 'medium',
        title: '优化批处理大小',
        description: `当前队列积压 ${latestMetrics.queueLength} 个任务，建议调整批处理大小以提高吞吐量`,
        expectedImpact: '可提高吞吐量 20-40%',
        implementationDifficulty: 'medium',
        timestamp: Date.now()
      });
    }

    if (latestMetrics.errorRate > this.monitoringConfig.alertThresholds.highErrorRate) {
      suggestions.push({
        id: this.generateSuggestionId(),
        queueName,
        type: 'configuration',
        priority: 'critical',
        title: '优化重试策略',
        description: `当前错误率为 ${(latestMetrics.errorRate * 100).toFixed(1)}%，建议优化重试策略和错误处理`,
        expectedImpact: '可降低错误率至 5% 以下',
        implementationDifficulty: 'medium',
        timestamp: Date.now()
      });
    }

    // 基于趋势生成建议
    const throughputTrend = trends.find(t => t.metricName === 'throughput');
    if (throughputTrend && throughputTrend.direction === 'decreasing') {
      suggestions.push({
        id: this.generateSuggestionId(),
        queueName,
        type: 'architecture',
        priority: 'medium',
        title: '考虑水平扩展',
        description: '吞吐量呈下降趋势，建议考虑水平扩展或负载均衡',
        expectedImpact: '可恢复并提升吞吐量',
        implementationDifficulty: 'hard',
        timestamp: Date.now()
      });
    }

    this.optimizationSuggestions.push(...suggestions);
    return suggestions;
  }

  /**
   * 定时性能分析
   */
  @Cron(CronExpression.EVERY_10_MINUTES)
  private performScheduledAnalysis(): void {
    for (const queueName of this.metricsHistory.keys()) {
      const trends = this.analyzePerformanceTrends(queueName);
      const suggestions = this.generateOptimizationSuggestions(queueName);

      if (trends.length > 0 || suggestions.length > 0) {
        this.emit('performanceAnalysis', {
          queueName,
          trends,
          suggestions,
          timestamp: Date.now()
        });
      }
    }
  }

  /**
   * 获取性能指标历史
   */
  public getMetricsHistory(queueName: string, limit?: number): PerformanceMetrics[] {
    const history = this.metricsHistory.get(queueName) || [];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * 获取性能警报
   */
  public getPerformanceAlerts(queueName?: string): PerformanceAlert[] {
    const alerts = this.performanceAlerts;
    return queueName ? alerts.filter(alert => alert.queueName === queueName) : alerts;
  }

  /**
   * 获取优化建议
   */
  public getOptimizationSuggestions(queueName?: string): OptimizationSuggestion[] {
    const suggestions = this.optimizationSuggestions;
    return queueName ? suggestions.filter(s => s.queueName === queueName) : suggestions;
  }

  /**
   * 确认警报
   */
  public acknowledgeAlert(alertId: string): boolean {
    const alert = this.performanceAlerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.emit('alertAcknowledged', alert);
      return true;
    }
    return false;
  }

  /**
   * 生成警报ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成建议ID
   */
  private generateSuggestionId(): string {
    return `suggestion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清理过期数据
   */
  public cleanupExpiredData(): void {
    const now = Date.now();
    const expiredThreshold = now - 7 * 24 * 3600000; // 7天前

    // 清理过期警报
    this.performanceAlerts = this.performanceAlerts.filter(
      alert => alert.timestamp > expiredThreshold
    );

    // 清理过期建议
    this.optimizationSuggestions = this.optimizationSuggestions.filter(
      suggestion => suggestion.timestamp > expiredThreshold
    );

    this.logger.debug('清理过期性能监控数据完成');
  }

  /**
   * 销毁监控器
   */
  public destroy(): void {
    this.removeAllListeners();
    this.logger.log('队列性能监控器已销毁');
  }
}
