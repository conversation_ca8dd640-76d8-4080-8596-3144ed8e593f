/**
 * 体积渲染系统模块
 * 导出所有体积渲染相关的类和接口
 */

// 导出体积渲染系统
export { VolumetricRenderingSystem } from './VolumetricRenderingSystem';
export type { 
  VolumetricRenderingConfig,
  VolumetricLightConfig,
  FogConfig,
  AtmosphericScatteringConfig
} from './VolumetricRenderingSystem';
export { VolumetricQuality } from './VolumetricRenderingSystem';

// 导出体积光效果
export { VolumetricLightEffect } from './VolumetricLightEffect';
export type { VolumetricLightEffectConfig } from './VolumetricLightEffect';

// 导出雾效果
export { FogEffect } from './FogEffect';
export type { FogEffectConfig } from './FogEffect';

// 导出大气散射效果
export { AtmosphericScatteringEffect } from './AtmosphericScatteringEffect';
export type { AtmosphericScatteringEffectConfig } from './AtmosphericScatteringEffect';
