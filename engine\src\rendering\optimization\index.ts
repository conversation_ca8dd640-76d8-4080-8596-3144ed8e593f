/**
 * 渲染优化模块
 * 导出所有渲染优化相关的类和接口
 */

// 导出LOD系统
export { LODSystem } from './LODSystem';
export type { LODSystemOptions, LODSystemEventType } from './LODSystem';

// 导出自适应LOD系统
export { AdaptiveLODSystem } from './DynamicLODSystem';
export type { AdaptiveLODSystemOptions, AdaptiveStrategy, LODTransitionState, AdaptiveLODSystemEventType } from './DynamicLODSystem';

// 导出LOD组件
export { LODComponent } from './LODComponent';
export type { LODLevel, LODLevelConfig, LODComponentOptions } from './LODComponent';

// 导出LOD生成器
export { LODGenerator } from './LODGenerator';
export type { LODGeneratorOptions, LODGeneratorResult } from './LODGenerator';

// 导出视锥体剔除系统
export { FrustumCullingSystem } from './FrustumCullingSystem';
export type { FrustumCullingSystemOptions, FrustumCullingSystemEventType } from './FrustumCullingSystem';

// 导出优化的遮挡剔除系统
export { OptimizedOcclusionCullingSystem } from './OptimizedOcclusionCullingSystem';
export type {
  OptimizedOcclusionCullingSystemOptions,
  OptimizedOcclusionCullingEventType,
  CullingStrategy,
  CullingStats
} from './OptimizedOcclusionCullingSystem';

// 导出可剔除组件
export { CullableComponent } from './CullableComponent';
export type { CullableComponentOptions } from './CullableComponent';

// 导出实例化渲染系统
export { InstancedRenderingSystem } from './InstancedRenderingSystem';
export type { InstancedRenderingSystemOptions, InstancedRenderingSystemEventType } from './InstancedRenderingSystem';

// 导出实例化组件
export { InstancedComponent } from './InstancedComponent';
export type { InstanceData, InstancedComponentOptions } from './InstancedComponent';

// 导出空间分割结构
export { Octree } from './spatial/Octree';
export type { OctreeOptions } from './spatial/Octree';

// 导出批处理系统
export { BatchingSystem } from './BatchingSystem';
export type { BatchingSystemOptions, BatchingSystemEventType, BatchGroup } from './BatchingSystem';

// 导出智能批处理系统
export { SmartBatchingSystem } from './SmartBatchingSystem';
export type {
  SmartBatchingSystemOptions,
  SmartBatchingStrategy,
  SmartBatchingEventType,
  BatchingAnalysis,
  SmartBatchingStats
} from './SmartBatchingSystem';

// 导出优化的渲染系统
export { OptimizedRenderingSystem } from './OptimizedRenderingSystem';
export type {
  OptimizedRenderingSystemOptions,
  OptimizedRenderingEventType,
  RenderStateType,
  RenderState,
  RenderQueueType,
  RenderItem,
  RenderBatch,
  MemoryStats
} from './OptimizedRenderingSystem';
