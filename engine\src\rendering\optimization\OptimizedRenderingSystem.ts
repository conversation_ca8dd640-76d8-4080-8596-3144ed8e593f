/**
 * 优化的渲染系统
 * 实现渲染状态管理、渲染队列排序和内存管理优化
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import { Renderer } from '../Renderer';
import { RenderSystem } from '../RenderSystem';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor, PerformanceMetricType } from '../../utils/PerformanceMonitor';
import { memoryManager } from '../../memory/EnhancedMemoryManager';

/**
 * 渲染状态类型
 */
export enum RenderStateType {
  /** 深度测试 */
  DEPTH_TEST = 'depth_test',
  /** 深度写入 */
  DEPTH_WRITE = 'depth_write',
  /** 混合模式 */
  BLENDING = 'blending',
  /** 剔除模式 */
  CULLING = 'culling',
  /** 多边形偏移 */
  POLYGON_OFFSET = 'polygon_offset',
  /** 模板测试 */
  STENCIL_TEST = 'stencil_test'
}

/**
 * 渲染状态
 */
export interface RenderState {
  /** 状态类型 */
  type: RenderStateType;
  /** 状态值 */
  value: any;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 渲染队列类型
 */
export enum RenderQueueType {
  /** 背景 */
  BACKGROUND = 0,
  /** 不透明 */
  OPAQUE = 1000,
  /** 透明 */
  TRANSPARENT = 2000,
  /** 覆盖 */
  OVERLAY = 3000
}

/**
 * 渲染项
 */
export interface RenderItem {
  /** 实体 */
  entity: Entity;
  /** 网格 */
  mesh: THREE.Mesh;
  /** 材质 */
  material: THREE.Material;
  /** 几何体 */
  geometry: THREE.BufferGeometry;
  /** 变换矩阵 */
  matrix: THREE.Matrix4;
  /** 渲染队列 */
  queue: RenderQueueType;
  /** 排序键 */
  sortKey: number;
  /** 距离 */
  distance: number;
  /** 是否可见 */
  visible: boolean;
}

/**
 * 渲染批次
 */
export interface RenderBatch {
  /** 批次ID */
  id: string;
  /** 渲染项列表 */
  items: RenderItem[];
  /** 材质 */
  material: THREE.Material;
  /** 几何体 */
  geometry: THREE.BufferGeometry;
  /** 实例数量 */
  instanceCount: number;
  /** 是否脏 */
  dirty: boolean;
}

/**
 * 内存统计信息
 */
export interface MemoryStats {
  /** 几何体内存（MB） */
  geometryMemory: number;
  /** 纹理内存（MB） */
  textureMemory: number;
  /** 渲染目标内存（MB） */
  renderTargetMemory: number;
  /** 总内存（MB） */
  totalMemory: number;
  /** 内存使用率 */
  memoryUsage: number;
}

/**
 * 优化的渲染系统配置
 */
export interface OptimizedRenderingSystemOptions {
  /** 是否启用状态排序 */
  enableStateSorting?: boolean;
  /** 是否启用深度排序 */
  enableDepthSorting?: boolean;
  /** 是否启用材质排序 */
  enableMaterialSorting?: boolean;
  /** 是否启用内存管理 */
  enableMemoryManagement?: boolean;
  /** 是否启用渲染批次 */
  enableRenderBatching?: boolean;
  /** 最大渲染项数量 */
  maxRenderItems?: number;
  /** 内存阈值（MB） */
  memoryThreshold?: number;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
}

/**
 * 优化的渲染系统事件类型
 */
export enum OptimizedRenderingEventType {
  /** 渲染开始 */
  RENDER_START = 'render_start',
  /** 渲染完成 */
  RENDER_COMPLETE = 'render_complete',
  /** 状态变更 */
  STATE_CHANGED = 'state_changed',
  /** 内存阈值触发 */
  MEMORY_THRESHOLD_TRIGGERED = 'memory_threshold_triggered',
  /** 批次创建 */
  BATCH_CREATED = 'batch_created',
  /** 批次更新 */
  BATCH_UPDATED = 'batch_updated'
}

/**
 * 优化的渲染系统
 */
export class OptimizedRenderingSystem extends RenderSystem {
  /** 配置选项 */
  private options: Required<OptimizedRenderingSystemOptions>;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();

  /** 当前渲染状态 */
  private currentStates: Map<RenderStateType, RenderState> = new Map();
  /** 渲染队列 */
  private renderQueues: Map<RenderQueueType, RenderItem[]> = new Map();
  /** 渲染批次 */
  private renderBatches: Map<string, RenderBatch> = new Map();

  /** 临时对象池 */
  private tempMatrix = new THREE.Matrix4();
  private tempVector = new THREE.Vector3();
  private tempQuaternion = new THREE.Quaternion();

  /** 内存统计 */
  private memoryStats: MemoryStats = {
    geometryMemory: 0,
    textureMemory: 0,
    renderTargetMemory: 0,
    totalMemory: 0,
    memoryUsage: 0
  };

  /** 渲染统计 */
  private renderStats = {
    drawCalls: 0,
    triangles: 0,
    vertices: 0,
    stateChanges: 0,
    batchCount: 0,
    renderTime: 0
  };

  /**
   * 创建优化的渲染系统
   * @param renderer 渲染器
   * @param options 配置选项
   */
  constructor(renderer: Renderer, options: OptimizedRenderingSystemOptions = {}) {
    super(renderer);

    // 设置默认配置
    this.options = {
      enableStateSorting: options.enableStateSorting !== undefined ? options.enableStateSorting : true,
      enableDepthSorting: options.enableDepthSorting !== undefined ? options.enableDepthSorting : true,
      enableMaterialSorting: options.enableMaterialSorting !== undefined ? options.enableMaterialSorting : true,
      enableMemoryManagement: options.enableMemoryManagement !== undefined ? options.enableMemoryManagement : true,
      enableRenderBatching: options.enableRenderBatching !== undefined ? options.enableRenderBatching : true,
      maxRenderItems: options.maxRenderItems || 10000,
      memoryThreshold: options.memoryThreshold || 512, // 512MB
      enablePerformanceMonitoring: options.enablePerformanceMonitoring !== undefined ? options.enablePerformanceMonitoring : true
    };

    // 初始化渲染队列
    this.initializeRenderQueues();
    
    // 初始化渲染状态
    this.initializeRenderStates();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return 'OptimizedRenderingSystem';
  }

  /**
   * 初始化渲染队列
   */
  private initializeRenderQueues(): void {
    this.renderQueues.set(RenderQueueType.BACKGROUND, []);
    this.renderQueues.set(RenderQueueType.OPAQUE, []);
    this.renderQueues.set(RenderQueueType.TRANSPARENT, []);
    this.renderQueues.set(RenderQueueType.OVERLAY, []);
  }

  /**
   * 初始化渲染状态
   */
  private initializeRenderStates(): void {
    // 设置默认渲染状态
    this.currentStates.set(RenderStateType.DEPTH_TEST, {
      type: RenderStateType.DEPTH_TEST,
      value: true,
      enabled: true
    });

    this.currentStates.set(RenderStateType.DEPTH_WRITE, {
      type: RenderStateType.DEPTH_WRITE,
      value: true,
      enabled: true
    });

    this.currentStates.set(RenderStateType.BLENDING, {
      type: RenderStateType.BLENDING,
      value: THREE.NormalBlending,
      enabled: false
    });

    this.currentStates.set(RenderStateType.CULLING, {
      type: RenderStateType.CULLING,
      value: THREE.BackSide,
      enabled: true
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.getActiveCamera() || !this.getActiveScene()) {
      return;
    }

    const startTime = performance.now();
    this.eventEmitter.emit(OptimizedRenderingEventType.RENDER_START);

    // 重置统计信息
    this.resetRenderStats();

    // 收集渲染项
    this.collectRenderItems();

    // 排序渲染队列
    this.sortRenderQueues();

    // 创建渲染批次
    if (this.options.enableRenderBatching) {
      this.createRenderBatches();
    }

    // 执行渲染
    this.executeRender();

    // 更新内存管理
    if (this.options.enableMemoryManagement) {
      this.updateMemoryManagement();
    }

    // 更新性能监控
    if (this.options.enablePerformanceMonitoring) {
      this.updatePerformanceMonitoring(deltaTime);
    }

    // 记录渲染时间
    this.renderStats.renderTime = performance.now() - startTime;
    this.eventEmitter.emit(OptimizedRenderingEventType.RENDER_COMPLETE, this.renderStats);

    // 调用父类更新
    super.update(deltaTime);
  }

  /**
   * 重置渲染统计
   */
  private resetRenderStats(): void {
    this.renderStats.drawCalls = 0;
    this.renderStats.triangles = 0;
    this.renderStats.vertices = 0;
    this.renderStats.stateChanges = 0;
    this.renderStats.batchCount = 0;

    // 清空渲染队列
    for (const queue of this.renderQueues.values()) {
      queue.length = 0;
    }
  }

  /**
   * 收集渲染项
   */
  private collectRenderItems(): void {
    if (!this.world) {
      return;
    }

    const camera = this.getActiveCamera();
    if (!camera) {
      return;
    }

    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有实体
    for (const entity of this.world.getEntities().values()) {
      const meshComponent = entity.getComponent('MeshComponent');
      const transform = entity.getComponent('Transform');

      if (!meshComponent || !transform) {
        continue;
      }

      const mesh = (meshComponent as any).getMesh();
      if (!mesh || !(mesh instanceof THREE.Mesh)) {
        continue;
      }

      // 创建渲染项
      const renderItem = this.createRenderItem(entity, mesh, transform, cameraPosition);
      if (renderItem) {
        // 添加到对应的渲染队列
        const queue = this.renderQueues.get(renderItem.queue);
        if (queue) {
          queue.push(renderItem);
        }
      }
    }
  }

  /**
   * 创建渲染项
   * @param entity 实体
   * @param mesh 网格
   * @param transform 变换组件
   * @param cameraPosition 相机位置
   * @returns 渲染项
   */
  private createRenderItem(entity: Entity, mesh: THREE.Mesh, transform: any, cameraPosition: THREE.Vector3): RenderItem | null {
    // 检查可见性
    if (!mesh.visible) {
      return null;
    }

    // 获取世界矩阵
    const worldMatrix = transform.getWorldMatrix();

    // 计算距离
    const worldPosition = this.tempVector.setFromMatrixPosition(worldMatrix);
    const distance = worldPosition.distanceTo(cameraPosition);

    // 确定渲染队列
    const queue = this.determineRenderQueue(mesh.material);

    // 计算排序键
    const sortKey = this.calculateSortKey(mesh.material, distance, queue);

    return {
      entity,
      mesh,
      material: Array.isArray(mesh.material) ? mesh.material[0] : mesh.material,
      geometry: mesh.geometry,
      matrix: worldMatrix.clone(),
      queue,
      sortKey,
      distance,
      visible: true
    };
  }

  /**
   * 确定渲染队列
   * @param material 材质
   * @returns 渲染队列类型
   */
  private determineRenderQueue(material: THREE.Material | THREE.Material[]): RenderQueueType {
    const mat = Array.isArray(material) ? material[0] : material;

    // 检查透明度
    if (mat.transparent || mat.opacity < 1.0) {
      return RenderQueueType.TRANSPARENT;
    }

    // 检查是否是背景材质
    if ((mat as any).isBackground) {
      return RenderQueueType.BACKGROUND;
    }

    // 检查是否是覆盖材质
    if ((mat as any).isOverlay) {
      return RenderQueueType.OVERLAY;
    }

    // 默认为不透明队列
    return RenderQueueType.OPAQUE;
  }

  /**
   * 计算排序键
   * @param material 材质
   * @param distance 距离
   * @param queue 渲染队列
   * @returns 排序键
   */
  private calculateSortKey(material: THREE.Material, distance: number, queue: RenderQueueType): number {
    let sortKey = 0;

    // 基于队列类型的基础排序
    sortKey += queue * 1000000;

    if (queue === RenderQueueType.TRANSPARENT) {
      // 透明物体按距离从远到近排序
      sortKey += (10000 - Math.min(distance, 9999));
    } else {
      // 不透明物体按材质和距离排序
      sortKey += this.getMaterialSortKey(material) * 1000;
      sortKey += Math.min(distance, 999);
    }

    return sortKey;
  }

  /**
   * 获取材质排序键
   * @param material 材质
   * @returns 材质排序键
   */
  private getMaterialSortKey(material: THREE.Material): number {
    // 基于材质类型和属性生成排序键
    let key = 0;

    // 材质类型
    if (material instanceof THREE.MeshBasicMaterial) key += 100;
    else if (material instanceof THREE.MeshLambertMaterial) key += 200;
    else if (material instanceof THREE.MeshPhongMaterial) key += 300;
    else if (material instanceof THREE.MeshStandardMaterial) key += 400;
    else if (material instanceof THREE.MeshPhysicalMaterial) key += 500;

    // 纹理数量
    const textureCount = this.getTextureCount(material);
    key += textureCount * 10;

    return key;
  }

  /**
   * 获取材质纹理数量
   * @param material 材质
   * @returns 纹理数量
   */
  private getTextureCount(material: THREE.Material): number {
    let count = 0;

    if ((material as any).map) count++;
    if ((material as any).normalMap) count++;
    if ((material as any).roughnessMap) count++;
    if ((material as any).metalnessMap) count++;
    if ((material as any).emissiveMap) count++;
    if ((material as any).aoMap) count++;
    if ((material as any).envMap) count++;

    return count;
  }

  /**
   * 排序渲染队列
   */
  private sortRenderQueues(): void {
    for (const [queueType, queue] of this.renderQueues.entries()) {
      if (queue.length === 0) {
        continue;
      }

      if (this.options.enableStateSorting || this.options.enableDepthSorting || this.options.enableMaterialSorting) {
        queue.sort((a, b) => {
          // 首先按排序键排序
          if (a.sortKey !== b.sortKey) {
            return a.sortKey - b.sortKey;
          }

          // 然后按材质排序（减少状态切换）
          if (this.options.enableMaterialSorting) {
            const materialA = a.material.uuid;
            const materialB = b.material.uuid;
            if (materialA !== materialB) {
              return materialA.localeCompare(materialB);
            }
          }

          // 最后按几何体排序
          const geometryA = a.geometry.uuid;
          const geometryB = b.geometry.uuid;
          return geometryA.localeCompare(geometryB);
        });
      }
    }
  }

  /**
   * 创建渲染批次
   */
  private createRenderBatches(): void {
    // 清空现有批次
    this.renderBatches.clear();

    // 为每个渲染队列创建批次
    for (const [queueType, queue] of this.renderQueues.entries()) {
      if (queue.length === 0) {
        continue;
      }

      this.createBatchesForQueue(queue, queueType);
    }
  }

  /**
   * 为队列创建批次
   * @param queue 渲染队列
   * @param queueType 队列类型
   */
  private createBatchesForQueue(queue: RenderItem[], queueType: RenderQueueType): void {
    let currentBatch: RenderItem[] = [];
    let currentMaterial: THREE.Material | null = null;
    let currentGeometry: THREE.BufferGeometry | null = null;

    for (const item of queue) {
      // 检查是否可以批处理
      const canBatch = currentMaterial && currentGeometry &&
                      item.material.uuid === currentMaterial.uuid &&
                      item.geometry.uuid === currentGeometry.uuid;

      if (canBatch && currentBatch.length < 100) { // 限制批次大小
        currentBatch.push(item);
      } else {
        // 创建新批次
        if (currentBatch.length > 0) {
          this.createRenderBatch(currentBatch, currentMaterial!, currentGeometry!);
        }

        currentBatch = [item];
        currentMaterial = item.material;
        currentGeometry = item.geometry;
      }
    }

    // 处理最后一个批次
    if (currentBatch.length > 0 && currentMaterial && currentGeometry) {
      this.createRenderBatch(currentBatch, currentMaterial, currentGeometry);
    }
  }

  /**
   * 创建渲染批次
   * @param items 渲染项列表
   * @param material 材质
   * @param geometry 几何体
   */
  private createRenderBatch(items: RenderItem[], material: THREE.Material, geometry: THREE.BufferGeometry): void {
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const batch: RenderBatch = {
      id: batchId,
      items: [...items],
      material,
      geometry,
      instanceCount: items.length,
      dirty: false
    };

    this.renderBatches.set(batchId, batch);
    this.renderStats.batchCount++;

    this.eventEmitter.emit(OptimizedRenderingEventType.BATCH_CREATED, batch);
  }

  /**
   * 执行渲染
   */
  private executeRender(): void {
    const renderer = this.getRenderer().getThreeRenderer();
    const camera = this.getActiveCamera()?.getThreeCamera();
    const scene = this.getActiveScene()?.getThreeScene();

    if (!renderer || !camera || !scene) {
      return;
    }

    // 按队列顺序渲染
    const queueOrder = [
      RenderQueueType.BACKGROUND,
      RenderQueueType.OPAQUE,
      RenderQueueType.TRANSPARENT,
      RenderQueueType.OVERLAY
    ];

    for (const queueType of queueOrder) {
      this.renderQueue(queueType, renderer, camera, scene);
    }
  }

  /**
   * 渲染队列
   * @param queueType 队列类型
   * @param renderer 渲染器
   * @param camera 相机
   * @param scene 场景
   */
  private renderQueue(queueType: RenderQueueType, renderer: THREE.WebGLRenderer, camera: THREE.Camera, scene: THREE.Scene): void {
    const queue = this.renderQueues.get(queueType);
    if (!queue || queue.length === 0) {
      return;
    }

    // 设置队列特定的渲染状态
    this.setQueueRenderStates(queueType, renderer);

    if (this.options.enableRenderBatching) {
      // 使用批次渲染
      this.renderBatches(queueType, renderer, camera, scene);
    } else {
      // 直接渲染
      this.renderItems(queue, renderer, camera, scene);
    }
  }

  /**
   * 设置队列渲染状态
   * @param queueType 队列类型
   * @param renderer 渲染器
   */
  private setQueueRenderStates(queueType: RenderQueueType, renderer: THREE.WebGLRenderer): void {
    const gl = renderer.getContext();

    switch (queueType) {
      case RenderQueueType.BACKGROUND:
        this.setRenderState(RenderStateType.DEPTH_TEST, false, gl);
        this.setRenderState(RenderStateType.DEPTH_WRITE, false, gl);
        break;

      case RenderQueueType.OPAQUE:
        this.setRenderState(RenderStateType.DEPTH_TEST, true, gl);
        this.setRenderState(RenderStateType.DEPTH_WRITE, true, gl);
        this.setRenderState(RenderStateType.BLENDING, false, gl);
        break;

      case RenderQueueType.TRANSPARENT:
        this.setRenderState(RenderStateType.DEPTH_TEST, true, gl);
        this.setRenderState(RenderStateType.DEPTH_WRITE, false, gl);
        this.setRenderState(RenderStateType.BLENDING, true, gl);
        break;

      case RenderQueueType.OVERLAY:
        this.setRenderState(RenderStateType.DEPTH_TEST, false, gl);
        this.setRenderState(RenderStateType.DEPTH_WRITE, false, gl);
        break;
    }
  }

  /**
   * 设置渲染状态
   * @param stateType 状态类型
   * @param value 状态值
   * @param gl WebGL上下文
   */
  private setRenderState(stateType: RenderStateType, value: any, gl: WebGLRenderingContext | WebGL2RenderingContext): void {
    const currentState = this.currentStates.get(stateType);

    if (!currentState || currentState.value !== value || currentState.enabled !== !!value) {
      // 状态发生变化，更新状态
      this.currentStates.set(stateType, {
        type: stateType,
        value,
        enabled: !!value
      });

      // 应用状态到WebGL
      this.applyRenderState(stateType, value, gl);
      this.renderStats.stateChanges++;

      this.eventEmitter.emit(OptimizedRenderingEventType.STATE_CHANGED, stateType, value);
    }
  }

  /**
   * 应用渲染状态到WebGL
   * @param stateType 状态类型
   * @param value 状态值
   * @param gl WebGL上下文
   */
  private applyRenderState(stateType: RenderStateType, value: any, gl: WebGLRenderingContext | WebGL2RenderingContext): void {
    switch (stateType) {
      case RenderStateType.DEPTH_TEST:
        if (value) {
          gl.enable(gl.DEPTH_TEST);
        } else {
          gl.disable(gl.DEPTH_TEST);
        }
        break;

      case RenderStateType.DEPTH_WRITE:
        gl.depthMask(!!value);
        break;

      case RenderStateType.BLENDING:
        if (value) {
          gl.enable(gl.BLEND);
        } else {
          gl.disable(gl.BLEND);
        }
        break;

      case RenderStateType.CULLING:
        if (value !== null) {
          gl.enable(gl.CULL_FACE);
          gl.cullFace(value);
        } else {
          gl.disable(gl.CULL_FACE);
        }
        break;
    }
  }

  /**
   * 渲染批次
   * @param queueType 队列类型
   * @param renderer 渲染器
   * @param camera 相机
   * @param scene 场景
   */
  private renderBatches(queueType: RenderQueueType, renderer: THREE.WebGLRenderer, camera: THREE.Camera, scene: THREE.Scene): void {
    for (const batch of this.renderBatches.values()) {
      if (batch.items.length === 0) {
        continue;
      }

      // 检查批次中的项是否属于当前队列
      const firstItem = batch.items[0];
      if (firstItem.queue !== queueType) {
        continue;
      }

      this.renderBatch(batch, renderer, camera, scene);
    }
  }

  /**
   * 渲染单个批次
   * @param batch 渲染批次
   * @param renderer 渲染器
   * @param camera 相机
   * @param scene 场景
   */
  private renderBatch(batch: RenderBatch, renderer: THREE.WebGLRenderer, camera: THREE.Camera, scene: THREE.Scene): void {
    if (batch.instanceCount > 1) {
      // 使用实例化渲染
      this.renderInstancedBatch(batch, renderer, camera, scene);
    } else {
      // 单个物体渲染
      this.renderSingleItem(batch.items[0], renderer, camera, scene);
    }
  }

  /**
   * 渲染实例化批次
   * @param batch 渲染批次
   * @param renderer 渲染器
   * @param camera 相机
   * @param scene 场景
   */
  private renderInstancedBatch(batch: RenderBatch, renderer: THREE.WebGLRenderer, camera: THREE.Camera, scene: THREE.Scene): void {
    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(batch.geometry, batch.material, batch.instanceCount);

    // 设置实例矩阵
    for (let i = 0; i < batch.items.length; i++) {
      instancedMesh.setMatrixAt(i, batch.items[i].matrix);
    }

    instancedMesh.instanceMatrix.needsUpdate = true;

    // 渲染实例化网格
    renderer.render(scene, camera);
    this.renderStats.drawCalls++;
    this.renderStats.triangles += this.getGeometryTriangleCount(batch.geometry) * batch.instanceCount;
    this.renderStats.vertices += this.getGeometryVertexCount(batch.geometry) * batch.instanceCount;
  }

  /**
   * 渲染单个项
   * @param item 渲染项
   * @param renderer 渲染器
   * @param camera 相机
   * @param scene 场景
   */
  private renderSingleItem(item: RenderItem, renderer: THREE.WebGLRenderer, camera: THREE.Camera, scene: THREE.Scene): void {
    // 设置网格变换
    item.mesh.matrix.copy(item.matrix);
    item.mesh.matrixAutoUpdate = false;

    // 渲染网格
    renderer.render(scene, camera);
    this.renderStats.drawCalls++;
    this.renderStats.triangles += this.getGeometryTriangleCount(item.geometry);
    this.renderStats.vertices += this.getGeometryVertexCount(item.geometry);
  }

  /**
   * 渲染项列表
   * @param items 渲染项列表
   * @param renderer 渲染器
   * @param camera 相机
   * @param scene 场景
   */
  private renderItems(items: RenderItem[], renderer: THREE.WebGLRenderer, camera: THREE.Camera, scene: THREE.Scene): void {
    for (const item of items) {
      this.renderSingleItem(item, renderer, camera, scene);
    }
  }

  /**
   * 获取几何体三角形数量
   * @param geometry 几何体
   * @returns 三角形数量
   */
  private getGeometryTriangleCount(geometry: THREE.BufferGeometry): number {
    const index = geometry.index;
    const position = geometry.getAttribute('position');

    if (index) {
      return index.count / 3;
    } else if (position) {
      return position.count / 3;
    }

    return 0;
  }

  /**
   * 获取几何体顶点数量
   * @param geometry 几何体
   * @returns 顶点数量
   */
  private getGeometryVertexCount(geometry: THREE.BufferGeometry): number {
    const position = geometry.getAttribute('position');
    return position ? position.count : 0;
  }

  /**
   * 更新内存管理
   */
  private updateMemoryManagement(): void {
    // 更新内存统计
    this.updateMemoryStats();

    // 检查内存阈值
    if (this.memoryStats.totalMemory > this.options.memoryThreshold) {
      this.eventEmitter.emit(OptimizedRenderingEventType.MEMORY_THRESHOLD_TRIGGERED, this.memoryStats);
      this.performMemoryCleanup();
    }
  }

  /**
   * 更新内存统计
   */
  private updateMemoryStats(): void {
    const renderer = this.getRenderer().getThreeRenderer();
    const info = renderer.info;

    // 估算内存使用量
    this.memoryStats.geometryMemory = info.memory.geometries * 0.1; // 估算每个几何体0.1MB
    this.memoryStats.textureMemory = info.memory.textures * 0.5; // 估算每个纹理0.5MB
    this.memoryStats.renderTargetMemory = 0; // 需要更精确的计算
    this.memoryStats.totalMemory = this.memoryStats.geometryMemory + this.memoryStats.textureMemory + this.memoryStats.renderTargetMemory;
    this.memoryStats.memoryUsage = this.memoryStats.totalMemory / this.options.memoryThreshold;
  }

  /**
   * 执行内存清理
   */
  private performMemoryCleanup(): void {
    // 清理未使用的几何体和材质
    const renderer = this.getRenderer().getThreeRenderer();

    // 强制垃圾回收（如果可用）
    if ((window as any).gc) {
      (window as any).gc();
    }

    Debug.log('OptimizedRenderingSystem', '执行内存清理');
  }

  /**
   * 更新性能监控
   * @param deltaTime 帧间隔时间
   */
  private updatePerformanceMonitoring(deltaTime: number): void {
    // 更新性能指标
    this.performanceMonitor.updateMetric(PerformanceMetricType.DRAW_CALLS, this.renderStats.drawCalls);
    this.performanceMonitor.updateMetric(PerformanceMetricType.TRIANGLES, this.renderStats.triangles);
    this.performanceMonitor.updateMetric(PerformanceMetricType.FRAME_TIME, this.renderStats.renderTime);
    this.performanceMonitor.updateMetric(PerformanceMetricType.MEMORY_USAGE, this.memoryStats.totalMemory);
  }

  /**
   * 获取渲染统计
   * @returns 渲染统计
   */
  public getRenderStats(): typeof this.renderStats {
    return { ...this.renderStats };
  }

  /**
   * 获取内存统计
   * @returns 内存统计
   */
  public getMemoryStats(): MemoryStats {
    return { ...this.memoryStats };
  }

  /**
   * 获取当前渲染状态
   * @returns 渲染状态映射
   */
  public getCurrentStates(): Map<RenderStateType, RenderState> {
    return new Map(this.currentStates);
  }

  /**
   * 设置配置选项
   * @param options 配置选项
   */
  public setOptions(options: Partial<OptimizedRenderingSystemOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * 获取配置选项
   * @returns 配置选项
   */
  public getOptions(): Required<OptimizedRenderingSystemOptions> {
    return { ...this.options };
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    super.dispose();

    // 清理资源
    this.renderQueues.clear();
    this.renderBatches.clear();
    this.currentStates.clear();

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();
  }
}
