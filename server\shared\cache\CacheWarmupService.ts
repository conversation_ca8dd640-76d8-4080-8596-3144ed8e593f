/**
 * 缓存预热服务
 * 实现智能缓存预热、热点数据识别和预加载策略
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MultiLevelCacheManager } from './MultiLevelCacheManager';
import { EventEmitter } from 'events';

/**
 * 预热策略枚举
 */
export enum WarmupStrategy {
  IMMEDIATE = 'immediate', // 立即预热
  SCHEDULED = 'scheduled', // 定时预热
  ON_DEMAND = 'on_demand', // 按需预热
  PREDICTIVE = 'predictive' // 预测性预热
}

/**
 * 热点数据项
 */
export interface HotDataItem {
  /** 数据键 */
  key: string;
  /** 访问频率 */
  frequency: number;
  /** 最后访问时间 */
  lastAccessed: number;
  /** 数据大小 */
  size: number;
  /** 重要性评分 */
  importance: number;
  /** 数据类型 */
  dataType: string;
  /** 业务标签 */
  tags: string[];
}

/**
 * 预热任务
 */
export interface WarmupTask {
  /** 任务ID */
  id: string;
  /** 任务名称 */
  name: string;
  /** 预热策略 */
  strategy: WarmupStrategy;
  /** 数据源函数 */
  dataSource: () => Promise<Array<{ key: string; value: any; ttl?: number; tags?: string[] }>>;
  /** 执行条件 */
  condition?: () => boolean;
  /** 优先级 */
  priority: number;
  /** 是否启用 */
  enabled: boolean;
  /** 最后执行时间 */
  lastExecution?: number;
  /** 执行次数 */
  executionCount: number;
  /** 平均执行时间 */
  averageExecutionTime: number;
}

/**
 * 预热统计
 */
export interface WarmupStats {
  /** 总预热次数 */
  totalWarmups: number;
  /** 成功预热次数 */
  successfulWarmups: number;
  /** 失败预热次数 */
  failedWarmups: number;
  /** 预热的数据项总数 */
  totalItemsWarmed: number;
  /** 平均预热时间 */
  averageWarmupTime: number;
  /** 最后预热时间 */
  lastWarmupTime: number;
  /** 热点数据数量 */
  hotDataCount: number;
}

/**
 * 缓存预热服务
 */
@Injectable()
export class CacheWarmupService extends EventEmitter implements OnModuleInit {
  private readonly logger = new Logger(CacheWarmupService.name);

  /** 预热任务映射 */
  private warmupTasks: Map<string, WarmupTask> = new Map();
  /** 热点数据映射 */
  private hotDataItems: Map<string, HotDataItem> = new Map();
  /** 预热统计 */
  private stats: WarmupStats;

  /** 配置参数 */
  private readonly enableAutoWarmup: boolean;
  private readonly hotDataThreshold: number;
  private readonly maxHotDataItems: number;
  private readonly warmupBatchSize: number;
  private readonly warmupTimeout: number;

  /** 正在执行的预热任务 */
  private runningTasks: Set<string> = new Set();

  constructor(
    private readonly cacheManager: MultiLevelCacheManager,
    private readonly configService: ConfigService
  ) {
    super();

    this.enableAutoWarmup = this.configService.get<boolean>('CACHE_AUTO_WARMUP', true);
    this.hotDataThreshold = this.configService.get<number>('CACHE_HOT_DATA_THRESHOLD', 10);
    this.maxHotDataItems = this.configService.get<number>('CACHE_MAX_HOT_DATA_ITEMS', 1000);
    this.warmupBatchSize = this.configService.get<number>('CACHE_WARMUP_BATCH_SIZE', 100);
    this.warmupTimeout = this.configService.get<number>('CACHE_WARMUP_TIMEOUT', 30000);

    this.initializeStats();
  }

  /**
   * 模块初始化
   */
  public async onModuleInit(): Promise<void> {
    if (this.enableAutoWarmup) {
      await this.initializeDefaultTasks();
      this.logger.log('缓存预热服务已启动');
    }
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    this.stats = {
      totalWarmups: 0,
      successfulWarmups: 0,
      failedWarmups: 0,
      totalItemsWarmed: 0,
      averageWarmupTime: 0,
      lastWarmupTime: 0,
      hotDataCount: 0
    };
  }

  /**
   * 初始化默认预热任务
   */
  private async initializeDefaultTasks(): Promise<void> {
    // 系统配置预热任务
    this.registerTask({
      id: 'system_config',
      name: '系统配置预热',
      strategy: WarmupStrategy.IMMEDIATE,
      dataSource: async () => {
        // 这里应该从数据库加载系统配置
        return [
          { key: 'system:config:app', value: { name: 'DL Engine', version: '2.0' }, ttl: 3600 },
          { key: 'system:config:features', value: { ai: true, vr: true }, ttl: 3600 }
        ];
      },
      priority: 10,
      enabled: true,
      executionCount: 0,
      averageExecutionTime: 0
    });

    // 用户会话预热任务
    this.registerTask({
      id: 'user_sessions',
      name: '用户会话预热',
      strategy: WarmupStrategy.SCHEDULED,
      dataSource: async () => {
        // 这里应该从数据库加载活跃用户会话
        return [];
      },
      priority: 8,
      enabled: true,
      executionCount: 0,
      averageExecutionTime: 0
    });

    // 热点项目数据预热任务
    this.registerTask({
      id: 'hot_projects',
      name: '热点项目预热',
      strategy: WarmupStrategy.PREDICTIVE,
      dataSource: async () => {
        // 这里应该从数据库加载热点项目数据
        return [];
      },
      condition: () => this.hotDataItems.size > 0,
      priority: 6,
      enabled: true,
      executionCount: 0,
      averageExecutionTime: 0
    });
  }

  /**
   * 注册预热任务
   */
  public registerTask(task: WarmupTask): void {
    this.warmupTasks.set(task.id, task);
    this.logger.log(`注册预热任务: ${task.name}`);
    this.emit('taskRegistered', task);
  }

  /**
   * 注销预热任务
   */
  public unregisterTask(taskId: string): void {
    const task = this.warmupTasks.get(taskId);
    if (task) {
      this.warmupTasks.delete(taskId);
      this.runningTasks.delete(taskId);
      this.logger.log(`注销预热任务: ${task.name}`);
      this.emit('taskUnregistered', task);
    }
  }

  /**
   * 执行预热任务
   */
  public async executeTask(taskId: string): Promise<boolean> {
    const task = this.warmupTasks.get(taskId);
    if (!task || !task.enabled) {
      return false;
    }

    if (this.runningTasks.has(taskId)) {
      this.logger.warn(`预热任务 ${task.name} 正在执行中`);
      return false;
    }

    // 检查执行条件
    if (task.condition && !task.condition()) {
      this.logger.debug(`预热任务 ${task.name} 条件不满足，跳过执行`);
      return false;
    }

    this.runningTasks.add(taskId);
    const startTime = Date.now();

    try {
      this.logger.log(`开始执行预热任务: ${task.name}`);
      
      // 获取数据
      const data = await Promise.race([
        task.dataSource(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('预热任务超时')), this.warmupTimeout)
        )
      ]);

      // 分批预热
      let warmedCount = 0;
      for (let i = 0; i < data.length; i += this.warmupBatchSize) {
        const batch = data.slice(i, i + this.warmupBatchSize);
        await this.cacheManager.warmup(batch);
        warmedCount += batch.length;
      }

      // 更新任务统计
      const executionTime = Date.now() - startTime;
      task.lastExecution = Date.now();
      task.executionCount++;
      task.averageExecutionTime = (task.averageExecutionTime * (task.executionCount - 1) + executionTime) / task.executionCount;

      // 更新全局统计
      this.stats.totalWarmups++;
      this.stats.successfulWarmups++;
      this.stats.totalItemsWarmed += warmedCount;
      this.stats.lastWarmupTime = Date.now();
      this.updateAverageWarmupTime(executionTime);

      this.logger.log(`预热任务 ${task.name} 执行完成，预热了 ${warmedCount} 个项目，耗时 ${executionTime}ms`);
      this.emit('taskCompleted', { task, warmedCount, executionTime });

      return true;

    } catch (error) {
      this.logger.error(`预热任务 ${task.name} 执行失败:`, error);
      this.stats.totalWarmups++;
      this.stats.failedWarmups++;
      this.emit('taskFailed', { task, error });
      return false;

    } finally {
      this.runningTasks.delete(taskId);
    }
  }

  /**
   * 执行所有启用的预热任务
   */
  public async executeAllTasks(): Promise<void> {
    const tasks = Array.from(this.warmupTasks.values())
      .filter(task => task.enabled)
      .sort((a, b) => b.priority - a.priority);

    for (const task of tasks) {
      await this.executeTask(task.id);
    }
  }

  /**
   * 记录热点数据访问
   */
  public recordHotDataAccess(key: string, dataType: string, size: number = 0, tags: string[] = []): void {
    const existing = this.hotDataItems.get(key);
    const now = Date.now();

    if (existing) {
      existing.frequency++;
      existing.lastAccessed = now;
      existing.importance = this.calculateImportance(existing);
    } else {
      const hotDataItem: HotDataItem = {
        key,
        frequency: 1,
        lastAccessed: now,
        size,
        importance: 1,
        dataType,
        tags
      };
      hotDataItem.importance = this.calculateImportance(hotDataItem);
      this.hotDataItems.set(key, hotDataItem);
    }

    // 限制热点数据数量
    if (this.hotDataItems.size > this.maxHotDataItems) {
      this.cleanupHotData();
    }

    this.stats.hotDataCount = this.hotDataItems.size;
  }

  /**
   * 计算数据重要性
   */
  private calculateImportance(item: HotDataItem): number {
    const frequencyScore = Math.min(item.frequency / this.hotDataThreshold, 1);
    const recencyScore = Math.max(0, 1 - (Date.now() - item.lastAccessed) / (24 * 3600000)); // 24小时内的新鲜度
    const sizeScore = item.size > 0 ? Math.min(1 / (item.size / 1024), 1) : 0.5; // 小数据优先

    return frequencyScore * 0.5 + recencyScore * 0.3 + sizeScore * 0.2;
  }

  /**
   * 清理热点数据
   */
  private cleanupHotData(): void {
    const items = Array.from(this.hotDataItems.entries())
      .sort((a, b) => a[1].importance - b[1].importance);

    const toRemove = items.slice(0, Math.floor(this.maxHotDataItems * 0.1));
    for (const [key] of toRemove) {
      this.hotDataItems.delete(key);
    }
  }

  /**
   * 获取热点数据
   */
  public getHotData(limit: number = 100): HotDataItem[] {
    return Array.from(this.hotDataItems.values())
      .sort((a, b) => b.importance - a.importance)
      .slice(0, limit);
  }

  /**
   * 预测性预热
   */
  public async predictiveWarmup(): Promise<void> {
    const hotData = this.getHotData(50);
    const predictions: Array<{ key: string; value: any; ttl?: number; tags?: string[] }> = [];

    for (const item of hotData) {
      // 这里应该实现预测逻辑，例如基于历史访问模式预测可能需要的数据
      // 简化实现：为高频访问的数据生成相关的预测键
      if (item.frequency > this.hotDataThreshold) {
        predictions.push({
          key: `predicted:${item.key}`,
          value: { predicted: true, originalKey: item.key },
          ttl: 1800, // 30分钟
          tags: ['predicted', ...item.tags]
        });
      }
    }

    if (predictions.length > 0) {
      await this.cacheManager.warmup(predictions);
      this.logger.log(`预测性预热完成，预热了 ${predictions.length} 个项目`);
    }
  }

  /**
   * 定时预热任务
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async scheduledWarmup(): Promise<void> {
    if (!this.enableAutoWarmup) return;

    const scheduledTasks = Array.from(this.warmupTasks.values())
      .filter(task => task.enabled && task.strategy === WarmupStrategy.SCHEDULED);

    for (const task of scheduledTasks) {
      await this.executeTask(task.id);
    }
  }

  /**
   * 定时预测性预热
   */
  @Cron(CronExpression.EVERY_30_MINUTES)
  private async scheduledPredictiveWarmup(): Promise<void> {
    if (!this.enableAutoWarmup) return;
    await this.predictiveWarmup();
  }

  /**
   * 更新平均预热时间
   */
  private updateAverageWarmupTime(executionTime: number): void {
    if (this.stats.successfulWarmups === 1) {
      this.stats.averageWarmupTime = executionTime;
    } else {
      this.stats.averageWarmupTime = (this.stats.averageWarmupTime * (this.stats.successfulWarmups - 1) + executionTime) / this.stats.successfulWarmups;
    }
  }

  /**
   * 获取预热统计
   */
  public getStats(): WarmupStats {
    return { ...this.stats };
  }

  /**
   * 获取预热任务列表
   */
  public getTasks(): WarmupTask[] {
    return Array.from(this.warmupTasks.values());
  }

  /**
   * 获取正在运行的任务
   */
  public getRunningTasks(): string[] {
    return Array.from(this.runningTasks);
  }
}
