/**
 * 快捷键管理器样式
 */
.shortcut-manager {
  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-modal-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .shortcut-manager-toolbar {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;

    .ant-input-search {
      .ant-input {
        border-radius: 4px;
      }
    }

    .ant-select {
      .ant-select-selector {
        border-radius: 4px;
      }
    }

    .ant-btn {
      border-radius: 4px;
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
    }

    .ant-table-tbody > tr {
      &:hover {
        background: #f5f5f5;
      }

      td {
        padding: 12px 16px;
      }
    }
  }

  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;

    &.ant-tag-blue {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 500;
    }
  }

  .ant-switch {
    &.ant-switch-checked {
      background-color: #1890ff;
    }
  }

  .ant-alert {
    border-radius: 6px;
    margin-bottom: 16px;

    &.ant-alert-warning {
      background-color: #fffbe6;
      border-color: #ffe58f;

      .ant-alert-icon {
        color: #faad14;
      }
    }

    .ant-alert-description {
      margin-top: 8px;

      .ant-tag {
        margin: 2px 4px;
      }
    }
  }

  .ant-tabs {
    .ant-tabs-tab {
      font-weight: 500;
      
      &.ant-tabs-tab-active {
        color: #1890ff;
      }
    }

    .ant-tabs-content-holder {
      padding-top: 16px;
    }
  }

  // 无冲突状态
  .no-conflicts-container {
    text-align: center;
    padding: 50px 20px;

    .success-icon {
      font-size: 48px;
      color: #52c41a;
      margin-bottom: 16px;
    }

    .success-title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    .success-description {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }

    .shortcut-manager-toolbar {
      .ant-space {
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }

  // 深色主题支持
  &.dark-theme {
    .ant-modal-content {
      background: #1f1f1f;
      color: #fff;
    }

    .ant-modal-header {
      background: #1f1f1f;
      border-color: #434343;

      .ant-modal-title {
        color: #fff;
      }
    }

    .shortcut-manager-toolbar {
      background: #2a2a2a;
      border-color: #434343;
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #2a2a2a;
        color: #fff;
        border-color: #434343;
      }

      .ant-table-tbody > tr {
        background: #1f1f1f;
        border-color: #434343;

        &:hover {
          background: #262626;
        }

        td {
          color: #fff;
          border-color: #434343;
        }
      }
    }

    .ant-tag {
      &.ant-tag-blue {
        background: #111b26;
        border-color: #1890ff;
        color: #40a9ff;
      }
    }

    .ant-alert {
      &.ant-alert-warning {
        background: #2a2111;
        border-color: #faad14;
        color: #fff;
      }
    }
  }

  // 高对比度模式
  @media (prefers-contrast: high) {
    .ant-modal-content {
      border: 2px solid #000;
    }

    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        border: 1px solid #000;
      }
    }

    .ant-tag {
      border: 1px solid #000;
    }

    .ant-btn {
      border: 2px solid #000;

      &.ant-btn-primary {
        background: #0066cc;
        border-color: #0066cc;
      }

      &.ant-btn-danger {
        background: #cc0000;
        border-color: #cc0000;
      }
    }
  }

  // 减少动画模式
  @media (prefers-reduced-motion: reduce) {
    .ant-table-tbody > tr,
    .ant-btn,
    .ant-switch {
      transition: none;
    }
  }
}
