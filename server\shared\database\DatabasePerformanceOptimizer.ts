/**
 * 数据库性能优化器
 * 实现数据库连接池优化、查询性能监控和索引策略优化
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Pool, PoolClient } from 'pg';
import { EventEmitter } from 'events';

/**
 * 查询性能指标
 */
export interface QueryPerformanceMetrics {
  /** 查询ID */
  queryId: string;
  /** SQL语句 */
  sql: string;
  /** 参数 */
  params: any[];
  /** 执行时间（毫秒） */
  executionTime: number;
  /** 返回行数 */
  rowCount: number;
  /** 是否使用索引 */
  usedIndex: boolean;
  /** 扫描行数 */
  scannedRows: number;
  /** 缓存命中 */
  cacheHit: boolean;
  /** 执行时间戳 */
  timestamp: number;
  /** 执行计划 */
  executionPlan?: any;
}

/**
 * 连接池性能指标
 */
export interface PoolPerformanceMetrics {
  /** 连接池名称 */
  poolName: string;
  /** 总连接数 */
  totalConnections: number;
  /** 活跃连接数 */
  activeConnections: number;
  /** 空闲连接数 */
  idleConnections: number;
  /** 等待连接数 */
  waitingConnections: number;
  /** 平均等待时间 */
  averageWaitTime: number;
  /** 连接获取成功率 */
  connectionSuccessRate: number;
  /** 连接超时次数 */
  connectionTimeouts: number;
  /** 最后更新时间 */
  lastUpdated: number;
}

/**
 * 索引建议
 */
export interface IndexSuggestion {
  /** 建议ID */
  id: string;
  /** 表名 */
  tableName: string;
  /** 建议的索引列 */
  columns: string[];
  /** 索引类型 */
  indexType: 'btree' | 'hash' | 'gin' | 'gist';
  /** 建议原因 */
  reason: string;
  /** 预期性能提升 */
  expectedImprovement: number;
  /** 影响的查询数量 */
  affectedQueries: number;
  /** 优先级 */
  priority: 'high' | 'medium' | 'low';
  /** 创建时间 */
  createdAt: number;
}

/**
 * 慢查询信息
 */
export interface SlowQuery {
  /** 查询ID */
  id: string;
  /** SQL语句 */
  sql: string;
  /** 平均执行时间 */
  averageTime: number;
  /** 最大执行时间 */
  maxTime: number;
  /** 执行次数 */
  executionCount: number;
  /** 最后执行时间 */
  lastExecution: number;
  /** 优化建议 */
  suggestions: string[];
}

/**
 * 数据库性能优化器
 */
@Injectable()
export class DatabasePerformanceOptimizer extends EventEmitter {
  private readonly logger = new Logger(DatabasePerformanceOptimizer.name);

  /** 查询性能历史 */
  private queryMetricsHistory: QueryPerformanceMetrics[] = [];
  /** 连接池性能指标 */
  private poolMetrics: Map<string, PoolPerformanceMetrics> = new Map();
  /** 慢查询记录 */
  private slowQueries: Map<string, SlowQuery> = new Map();
  /** 索引建议 */
  private indexSuggestions: IndexSuggestion[] = [];

  /** 性能阈值配置 */
  private readonly slowQueryThreshold: number;
  private readonly connectionWaitThreshold: number;
  private readonly maxMetricsHistory: number;

  /** 监控定时器 */
  private monitoringTimer?: NodeJS.Timeout;
  private analysisTimer?: NodeJS.Timeout;

  constructor(private readonly configService: ConfigService) {
    super();

    this.slowQueryThreshold = this.configService.get<number>('DB_SLOW_QUERY_THRESHOLD', 1000);
    this.connectionWaitThreshold = this.configService.get<number>('DB_CONNECTION_WAIT_THRESHOLD', 100);
    this.maxMetricsHistory = this.configService.get<number>('DB_MAX_METRICS_HISTORY', 10000);

    this.startMonitoring();
  }

  /**
   * 开始性能监控
   */
  private startMonitoring(): void {
    // 每30秒收集一次性能指标
    this.monitoringTimer = setInterval(() => {
      this.collectPerformanceMetrics();
    }, 30000);

    // 每5分钟进行一次性能分析
    this.analysisTimer = setInterval(() => {
      this.analyzePerformance();
    }, 300000);

    this.logger.log('数据库性能监控已启动');
  }

  /**
   * 记录查询性能
   */
  public recordQueryPerformance(metrics: QueryPerformanceMetrics): void {
    // 添加到历史记录
    this.queryMetricsHistory.push(metrics);

    // 限制历史记录大小
    if (this.queryMetricsHistory.length > this.maxMetricsHistory) {
      this.queryMetricsHistory.shift();
    }

    // 检查是否为慢查询
    if (metrics.executionTime > this.slowQueryThreshold) {
      this.recordSlowQuery(metrics);
    }

    // 发出性能事件
    this.emit('queryPerformance', metrics);
  }

  /**
   * 记录慢查询
   */
  private recordSlowQuery(metrics: QueryPerformanceMetrics): void {
    const queryHash = this.generateQueryHash(metrics.sql);
    const existing = this.slowQueries.get(queryHash);

    if (existing) {
      // 更新现有慢查询记录
      existing.executionCount++;
      existing.averageTime = (existing.averageTime * (existing.executionCount - 1) + metrics.executionTime) / existing.executionCount;
      existing.maxTime = Math.max(existing.maxTime, metrics.executionTime);
      existing.lastExecution = metrics.timestamp;
    } else {
      // 创建新的慢查询记录
      const slowQuery: SlowQuery = {
        id: queryHash,
        sql: metrics.sql,
        averageTime: metrics.executionTime,
        maxTime: metrics.executionTime,
        executionCount: 1,
        lastExecution: metrics.timestamp,
        suggestions: this.generateQueryOptimizationSuggestions(metrics)
      };

      this.slowQueries.set(queryHash, slowQuery);
    }

    this.emit('slowQuery', this.slowQueries.get(queryHash));
  }

  /**
   * 生成查询优化建议
   */
  private generateQueryOptimizationSuggestions(metrics: QueryPerformanceMetrics): string[] {
    const suggestions: string[] = [];

    // 基于执行计划分析
    if (metrics.executionPlan) {
      if (metrics.executionPlan.includes('Seq Scan')) {
        suggestions.push('考虑添加索引以避免全表扫描');
      }
      if (metrics.executionPlan.includes('Sort')) {
        suggestions.push('考虑添加索引以避免排序操作');
      }
      if (metrics.executionPlan.includes('Hash Join')) {
        suggestions.push('考虑优化JOIN条件或添加索引');
      }
    }

    // 基于扫描行数分析
    if (metrics.scannedRows > metrics.rowCount * 10) {
      suggestions.push('查询扫描了过多行，考虑优化WHERE条件');
    }

    // 基于执行时间分析
    if (metrics.executionTime > 5000) {
      suggestions.push('查询执行时间过长，考虑分页或优化查询逻辑');
    }

    return suggestions;
  }

  /**
   * 更新连接池性能指标
   */
  public updatePoolMetrics(poolName: string, pool: Pool): void {
    const metrics: PoolPerformanceMetrics = {
      poolName,
      totalConnections: pool.totalCount,
      activeConnections: pool.totalCount - pool.idleCount,
      idleConnections: pool.idleCount,
      waitingConnections: pool.waitingCount,
      averageWaitTime: 0, // 需要从连接池获取
      connectionSuccessRate: 0, // 需要计算
      connectionTimeouts: 0, // 需要从连接池获取
      lastUpdated: Date.now()
    };

    this.poolMetrics.set(poolName, metrics);
    this.emit('poolMetrics', metrics);
  }

  /**
   * 收集性能指标
   */
  private collectPerformanceMetrics(): void {
    // 分析查询性能趋势
    this.analyzeQueryTrends();

    // 检查连接池健康状况
    this.checkPoolHealth();

    // 生成索引建议
    this.generateIndexSuggestions();
  }

  /**
   * 分析查询性能趋势
   */
  private analyzeQueryTrends(): void {
    const recentQueries = this.queryMetricsHistory.filter(
      q => Date.now() - q.timestamp < 300000 // 最近5分钟
    );

    if (recentQueries.length === 0) return;

    const averageTime = recentQueries.reduce((sum, q) => sum + q.executionTime, 0) / recentQueries.length;
    const slowQueryCount = recentQueries.filter(q => q.executionTime > this.slowQueryThreshold).length;
    const cacheHitRate = recentQueries.filter(q => q.cacheHit).length / recentQueries.length;

    this.emit('performanceTrend', {
      averageTime,
      slowQueryCount,
      cacheHitRate,
      totalQueries: recentQueries.length,
      timestamp: Date.now()
    });
  }

  /**
   * 检查连接池健康状况
   */
  private checkPoolHealth(): void {
    for (const [poolName, metrics] of this.poolMetrics.entries()) {
      const utilizationRate = metrics.activeConnections / metrics.totalConnections;
      const hasWaitingConnections = metrics.waitingConnections > 0;

      if (utilizationRate > 0.8) {
        this.emit('poolAlert', {
          type: 'high_utilization',
          poolName,
          utilizationRate,
          message: `连接池 ${poolName} 使用率过高: ${(utilizationRate * 100).toFixed(1)}%`
        });
      }

      if (hasWaitingConnections) {
        this.emit('poolAlert', {
          type: 'connection_wait',
          poolName,
          waitingCount: metrics.waitingConnections,
          message: `连接池 ${poolName} 有 ${metrics.waitingConnections} 个等待连接`
        });
      }
    }
  }

  /**
   * 生成索引建议
   */
  private generateIndexSuggestions(): void {
    // 分析慢查询，生成索引建议
    for (const slowQuery of this.slowQueries.values()) {
      const suggestions = this.analyzeQueryForIndexes(slowQuery);
      this.indexSuggestions.push(...suggestions);
    }

    // 去重和排序
    this.indexSuggestions = this.deduplicateIndexSuggestions(this.indexSuggestions);
    this.indexSuggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    // 限制建议数量
    if (this.indexSuggestions.length > 50) {
      this.indexSuggestions = this.indexSuggestions.slice(0, 50);
    }
  }

  /**
   * 分析查询以生成索引建议
   */
  private analyzeQueryForIndexes(slowQuery: SlowQuery): IndexSuggestion[] {
    const suggestions: IndexSuggestion[] = [];
    
    // 简化的SQL解析（实际应该使用专业的SQL解析器）
    const sql = slowQuery.sql.toLowerCase();
    
    // 查找WHERE子句中的列
    const whereMatch = sql.match(/where\s+(.+?)(?:\s+order\s+by|\s+group\s+by|\s+limit|$)/);
    if (whereMatch) {
      const whereClause = whereMatch[1];
      const columns = this.extractColumnsFromWhere(whereClause);
      
      if (columns.length > 0) {
        suggestions.push({
          id: `idx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          tableName: this.extractTableName(sql),
          columns,
          indexType: 'btree',
          reason: '优化WHERE条件查询',
          expectedImprovement: this.calculateExpectedImprovement(slowQuery),
          affectedQueries: 1,
          priority: slowQuery.averageTime > 5000 ? 'high' : 'medium',
          createdAt: Date.now()
        });
      }
    }

    return suggestions;
  }

  /**
   * 从WHERE子句提取列名
   */
  private extractColumnsFromWhere(whereClause: string): string[] {
    // 简化实现，实际应该更复杂
    const columns: string[] = [];
    const columnPattern = /(\w+)\s*[=<>]/g;
    let match;
    
    while ((match = columnPattern.exec(whereClause)) !== null) {
      columns.push(match[1]);
    }
    
    return [...new Set(columns)]; // 去重
  }

  /**
   * 提取表名
   */
  private extractTableName(sql: string): string {
    const fromMatch = sql.match(/from\s+(\w+)/);
    return fromMatch ? fromMatch[1] : 'unknown';
  }

  /**
   * 计算预期性能提升
   */
  private calculateExpectedImprovement(slowQuery: SlowQuery): number {
    // 基于执行时间和执行次数估算
    const baseImprovement = Math.min(slowQuery.averageTime / 1000 * 0.7, 0.9);
    const frequencyBonus = Math.min(slowQuery.executionCount / 100 * 0.1, 0.1);
    
    return Math.min(baseImprovement + frequencyBonus, 0.95);
  }

  /**
   * 去重索引建议
   */
  private deduplicateIndexSuggestions(suggestions: IndexSuggestion[]): IndexSuggestion[] {
    const seen = new Set<string>();
    return suggestions.filter(suggestion => {
      const key = `${suggestion.tableName}_${suggestion.columns.join('_')}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 生成查询哈希
   */
  private generateQueryHash(sql: string): string {
    // 标准化SQL（移除参数、空格等）
    const normalized = sql
      .replace(/\$\d+/g, '?') // 替换参数占位符
      .replace(/\s+/g, ' ') // 标准化空格
      .trim()
      .toLowerCase();

    return require('crypto')
      .createHash('md5')
      .update(normalized)
      .digest('hex');
  }

  /**
   * 执行性能分析
   */
  private analyzePerformance(): void {
    this.logger.log('执行数据库性能分析...');

    // 生成性能报告
    const report = this.generatePerformanceReport();
    this.emit('performanceReport', report);

    // 清理过期数据
    this.cleanupOldData();
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(): any {
    const now = Date.now();
    const oneHourAgo = now - 3600000;

    const recentQueries = this.queryMetricsHistory.filter(q => q.timestamp > oneHourAgo);
    const recentSlowQueries = Array.from(this.slowQueries.values())
      .filter(q => q.lastExecution > oneHourAgo);

    return {
      timestamp: now,
      summary: {
        totalQueries: recentQueries.length,
        slowQueries: recentSlowQueries.length,
        averageExecutionTime: recentQueries.length > 0 
          ? recentQueries.reduce((sum, q) => sum + q.executionTime, 0) / recentQueries.length 
          : 0,
        cacheHitRate: recentQueries.length > 0 
          ? recentQueries.filter(q => q.cacheHit).length / recentQueries.length 
          : 0
      },
      slowQueries: recentSlowQueries.slice(0, 10), // 前10个慢查询
      indexSuggestions: this.indexSuggestions.slice(0, 5), // 前5个索引建议
      poolMetrics: Object.fromEntries(this.poolMetrics)
    };
  }

  /**
   * 清理过期数据
   */
  private cleanupOldData(): void {
    const oneWeekAgo = Date.now() - 7 * 24 * 3600000;

    // 清理过期的查询指标
    this.queryMetricsHistory = this.queryMetricsHistory.filter(
      q => q.timestamp > oneWeekAgo
    );

    // 清理过期的慢查询
    for (const [key, slowQuery] of this.slowQueries.entries()) {
      if (slowQuery.lastExecution < oneWeekAgo) {
        this.slowQueries.delete(key);
      }
    }

    // 清理过期的索引建议
    this.indexSuggestions = this.indexSuggestions.filter(
      s => s.createdAt > oneWeekAgo
    );
  }

  /**
   * 获取慢查询列表
   */
  public getSlowQueries(): SlowQuery[] {
    return Array.from(this.slowQueries.values())
      .sort((a, b) => b.averageTime - a.averageTime);
  }

  /**
   * 获取索引建议
   */
  public getIndexSuggestions(): IndexSuggestion[] {
    return [...this.indexSuggestions];
  }

  /**
   * 获取连接池状态
   */
  public getPoolMetrics(): PoolPerformanceMetrics[] {
    return Array.from(this.poolMetrics.values());
  }

  /**
   * 销毁优化器
   */
  public destroy(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    if (this.analysisTimer) {
      clearInterval(this.analysisTimer);
    }

    this.removeAllListeners();
    this.logger.log('数据库性能优化器已销毁');
  }
}
