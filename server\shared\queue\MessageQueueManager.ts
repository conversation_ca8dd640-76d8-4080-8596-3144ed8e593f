/**
 * 消息队列管理器
 * 实现消息队列监控、消息重试机制和性能优化
 */
import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';
import Bull, { Queue, Job, JobOptions } from 'bull';
import { Redis } from 'ioredis';

/**
 * 消息优先级枚举
 */
export enum MessagePriority {
  LOW = 1,
  NORMAL = 5,
  HIGH = 10,
  CRITICAL = 20
}

/**
 * 队列状态枚举
 */
export enum QueueStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  ERROR = 'error'
}

/**
 * 消息处理结果
 */
export interface MessageProcessResult {
  /** 是否成功 */
  success: boolean;
  /** 结果数据 */
  data?: any;
  /** 错误信息 */
  error?: string;
  /** 处理时间（毫秒） */
  processingTime: number;
  /** 重试次数 */
  retryCount: number;
}

/**
 * 队列配置
 */
export interface QueueConfig {
  /** 队列名称 */
  name: string;
  /** 并发处理数 */
  concurrency: number;
  /** 默认任务选项 */
  defaultJobOptions: JobOptions;
  /** 是否启用 */
  enabled: boolean;
  /** 处理器函数 */
  processor: (job: Job) => Promise<any>;
  /** 重试配置 */
  retryConfig: {
    attempts: number;
    backoff: 'fixed' | 'exponential';
    delay: number;
  };
}

/**
 * 队列统计信息
 */
export interface QueueStats {
  /** 队列名称 */
  name: string;
  /** 状态 */
  status: QueueStatus;
  /** 等待中的任务数 */
  waiting: number;
  /** 活跃任务数 */
  active: number;
  /** 已完成任务数 */
  completed: number;
  /** 失败任务数 */
  failed: number;
  /** 延迟任务数 */
  delayed: number;
  /** 暂停任务数 */
  paused: number;
  /** 处理速率（任务/秒） */
  processingRate: number;
  /** 平均处理时间 */
  averageProcessingTime: number;
  /** 错误率 */
  errorRate: number;
  /** 最后更新时间 */
  lastUpdated: number;
}

/**
 * 消息队列管理器
 */
@Injectable()
export class MessageQueueManager extends EventEmitter implements OnModuleDestroy {
  private readonly logger = new Logger(MessageQueueManager.name);

  /** 队列映射 */
  private queues: Map<string, Queue> = new Map();
  /** 队列配置映射 */
  private queueConfigs: Map<string, QueueConfig> = new Map();
  /** 队列统计映射 */
  private queueStats: Map<string, QueueStats> = new Map();

  /** Redis连接 */
  private redis: Redis;

  /** 监控定时器 */
  private monitoringTimer?: NodeJS.Timeout;
  /** 清理定时器 */
  private cleanupTimer?: NodeJS.Timeout;

  /** 全局统计 */
  private globalStats = {
    totalQueues: 0,
    totalJobs: 0,
    totalProcessed: 0,
    totalFailed: 0,
    averageProcessingTime: 0,
    systemLoad: 0
  };

  constructor(private readonly configService: ConfigService) {
    super();
    this.initializeRedis();
    this.startMonitoring();
  }

  /**
   * 初始化Redis连接
   */
  private initializeRedis(): void {
    this.redis = new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_QUEUE_DB', 1),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis连接错误:', error);
    });

    this.redis.on('connect', () => {
      this.logger.log('Redis连接已建立');
    });
  }

  /**
   * 创建队列
   */
  public createQueue(config: QueueConfig): Queue {
    if (this.queues.has(config.name)) {
      throw new Error(`队列 ${config.name} 已存在`);
    }

    const queue = new Bull(config.name, {
      redis: {
        host: this.configService.get<string>('REDIS_HOST', 'localhost'),
        port: this.configService.get<number>('REDIS_PORT', 6379),
        password: this.configService.get<string>('REDIS_PASSWORD'),
        db: this.configService.get<number>('REDIS_QUEUE_DB', 1)
      },
      defaultJobOptions: config.defaultJobOptions
    });

    // 设置处理器
    queue.process(config.concurrency, async (job: Job) => {
      const startTime = Date.now();
      
      try {
        const result = await config.processor(job);
        const processingTime = Date.now() - startTime;
        
        this.updateJobStats(config.name, true, processingTime, job.attemptsMade);
        
        return result;
      } catch (error) {
        const processingTime = Date.now() - startTime;
        
        this.updateJobStats(config.name, false, processingTime, job.attemptsMade);
        
        throw error;
      }
    });

    // 设置事件监听器
    this.setupQueueEventListeners(queue, config.name);

    // 存储队列和配置
    this.queues.set(config.name, queue);
    this.queueConfigs.set(config.name, config);

    // 初始化统计
    this.initializeQueueStats(config.name);

    this.logger.log(`队列 ${config.name} 创建成功`);
    this.emit('queueCreated', config.name);

    return queue;
  }

  /**
   * 添加消息到队列
   */
  public async addMessage(
    queueName: string,
    data: any,
    options?: {
      priority?: MessagePriority;
      delay?: number;
      attempts?: number;
      backoff?: 'fixed' | 'exponential';
      removeOnComplete?: number;
      removeOnFail?: number;
    }
  ): Promise<Job> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`队列 ${queueName} 不存在`);
    }

    const config = this.queueConfigs.get(queueName);
    const jobOptions: JobOptions = {
      priority: options?.priority || MessagePriority.NORMAL,
      delay: options?.delay || 0,
      attempts: options?.attempts || config?.retryConfig.attempts || 3,
      backoff: options?.backoff || config?.retryConfig.backoff || 'exponential',
      removeOnComplete: options?.removeOnComplete || 100,
      removeOnFail: options?.removeOnFail || 50
    };

    const job = await queue.add(data, jobOptions);
    
    this.logger.debug(`消息已添加到队列 ${queueName}, Job ID: ${job.id}`);
    this.emit('messageAdded', { queueName, jobId: job.id, data });

    return job;
  }

  /**
   * 批量添加消息
   */
  public async addBulkMessages(
    queueName: string,
    messages: Array<{ data: any; options?: JobOptions }>
  ): Promise<Job[]> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`队列 ${queueName} 不存在`);
    }

    const jobs = await queue.addBulk(messages);
    
    this.logger.log(`批量添加 ${messages.length} 条消息到队列 ${queueName}`);
    this.emit('bulkMessagesAdded', { queueName, count: messages.length });

    return jobs;
  }

  /**
   * 暂停队列
   */
  public async pauseQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`队列 ${queueName} 不存在`);
    }

    await queue.pause();
    
    const stats = this.queueStats.get(queueName);
    if (stats) {
      stats.status = QueueStatus.PAUSED;
    }

    this.logger.log(`队列 ${queueName} 已暂停`);
    this.emit('queuePaused', queueName);
  }

  /**
   * 恢复队列
   */
  public async resumeQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`队列 ${queueName} 不存在`);
    }

    await queue.resume();
    
    const stats = this.queueStats.get(queueName);
    if (stats) {
      stats.status = QueueStatus.ACTIVE;
    }

    this.logger.log(`队列 ${queueName} 已恢复`);
    this.emit('queueResumed', queueName);
  }

  /**
   * 清空队列
   */
  public async clearQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`队列 ${queueName} 不存在`);
    }

    await queue.empty();
    
    this.logger.log(`队列 ${queueName} 已清空`);
    this.emit('queueCleared', queueName);
  }

  /**
   * 获取队列统计
   */
  public async getQueueStats(queueName: string): Promise<QueueStats | null> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      return null;
    }

    const waiting = await queue.getWaiting();
    const active = await queue.getActive();
    const completed = await queue.getCompleted();
    const failed = await queue.getFailed();
    const delayed = await queue.getDelayed();

    const stats = this.queueStats.get(queueName);
    if (stats) {
      stats.waiting = waiting.length;
      stats.active = active.length;
      stats.completed = completed.length;
      stats.failed = failed.length;
      stats.delayed = delayed.length;
      stats.lastUpdated = Date.now();
    }

    return stats || null;
  }

  /**
   * 获取所有队列统计
   */
  public async getAllQueueStats(): Promise<QueueStats[]> {
    const allStats: QueueStats[] = [];
    
    for (const queueName of this.queues.keys()) {
      const stats = await this.getQueueStats(queueName);
      if (stats) {
        allStats.push(stats);
      }
    }

    return allStats;
  }

  /**
   * 重试失败的任务
   */
  public async retryFailedJobs(queueName: string, limit?: number): Promise<number> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`队列 ${queueName} 不存在`);
    }

    const failedJobs = await queue.getFailed(0, limit || -1);
    let retriedCount = 0;

    for (const job of failedJobs) {
      try {
        await job.retry();
        retriedCount++;
      } catch (error) {
        this.logger.error(`重试任务失败 ${job.id}:`, error);
      }
    }

    this.logger.log(`队列 ${queueName} 重试了 ${retriedCount} 个失败任务`);
    this.emit('jobsRetried', { queueName, count: retriedCount });

    return retriedCount;
  }

  /**
   * 设置队列事件监听器
   */
  private setupQueueEventListeners(queue: Queue, queueName: string): void {
    queue.on('completed', (job: Job, result: any) => {
      this.logger.debug(`任务完成: ${queueName}/${job.id}`);
      this.emit('jobCompleted', { queueName, jobId: job.id, result });
    });

    queue.on('failed', (job: Job, error: Error) => {
      this.logger.error(`任务失败: ${queueName}/${job.id}`, error);
      this.emit('jobFailed', { queueName, jobId: job.id, error: error.message });
    });

    queue.on('stalled', (job: Job) => {
      this.logger.warn(`任务停滞: ${queueName}/${job.id}`);
      this.emit('jobStalled', { queueName, jobId: job.id });
    });

    queue.on('progress', (job: Job, progress: number) => {
      this.emit('jobProgress', { queueName, jobId: job.id, progress });
    });

    queue.on('error', (error: Error) => {
      this.logger.error(`队列错误 ${queueName}:`, error);
      
      const stats = this.queueStats.get(queueName);
      if (stats) {
        stats.status = QueueStatus.ERROR;
      }
      
      this.emit('queueError', { queueName, error: error.message });
    });
  }

  /**
   * 初始化队列统计
   */
  private initializeQueueStats(queueName: string): void {
    const stats: QueueStats = {
      name: queueName,
      status: QueueStatus.ACTIVE,
      waiting: 0,
      active: 0,
      completed: 0,
      failed: 0,
      delayed: 0,
      paused: 0,
      processingRate: 0,
      averageProcessingTime: 0,
      errorRate: 0,
      lastUpdated: Date.now()
    };

    this.queueStats.set(queueName, stats);
  }

  /**
   * 更新任务统计
   */
  private updateJobStats(
    queueName: string,
    success: boolean,
    processingTime: number,
    retryCount: number
  ): void {
    const stats = this.queueStats.get(queueName);
    if (!stats) return;

    // 更新处理时间
    if (stats.averageProcessingTime === 0) {
      stats.averageProcessingTime = processingTime;
    } else {
      stats.averageProcessingTime = (stats.averageProcessingTime + processingTime) / 2;
    }

    // 更新错误率
    const totalJobs = stats.completed + stats.failed + 1;
    if (success) {
      stats.completed++;
    } else {
      stats.failed++;
    }
    stats.errorRate = stats.failed / totalJobs;

    // 更新全局统计
    this.globalStats.totalProcessed++;
    if (!success) {
      this.globalStats.totalFailed++;
    }
    this.globalStats.averageProcessingTime = 
      (this.globalStats.averageProcessingTime + processingTime) / 2;
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    // 每30秒更新统计
    this.monitoringTimer = setInterval(async () => {
      await this.updateAllStats();
    }, 30000);

    // 每小时清理完成的任务
    this.cleanupTimer = setInterval(async () => {
      await this.cleanupCompletedJobs();
    }, 3600000);

    this.logger.log('消息队列监控已启动');
  }

  /**
   * 更新所有统计
   */
  private async updateAllStats(): Promise<void> {
    for (const queueName of this.queues.keys()) {
      await this.getQueueStats(queueName);
    }

    this.globalStats.totalQueues = this.queues.size;
    this.emit('statsUpdated', {
      queueStats: Array.from(this.queueStats.values()),
      globalStats: this.globalStats
    });
  }

  /**
   * 清理已完成的任务
   */
  private async cleanupCompletedJobs(): Promise<void> {
    for (const [queueName, queue] of this.queues.entries()) {
      try {
        await queue.clean(24 * 3600 * 1000, 'completed'); // 清理24小时前的已完成任务
        await queue.clean(7 * 24 * 3600 * 1000, 'failed'); // 清理7天前的失败任务
        
        this.logger.debug(`队列 ${queueName} 清理完成`);
      } catch (error) {
        this.logger.error(`队列 ${queueName} 清理失败:`, error);
      }
    }
  }

  /**
   * 获取全局统计
   */
  public getGlobalStats(): any {
    return { ...this.globalStats };
  }

  /**
   * 获取队列列表
   */
  public getQueueNames(): string[] {
    return Array.from(this.queues.keys());
  }

  /**
   * 模块销毁时清理资源
   */
  public async onModuleDestroy(): Promise<void> {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    // 关闭所有队列
    const closePromises = Array.from(this.queues.values()).map(queue => queue.close());
    await Promise.allSettled(closePromises);

    // 关闭Redis连接
    await this.redis.quit();

    this.removeAllListeners();
    this.logger.log('消息队列管理器已销毁');
  }
}
