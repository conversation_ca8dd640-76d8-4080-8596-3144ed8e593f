/**
 * 查询性能监控服务
 * 实现实时查询性能监控、慢查询分析和性能报告生成
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';
import { PoolClient } from 'pg';

/**
 * 查询执行信息
 */
export interface QueryExecution {
  /** 查询ID */
  id: string;
  /** SQL语句 */
  sql: string;
  /** 参数 */
  params: any[];
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime?: number;
  /** 执行时间（毫秒） */
  duration?: number;
  /** 返回行数 */
  rowCount?: number;
  /** 是否成功 */
  success?: boolean;
  /** 错误信息 */
  error?: string;
  /** 执行计划 */
  executionPlan?: any;
  /** 连接池名称 */
  poolName?: string;
  /** 客户端信息 */
  clientInfo?: any;
}

/**
 * 查询统计信息
 */
export interface QueryStatistics {
  /** SQL模式（标准化后的SQL） */
  sqlPattern: string;
  /** 执行次数 */
  executionCount: number;
  /** 总执行时间 */
  totalDuration: number;
  /** 平均执行时间 */
  averageDuration: number;
  /** 最小执行时间 */
  minDuration: number;
  /** 最大执行时间 */
  maxDuration: number;
  /** 成功次数 */
  successCount: number;
  /** 失败次数 */
  errorCount: number;
  /** 最后执行时间 */
  lastExecution: number;
  /** 第一次执行时间 */
  firstExecution: number;
}

/**
 * 性能警报
 */
export interface PerformanceAlert {
  /** 警报ID */
  id: string;
  /** 警报类型 */
  type: 'slow_query' | 'high_error_rate' | 'connection_timeout' | 'resource_exhaustion';
  /** 警报级别 */
  level: 'info' | 'warning' | 'error' | 'critical';
  /** 警报消息 */
  message: string;
  /** 相关查询 */
  queryExecution?: QueryExecution;
  /** 统计信息 */
  statistics?: any;
  /** 创建时间 */
  timestamp: number;
  /** 是否已处理 */
  acknowledged: boolean;
}

/**
 * 查询性能监控器
 */
@Injectable()
export class QueryPerformanceMonitor extends EventEmitter {
  private readonly logger = new Logger(QueryPerformanceMonitor.name);

  /** 活跃查询映射 */
  private activeQueries: Map<string, QueryExecution> = new Map();
  /** 查询历史记录 */
  private queryHistory: QueryExecution[] = [];
  /** 查询统计 */
  private queryStatistics: Map<string, QueryStatistics> = new Map();
  /** 性能警报 */
  private performanceAlerts: PerformanceAlert[] = [];

  /** 配置参数 */
  private readonly slowQueryThreshold: number;
  private readonly maxHistorySize: number;
  private readonly maxStatisticsSize: number;
  private readonly errorRateThreshold: number;
  private readonly monitoringEnabled: boolean;

  /** 监控定时器 */
  private monitoringTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(private readonly configService: ConfigService) {
    super();

    this.slowQueryThreshold = this.configService.get<number>('DB_SLOW_QUERY_THRESHOLD', 1000);
    this.maxHistorySize = this.configService.get<number>('DB_MAX_QUERY_HISTORY', 10000);
    this.maxStatisticsSize = this.configService.get<number>('DB_MAX_QUERY_STATISTICS', 1000);
    this.errorRateThreshold = this.configService.get<number>('DB_ERROR_RATE_THRESHOLD', 0.1);
    this.monitoringEnabled = this.configService.get<boolean>('DB_MONITORING_ENABLED', true);

    if (this.monitoringEnabled) {
      this.startMonitoring();
    }
  }

  /**
   * 开始监控查询
   */
  public startQueryMonitoring(
    sql: string,
    params: any[] = [],
    poolName?: string,
    clientInfo?: any
  ): string {
    if (!this.monitoringEnabled) {
      return '';
    }

    const queryId = this.generateQueryId();
    const queryExecution: QueryExecution = {
      id: queryId,
      sql,
      params,
      startTime: Date.now(),
      poolName,
      clientInfo
    };

    this.activeQueries.set(queryId, queryExecution);
    this.emit('queryStarted', queryExecution);

    return queryId;
  }

  /**
   * 结束查询监控
   */
  public endQueryMonitoring(
    queryId: string,
    result?: {
      rowCount?: number;
      success?: boolean;
      error?: string;
      executionPlan?: any;
    }
  ): void {
    if (!this.monitoringEnabled || !queryId) {
      return;
    }

    const queryExecution = this.activeQueries.get(queryId);
    if (!queryExecution) {
      return;
    }

    // 完善查询执行信息
    queryExecution.endTime = Date.now();
    queryExecution.duration = queryExecution.endTime - queryExecution.startTime;
    queryExecution.rowCount = result?.rowCount;
    queryExecution.success = result?.success !== false;
    queryExecution.error = result?.error;
    queryExecution.executionPlan = result?.executionPlan;

    // 从活跃查询中移除
    this.activeQueries.delete(queryId);

    // 添加到历史记录
    this.addToHistory(queryExecution);

    // 更新统计信息
    this.updateStatistics(queryExecution);

    // 检查性能问题
    this.checkPerformanceIssues(queryExecution);

    this.emit('queryCompleted', queryExecution);
  }

  /**
   * 包装数据库客户端以自动监控
   */
  public wrapClient(client: PoolClient, poolName?: string): PoolClient {
    const originalQuery = client.query.bind(client);

    client.query = async (...args: any[]) => {
      let sql: string;
      let params: any[] = [];

      // 解析参数
      if (typeof args[0] === 'string') {
        sql = args[0];
        params = args[1] || [];
      } else if (args[0] && typeof args[0] === 'object') {
        sql = args[0].text || args[0].query || '';
        params = args[0].values || [];
      } else {
        sql = '';
      }

      // 开始监控
      const queryId = this.startQueryMonitoring(sql, params, poolName);

      try {
        const result = await originalQuery(...args);
        
        // 结束监控
        this.endQueryMonitoring(queryId, {
          rowCount: result.rowCount,
          success: true
        });

        return result;
      } catch (error) {
        // 结束监控（错误情况）
        this.endQueryMonitoring(queryId, {
          success: false,
          error: error.message
        });

        throw error;
      }
    };

    return client;
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(queryExecution: QueryExecution): void {
    this.queryHistory.push(queryExecution);

    // 限制历史记录大小
    if (this.queryHistory.length > this.maxHistorySize) {
      this.queryHistory.shift();
    }
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(queryExecution: QueryExecution): void {
    const sqlPattern = this.normalizeSql(queryExecution.sql);
    const existing = this.queryStatistics.get(sqlPattern);

    if (existing) {
      // 更新现有统计
      existing.executionCount++;
      existing.totalDuration += queryExecution.duration || 0;
      existing.averageDuration = existing.totalDuration / existing.executionCount;
      existing.minDuration = Math.min(existing.minDuration, queryExecution.duration || 0);
      existing.maxDuration = Math.max(existing.maxDuration, queryExecution.duration || 0);
      existing.lastExecution = queryExecution.endTime || 0;

      if (queryExecution.success) {
        existing.successCount++;
      } else {
        existing.errorCount++;
      }
    } else {
      // 创建新统计
      const statistics: QueryStatistics = {
        sqlPattern,
        executionCount: 1,
        totalDuration: queryExecution.duration || 0,
        averageDuration: queryExecution.duration || 0,
        minDuration: queryExecution.duration || 0,
        maxDuration: queryExecution.duration || 0,
        successCount: queryExecution.success ? 1 : 0,
        errorCount: queryExecution.success ? 0 : 1,
        lastExecution: queryExecution.endTime || 0,
        firstExecution: queryExecution.startTime
      };

      this.queryStatistics.set(sqlPattern, statistics);

      // 限制统计数量
      if (this.queryStatistics.size > this.maxStatisticsSize) {
        this.cleanupOldStatistics();
      }
    }
  }

  /**
   * 检查性能问题
   */
  private checkPerformanceIssues(queryExecution: QueryExecution): void {
    // 检查慢查询
    if (queryExecution.duration && queryExecution.duration > this.slowQueryThreshold) {
      this.createAlert({
        type: 'slow_query',
        level: queryExecution.duration > this.slowQueryThreshold * 5 ? 'critical' : 'warning',
        message: `检测到慢查询，执行时间: ${queryExecution.duration}ms`,
        queryExecution
      });
    }

    // 检查错误率
    const sqlPattern = this.normalizeSql(queryExecution.sql);
    const statistics = this.queryStatistics.get(sqlPattern);
    if (statistics && statistics.executionCount >= 10) {
      const errorRate = statistics.errorCount / statistics.executionCount;
      if (errorRate > this.errorRateThreshold) {
        this.createAlert({
          type: 'high_error_rate',
          level: 'error',
          message: `查询错误率过高: ${(errorRate * 100).toFixed(1)}%`,
          statistics
        });
      }
    }
  }

  /**
   * 创建性能警报
   */
  private createAlert(alertData: Partial<PerformanceAlert>): void {
    const alert: PerformanceAlert = {
      id: this.generateAlertId(),
      type: alertData.type!,
      level: alertData.level!,
      message: alertData.message!,
      queryExecution: alertData.queryExecution,
      statistics: alertData.statistics,
      timestamp: Date.now(),
      acknowledged: false
    };

    this.performanceAlerts.push(alert);
    this.emit('performanceAlert', alert);

    // 限制警报数量
    if (this.performanceAlerts.length > 1000) {
      this.performanceAlerts = this.performanceAlerts.slice(-500);
    }
  }

  /**
   * 标准化SQL语句
   */
  private normalizeSql(sql: string): string {
    return sql
      .replace(/\$\d+/g, '?') // 替换参数占位符
      .replace(/\s+/g, ' ') // 标准化空格
      .replace(/\b\d+\b/g, '?') // 替换数字字面量
      .replace(/'[^']*'/g, '?') // 替换字符串字面量
      .trim()
      .toLowerCase();
  }

  /**
   * 生成查询ID
   */
  private generateQueryId(): string {
    return `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成警报ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    // 每分钟检查一次性能指标
    this.monitoringTimer = setInterval(() => {
      this.performPerformanceCheck();
    }, 60000);

    // 每小时清理一次过期数据
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, 3600000);

    this.logger.log('查询性能监控已启动');
  }

  /**
   * 执行性能检查
   */
  private performPerformanceCheck(): void {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // 检查活跃查询是否有超时
    for (const [queryId, queryExecution] of this.activeQueries.entries()) {
      if (queryExecution.startTime < oneMinuteAgo) {
        this.createAlert({
          type: 'connection_timeout',
          level: 'warning',
          message: `查询执行超时: ${queryId}`,
          queryExecution
        });
      }
    }

    // 生成性能报告
    const report = this.generatePerformanceReport();
    this.emit('performanceReport', report);
  }

  /**
   * 执行清理
   */
  private performCleanup(): void {
    const oneWeekAgo = Date.now() - 7 * 24 * 3600000;

    // 清理过期的查询历史
    this.queryHistory = this.queryHistory.filter(
      query => query.startTime > oneWeekAgo
    );

    // 清理过期的警报
    this.performanceAlerts = this.performanceAlerts.filter(
      alert => alert.timestamp > oneWeekAgo
    );

    this.logger.debug('执行了性能监控数据清理');
  }

  /**
   * 清理旧统计数据
   */
  private cleanupOldStatistics(): void {
    // 按最后执行时间排序，删除最旧的统计
    const sortedStats = Array.from(this.queryStatistics.entries())
      .sort((a, b) => a[1].lastExecution - b[1].lastExecution);

    const toDelete = sortedStats.slice(0, Math.floor(this.maxStatisticsSize * 0.1));
    for (const [sqlPattern] of toDelete) {
      this.queryStatistics.delete(sqlPattern);
    }
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(): any {
    const now = Date.now();
    const oneHourAgo = now - 3600000;

    const recentQueries = this.queryHistory.filter(
      query => query.startTime > oneHourAgo
    );

    const slowQueries = recentQueries.filter(
      query => query.duration && query.duration > this.slowQueryThreshold
    );

    const errorQueries = recentQueries.filter(
      query => !query.success
    );

    return {
      timestamp: now,
      summary: {
        totalQueries: recentQueries.length,
        slowQueries: slowQueries.length,
        errorQueries: errorQueries.length,
        averageExecutionTime: recentQueries.length > 0
          ? recentQueries.reduce((sum, q) => sum + (q.duration || 0), 0) / recentQueries.length
          : 0,
        activeQueries: this.activeQueries.size
      },
      topSlowQueries: this.getTopSlowQueries(10),
      topErrorQueries: this.getTopErrorQueries(10),
      recentAlerts: this.getRecentAlerts(10)
    };
  }

  /**
   * 获取最慢查询
   */
  public getTopSlowQueries(limit: number = 10): QueryStatistics[] {
    return Array.from(this.queryStatistics.values())
      .sort((a, b) => b.averageDuration - a.averageDuration)
      .slice(0, limit);
  }

  /**
   * 获取错误最多的查询
   */
  public getTopErrorQueries(limit: number = 10): QueryStatistics[] {
    return Array.from(this.queryStatistics.values())
      .filter(stats => stats.errorCount > 0)
      .sort((a, b) => b.errorCount - a.errorCount)
      .slice(0, limit);
  }

  /**
   * 获取最近的警报
   */
  public getRecentAlerts(limit: number = 10): PerformanceAlert[] {
    return this.performanceAlerts
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * 确认警报
   */
  public acknowledgeAlert(alertId: string): boolean {
    const alert = this.performanceAlerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * 获取查询统计
   */
  public getQueryStatistics(): QueryStatistics[] {
    return Array.from(this.queryStatistics.values());
  }

  /**
   * 获取查询历史
   */
  public getQueryHistory(limit?: number): QueryExecution[] {
    const history = [...this.queryHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * 销毁监控器
   */
  public destroy(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.removeAllListeners();
    this.logger.log('查询性能监控器已销毁');
  }
}
