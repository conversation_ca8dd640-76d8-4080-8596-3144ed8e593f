/**
 * 资产浏览器服务Hook
 * 管理资产的加载、缓存、搜索、过滤和排序
 */
import { useState, useEffect, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';
import { Asset, AssetType } from '../store/asset/assetSlice';
import { ViewMode, SortType, SortDirection, AssetFilter } from '../components/assets/EnhancedAssetBrowser';

/**
 * 预览缓存项
 */
interface PreviewCacheItem {
  url: string;
  blob?: Blob;
  timestamp: number;
  loading: boolean;
}

/**
 * 资产浏览器服务Hook
 */
export const useAssetBrowserService = (projectId?: string, folderId?: string) => {
  // 状态管理
  const [assets, setAssets] = useState<Asset[]>([]);
  const [selectedAssetIds, setSelectedAssetIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<AssetFilter>({
    types: [],
    sizeRange: [0, Infinity],
    dateRange: ['', ''],
    tags: []
  });
  const [sortType, setSortType] = useState<SortType>(SortType.NAME);
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.ASC);
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.GRID);
  const [previewCache, setPreviewCache] = useState<Map<string, PreviewCacheItem>>(new Map());

  /**
   * 过滤和排序资产
   */
  const filteredAssets = useMemo(() => {
    let filtered = [...assets];

    // 按搜索词过滤
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(asset =>
        asset.name.toLowerCase().includes(lowerSearchTerm) ||
        asset.type.toLowerCase().includes(lowerSearchTerm) ||
        (asset.metadata?.tags || []).some((tag: string) => 
          tag.toLowerCase().includes(lowerSearchTerm)
        )
      );
    }

    // 按类型过滤
    if (filter.types.length > 0) {
      filtered = filtered.filter(asset => filter.types.includes(asset.type));
    }

    // 按大小过滤
    if (filter.sizeRange[0] > 0 || filter.sizeRange[1] < Infinity) {
      filtered = filtered.filter(asset => {
        const size = asset.metadata?.size || 0;
        return size >= filter.sizeRange[0] && size <= filter.sizeRange[1];
      });
    }

    // 按标签过滤
    if (filter.tags.length > 0) {
      filtered = filtered.filter(asset => {
        const assetTags = asset.metadata?.tags || [];
        return filter.tags.some(tag => assetTags.includes(tag));
      });
    }

    // 排序
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortType) {
        case SortType.NAME:
          comparison = a.name.localeCompare(b.name);
          break;
        case SortType.TYPE:
          comparison = a.type.localeCompare(b.type);
          break;
        case SortType.SIZE:
          comparison = (a.metadata?.size || 0) - (b.metadata?.size || 0);
          break;
        case SortType.DATE:
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
          break;
      }

      return sortDirection === SortDirection.ASC ? comparison : -comparison;
    });

    return filtered;
  }, [assets, searchTerm, filter, sortType, sortDirection]);

  /**
   * 加载资产列表
   */
  const loadAssets = useCallback(async () => {
    if (!projectId) return;

    setIsLoading(true);
    setLoadingProgress(0);

    try {
      // 模拟API调用
      const response = await fetch(`/api/projects/${projectId}/assets${folderId ? `?folderId=${folderId}` : ''}`);
      const data = await response.json();

      // 模拟加载进度
      for (let i = 0; i <= 100; i += 10) {
        setLoadingProgress(i);
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      setAssets(data.assets || []);
    } catch (error) {
      console.error('Failed to load assets:', error);
      setAssets([]);
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
    }
  }, [projectId, folderId]);

  /**
   * 选择资产
   */
  const selectAsset = useCallback((assetId: string) => {
    setSelectedAssetIds([assetId]);
  }, []);

  /**
   * 多选资产
   */
  const selectMultipleAssets = useCallback((assetIds: string[]) => {
    setSelectedAssetIds(prev => {
      const newSelection = [...prev];
      assetIds.forEach(id => {
        const index = newSelection.indexOf(id);
        if (index > -1) {
          newSelection.splice(index, 1);
        } else {
          newSelection.push(id);
        }
      });
      return newSelection;
    });
  }, []);

  /**
   * 清除选择
   */
  const clearSelection = useCallback(() => {
    setSelectedAssetIds([]);
  }, []);

  /**
   * 刷新资产
   */
  const refreshAssets = useCallback(() => {
    loadAssets();
  }, [loadAssets]);

  /**
   * 预加载缩略图
   */
  const preloadThumbnails = useCallback(async (assetIds: string[]) => {
    const loadPromises = assetIds.map(async (assetId) => {
      if (previewCache.has(assetId)) return;

      const asset = assets.find(a => a.id === assetId);
      if (!asset || !asset.thumbnail) return;

      try {
        setPreviewCache(prev => new Map(prev).set(assetId, {
          url: asset.thumbnail!,
          timestamp: Date.now(),
          loading: true
        }));

        const response = await fetch(asset.thumbnail);
        const blob = await response.blob();

        setPreviewCache(prev => {
          const newCache = new Map(prev);
          const item = newCache.get(assetId);
          if (item) {
            newCache.set(assetId, {
              ...item,
              blob,
              loading: false
            });
          }
          return newCache;
        });
      } catch (error) {
        console.error(`Failed to preload thumbnail for asset ${assetId}:`, error);
        setPreviewCache(prev => {
          const newCache = new Map(prev);
          newCache.delete(assetId);
          return newCache;
        });
      }
    });

    await Promise.all(loadPromises);
  }, [assets, previewCache]);

  /**
   * 获取资产预览
   */
  const getAssetPreview = useCallback((assetId: string): string | null => {
    const cached = previewCache.get(assetId);
    if (cached?.blob) {
      return URL.createObjectURL(cached.blob);
    }

    const asset = assets.find(a => a.id === assetId);
    return asset?.thumbnail || null;
  }, [previewCache, assets]);

  /**
   * 清理过期缓存
   */
  const cleanupCache = useCallback(() => {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10分钟

    setPreviewCache(prev => {
      const newCache = new Map();
      prev.forEach((item, key) => {
        if (now - item.timestamp < maxAge) {
          newCache.set(key, item);
        } else if (item.blob) {
          // 释放blob URL
          URL.revokeObjectURL(URL.createObjectURL(item.blob));
        }
      });
      return newCache;
    });
  }, []);

  /**
   * 批量操作资产
   */
  const batchOperateAssets = useCallback(async (
    operation: 'delete' | 'move' | 'copy',
    assetIds: string[],
    targetFolderId?: string
  ) => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/assets/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          operation,
          assetIds,
          targetFolderId
        })
      });

      if (!response.ok) {
        throw new Error('Batch operation failed');
      }

      // 刷新资产列表
      await loadAssets();
      
      // 清除选择
      clearSelection();
    } catch (error) {
      console.error('Batch operation failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [loadAssets, clearSelection]);

  /**
   * 上传资产
   */
  const uploadAssets = useCallback(async (files: File[], targetFolderId?: string) => {
    setIsLoading(true);
    setLoadingProgress(0);

    try {
      const formData = new FormData();
      files.forEach(file => formData.append('files', file));
      if (targetFolderId) {
        formData.append('folderId', targetFolderId);
      }
      if (projectId) {
        formData.append('projectId', projectId);
      }

      const response = await fetch('/api/assets/upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      // 刷新资产列表
      await loadAssets();
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
    }
  }, [projectId, loadAssets]);

  /**
   * 防抖的搜索设置
   */
  const debouncedSetSearchTerm = useMemo(
    () => debounce((term: string) => {
      setSearchTerm(term);
    }, 300),
    []
  );

  // 初始加载
  useEffect(() => {
    if (projectId) {
      loadAssets();
    }
  }, [projectId, folderId, loadAssets]);

  // 预加载可见资产的缩略图
  useEffect(() => {
    const visibleAssetIds = filteredAssets.slice(0, 20).map(asset => asset.id);
    if (visibleAssetIds.length > 0) {
      preloadThumbnails(visibleAssetIds);
    }
  }, [filteredAssets, preloadThumbnails]);

  // 定期清理缓存
  useEffect(() => {
    const interval = setInterval(cleanupCache, 5 * 60 * 1000); // 每5分钟清理一次
    return () => clearInterval(interval);
  }, [cleanupCache]);

  return {
    // 状态
    assets,
    filteredAssets,
    selectedAssetIds,
    isLoading,
    loadingProgress,
    searchTerm,
    filter,
    sortType,
    sortDirection,
    viewMode,
    previewCache,

    // 方法
    setSearchTerm: debouncedSetSearchTerm,
    setFilter,
    setSortType,
    setSortDirection,
    setViewMode,
    selectAsset,
    selectMultipleAssets,
    clearSelection,
    refreshAssets,
    preloadThumbnails,
    getAssetPreview,
    batchOperateAssets,
    uploadAssets,
    cleanupCache
  };
};
