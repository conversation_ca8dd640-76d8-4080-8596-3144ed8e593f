/**
 * 消息重试机制服务
 * 实现智能重试策略、死信队列处理和重试统计分析
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';
import { Job } from 'bull';

/**
 * 重试策略枚举
 */
export enum RetryStrategy {
  FIXED = 'fixed', // 固定间隔
  EXPONENTIAL = 'exponential', // 指数退避
  LINEAR = 'linear', // 线性增长
  CUSTOM = 'custom' // 自定义策略
}

/**
 * 重试配置
 */
export interface RetryConfig {
  /** 最大重试次数 */
  maxAttempts: number;
  /** 重试策略 */
  strategy: RetryStrategy;
  /** 基础延迟时间（毫秒） */
  baseDelay: number;
  /** 最大延迟时间（毫秒） */
  maxDelay: number;
  /** 退避倍数（用于指数退避） */
  backoffMultiplier: number;
  /** 抖动因子（0-1，用于避免雷群效应） */
  jitterFactor: number;
  /** 自定义延迟计算函数 */
  customDelayFunction?: (attempt: number, error: Error) => number;
}

/**
 * 重试记录
 */
export interface RetryRecord {
  /** 记录ID */
  id: string;
  /** 任务ID */
  jobId: string;
  /** 队列名称 */
  queueName: string;
  /** 原始数据 */
  originalData: any;
  /** 重试次数 */
  attemptCount: number;
  /** 最大重试次数 */
  maxAttempts: number;
  /** 错误历史 */
  errorHistory: Array<{
    attempt: number;
    error: string;
    timestamp: number;
    delay: number;
  }>;
  /** 下次重试时间 */
  nextRetryAt: number;
  /** 创建时间 */
  createdAt: number;
  /** 最后更新时间 */
  updatedAt: number;
  /** 状态 */
  status: 'pending' | 'retrying' | 'exhausted' | 'succeeded';
}

/**
 * 死信消息
 */
export interface DeadLetterMessage {
  /** 消息ID */
  id: string;
  /** 原始任务ID */
  originalJobId: string;
  /** 队列名称 */
  queueName: string;
  /** 消息数据 */
  data: any;
  /** 失败原因 */
  failureReason: string;
  /** 重试历史 */
  retryHistory: RetryRecord;
  /** 进入死信队列时间 */
  deadLetterAt: number;
  /** 是否可恢复 */
  recoverable: boolean;
}

/**
 * 重试统计
 */
export interface RetryStats {
  /** 队列名称 */
  queueName: string;
  /** 总重试次数 */
  totalRetries: number;
  /** 成功重试次数 */
  successfulRetries: number;
  /** 失败重试次数 */
  failedRetries: number;
  /** 重试成功率 */
  retrySuccessRate: number;
  /** 平均重试次数 */
  averageRetryCount: number;
  /** 死信消息数量 */
  deadLetterCount: number;
  /** 最常见错误类型 */
  commonErrors: Array<{ error: string; count: number }>;
  /** 最后更新时间 */
  lastUpdated: number;
}

/**
 * 消息重试机制服务
 */
@Injectable()
export class MessageRetryService extends EventEmitter {
  private readonly logger = new Logger(MessageRetryService.name);

  /** 重试记录映射 */
  private retryRecords: Map<string, RetryRecord> = new Map();
  /** 死信消息映射 */
  private deadLetterMessages: Map<string, DeadLetterMessage> = new Map();
  /** 队列重试配置映射 */
  private queueRetryConfigs: Map<string, RetryConfig> = new Map();
  /** 重试统计映射 */
  private retryStats: Map<string, RetryStats> = new Map();

  /** 默认重试配置 */
  private readonly defaultRetryConfig: RetryConfig;

  /** 重试处理定时器 */
  private retryProcessingTimer?: NodeJS.Timeout;
  /** 统计更新定时器 */
  private statsUpdateTimer?: NodeJS.Timeout;

  constructor(private readonly configService: ConfigService) {
    super();

    this.defaultRetryConfig = {
      maxAttempts: this.configService.get<number>('QUEUE_DEFAULT_MAX_ATTEMPTS', 3),
      strategy: this.configService.get<RetryStrategy>('QUEUE_DEFAULT_RETRY_STRATEGY', RetryStrategy.EXPONENTIAL),
      baseDelay: this.configService.get<number>('QUEUE_DEFAULT_BASE_DELAY', 1000),
      maxDelay: this.configService.get<number>('QUEUE_DEFAULT_MAX_DELAY', 300000), // 5分钟
      backoffMultiplier: this.configService.get<number>('QUEUE_DEFAULT_BACKOFF_MULTIPLIER', 2),
      jitterFactor: this.configService.get<number>('QUEUE_DEFAULT_JITTER_FACTOR', 0.1)
    };

    this.startRetryProcessing();
    this.startStatsUpdate();
  }

  /**
   * 设置队列重试配置
   */
  public setQueueRetryConfig(queueName: string, config: Partial<RetryConfig>): void {
    const fullConfig: RetryConfig = {
      ...this.defaultRetryConfig,
      ...config
    };

    this.queueRetryConfigs.set(queueName, fullConfig);
    this.logger.log(`设置队列 ${queueName} 重试配置`);
  }

  /**
   * 获取队列重试配置
   */
  public getQueueRetryConfig(queueName: string): RetryConfig {
    return this.queueRetryConfigs.get(queueName) || this.defaultRetryConfig;
  }

  /**
   * 处理任务失败
   */
  public async handleJobFailure(
    job: Job,
    error: Error,
    queueName: string
  ): Promise<boolean> {
    const config = this.getQueueRetryConfig(queueName);
    const recordId = this.generateRetryRecordId(job.id.toString(), queueName);
    
    let retryRecord = this.retryRecords.get(recordId);
    
    if (!retryRecord) {
      // 创建新的重试记录
      retryRecord = {
        id: recordId,
        jobId: job.id.toString(),
        queueName,
        originalData: job.data,
        attemptCount: 0,
        maxAttempts: config.maxAttempts,
        errorHistory: [],
        nextRetryAt: 0,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        status: 'pending'
      };
      this.retryRecords.set(recordId, retryRecord);
    }

    // 更新重试记录
    retryRecord.attemptCount++;
    retryRecord.updatedAt = Date.now();
    
    // 计算下次重试延迟
    const delay = this.calculateRetryDelay(retryRecord.attemptCount, config, error);
    retryRecord.nextRetryAt = Date.now() + delay;

    // 添加错误历史
    retryRecord.errorHistory.push({
      attempt: retryRecord.attemptCount,
      error: error.message,
      timestamp: Date.now(),
      delay
    });

    // 检查是否超过最大重试次数
    if (retryRecord.attemptCount >= config.maxAttempts) {
      retryRecord.status = 'exhausted';
      await this.moveToDeadLetter(retryRecord, error);
      this.logger.warn(`任务 ${job.id} 重试次数已用尽，移入死信队列`);
      return false;
    }

    retryRecord.status = 'retrying';
    this.updateRetryStats(queueName, false);
    
    this.logger.debug(`任务 ${job.id} 将在 ${delay}ms 后重试（第 ${retryRecord.attemptCount} 次）`);
    this.emit('jobScheduledForRetry', { job, retryRecord, delay });

    return true;
  }

  /**
   * 处理任务成功
   */
  public handleJobSuccess(job: Job, queueName: string): void {
    const recordId = this.generateRetryRecordId(job.id.toString(), queueName);
    const retryRecord = this.retryRecords.get(recordId);

    if (retryRecord) {
      retryRecord.status = 'succeeded';
      retryRecord.updatedAt = Date.now();
      
      // 如果是重试成功，更新统计
      if (retryRecord.attemptCount > 0) {
        this.updateRetryStats(queueName, true);
        this.logger.debug(`任务 ${job.id} 重试成功（共重试 ${retryRecord.attemptCount} 次）`);
        this.emit('jobRetrySucceeded', { job, retryRecord });
      }

      // 清理重试记录
      setTimeout(() => {
        this.retryRecords.delete(recordId);
      }, 3600000); // 1小时后清理
    }
  }

  /**
   * 计算重试延迟
   */
  private calculateRetryDelay(
    attempt: number,
    config: RetryConfig,
    error: Error
  ): number {
    let delay: number;

    switch (config.strategy) {
      case RetryStrategy.FIXED:
        delay = config.baseDelay;
        break;

      case RetryStrategy.EXPONENTIAL:
        delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
        break;

      case RetryStrategy.LINEAR:
        delay = config.baseDelay * attempt;
        break;

      case RetryStrategy.CUSTOM:
        if (config.customDelayFunction) {
          delay = config.customDelayFunction(attempt, error);
        } else {
          delay = config.baseDelay;
        }
        break;

      default:
        delay = config.baseDelay;
    }

    // 应用最大延迟限制
    delay = Math.min(delay, config.maxDelay);

    // 应用抖动
    if (config.jitterFactor > 0) {
      const jitter = delay * config.jitterFactor * (Math.random() - 0.5);
      delay += jitter;
    }

    return Math.max(delay, 0);
  }

  /**
   * 移动到死信队列
   */
  private async moveToDeadLetter(retryRecord: RetryRecord, error: Error): Promise<void> {
    const deadLetterId = this.generateDeadLetterId();
    const deadLetterMessage: DeadLetterMessage = {
      id: deadLetterId,
      originalJobId: retryRecord.jobId,
      queueName: retryRecord.queueName,
      data: retryRecord.originalData,
      failureReason: error.message,
      retryHistory: retryRecord,
      deadLetterAt: Date.now(),
      recoverable: this.isRecoverable(error)
    };

    this.deadLetterMessages.set(deadLetterId, deadLetterMessage);
    this.updateDeadLetterStats(retryRecord.queueName);

    this.emit('messageMovedToDeadLetter', deadLetterMessage);
  }

  /**
   * 判断错误是否可恢复
   */
  private isRecoverable(error: Error): boolean {
    const nonRecoverableErrors = [
      'ValidationError',
      'AuthenticationError',
      'AuthorizationError',
      'BadRequestError'
    ];

    return !nonRecoverableErrors.some(errorType => 
      error.constructor.name === errorType || error.message.includes(errorType)
    );
  }

  /**
   * 开始重试处理
   */
  private startRetryProcessing(): void {
    this.retryProcessingTimer = setInterval(() => {
      this.processRetryQueue();
    }, 5000); // 每5秒检查一次
  }

  /**
   * 处理重试队列
   */
  private async processRetryQueue(): Promise<void> {
    const now = Date.now();
    const readyForRetry: RetryRecord[] = [];

    // 查找准备重试的记录
    for (const record of this.retryRecords.values()) {
      if (record.status === 'retrying' && record.nextRetryAt <= now) {
        readyForRetry.push(record);
      }
    }

    // 处理重试
    for (const record of readyForRetry) {
      try {
        await this.executeRetry(record);
      } catch (error) {
        this.logger.error(`重试执行失败: ${record.jobId}`, error);
      }
    }
  }

  /**
   * 执行重试
   */
  private async executeRetry(record: RetryRecord): Promise<void> {
    this.emit('retryExecuting', record);
    
    // 这里应该重新将任务添加到队列
    // 具体实现取决于队列管理器的接口
    this.logger.debug(`执行重试: ${record.jobId} (第 ${record.attemptCount} 次)`);
  }

  /**
   * 更新重试统计
   */
  private updateRetryStats(queueName: string, success: boolean): void {
    let stats = this.retryStats.get(queueName);
    
    if (!stats) {
      stats = {
        queueName,
        totalRetries: 0,
        successfulRetries: 0,
        failedRetries: 0,
        retrySuccessRate: 0,
        averageRetryCount: 0,
        deadLetterCount: 0,
        commonErrors: [],
        lastUpdated: Date.now()
      };
      this.retryStats.set(queueName, stats);
    }

    stats.totalRetries++;
    if (success) {
      stats.successfulRetries++;
    } else {
      stats.failedRetries++;
    }

    stats.retrySuccessRate = stats.successfulRetries / stats.totalRetries;
    stats.lastUpdated = Date.now();
  }

  /**
   * 更新死信统计
   */
  private updateDeadLetterStats(queueName: string): void {
    const stats = this.retryStats.get(queueName);
    if (stats) {
      stats.deadLetterCount++;
    }
  }

  /**
   * 开始统计更新
   */
  private startStatsUpdate(): void {
    this.statsUpdateTimer = setInterval(() => {
      this.updateAllStats();
    }, 60000); // 每分钟更新一次
  }

  /**
   * 更新所有统计
   */
  private updateAllStats(): void {
    // 计算平均重试次数
    for (const stats of this.retryStats.values()) {
      const retryRecords = Array.from(this.retryRecords.values())
        .filter(record => record.queueName === stats.queueName);
      
      if (retryRecords.length > 0) {
        const totalAttempts = retryRecords.reduce((sum, record) => sum + record.attemptCount, 0);
        stats.averageRetryCount = totalAttempts / retryRecords.length;
      }
    }

    this.emit('statsUpdated', Array.from(this.retryStats.values()));
  }

  /**
   * 恢复死信消息
   */
  public async recoverDeadLetterMessage(deadLetterId: string): Promise<boolean> {
    const deadLetterMessage = this.deadLetterMessages.get(deadLetterId);
    if (!deadLetterMessage || !deadLetterMessage.recoverable) {
      return false;
    }

    try {
      // 重新创建重试记录
      const retryRecord: RetryRecord = {
        ...deadLetterMessage.retryHistory,
        id: this.generateRetryRecordId(deadLetterMessage.originalJobId, deadLetterMessage.queueName),
        attemptCount: 0, // 重置重试次数
        status: 'pending',
        nextRetryAt: Date.now(),
        updatedAt: Date.now()
      };

      this.retryRecords.set(retryRecord.id, retryRecord);
      this.deadLetterMessages.delete(deadLetterId);

      this.logger.log(`死信消息 ${deadLetterId} 已恢复`);
      this.emit('deadLetterMessageRecovered', { deadLetterId, retryRecord });

      return true;
    } catch (error) {
      this.logger.error(`恢复死信消息失败: ${deadLetterId}`, error);
      return false;
    }
  }

  /**
   * 获取重试统计
   */
  public getRetryStats(queueName?: string): RetryStats[] {
    if (queueName) {
      const stats = this.retryStats.get(queueName);
      return stats ? [stats] : [];
    }
    return Array.from(this.retryStats.values());
  }

  /**
   * 获取死信消息
   */
  public getDeadLetterMessages(queueName?: string): DeadLetterMessage[] {
    const messages = Array.from(this.deadLetterMessages.values());
    return queueName ? messages.filter(msg => msg.queueName === queueName) : messages;
  }

  /**
   * 获取重试记录
   */
  public getRetryRecords(queueName?: string): RetryRecord[] {
    const records = Array.from(this.retryRecords.values());
    return queueName ? records.filter(record => record.queueName === queueName) : records;
  }

  /**
   * 生成重试记录ID
   */
  private generateRetryRecordId(jobId: string, queueName: string): string {
    return `retry_${queueName}_${jobId}`;
  }

  /**
   * 生成死信ID
   */
  private generateDeadLetterId(): string {
    return `dl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清理过期记录
   */
  public cleanupExpiredRecords(): void {
    const now = Date.now();
    const expiredThreshold = now - 7 * 24 * 3600000; // 7天前

    // 清理过期的重试记录
    for (const [id, record] of this.retryRecords.entries()) {
      if (record.updatedAt < expiredThreshold && record.status !== 'retrying') {
        this.retryRecords.delete(id);
      }
    }

    // 清理过期的死信消息（可选，根据业务需求）
    // for (const [id, message] of this.deadLetterMessages.entries()) {
    //   if (message.deadLetterAt < expiredThreshold) {
    //     this.deadLetterMessages.delete(id);
    //   }
    // }

    this.logger.debug('清理过期重试记录完成');
  }

  /**
   * 销毁服务
   */
  public destroy(): void {
    if (this.retryProcessingTimer) {
      clearInterval(this.retryProcessingTimer);
    }
    if (this.statsUpdateTimer) {
      clearInterval(this.statsUpdateTimer);
    }

    this.removeAllListeners();
    this.logger.log('消息重试机制服务已销毁');
  }
}
